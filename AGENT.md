# Android Development Instructions

This project supports Android development in both online and offline environments.

## CRITICAL: Project Structure Guidelines

**MANDATORY REQUIREMENT**: ALL guidelines and patterns defined in `/memory-bank/ANDROID_PROJECT_STRUCTURE.md` MUST be followed WITHOUT EXCUSE. This includes:
- Clean Architecture + MVVM pattern
- NO when expressions - use sealed classes with polymorphism
- NO hardcoded strings in UI - use stringResource()
- DRY, SOLID principles, and Hilt dependency injection
- Feature-based organization and separation of concerns

Failure to follow these guidelines is unacceptable.

## Prerequisites

- Java JDK 17 or higher must be installed
- Android SDK must be installed and configured
- Basic shell environment

## Build Commands

### Build Commands
```bash
# Standard build
./gradlew assembleDebug

# Clean and build
./gradlew clean assembleDebug

# Build with stacktrace for debugging
./gradlew assembleDebug --stacktrace
```

## ⚠️ MANDATORY Code Validation

**CRITICAL REQUIREMENT**: After making ANY code changes, you MUST validate that the code compiles and builds correctly before considering your work complete.

### Required Validation Commands

**Quick validation (recommended):**
```bash
./gradlew assembleDebug
```

**Full validation with clean:**
```bash
./gradlew clean assembleDebug
```

### Why This Validation is Required

1. **Ensures buildability**: Verifies your changes don't break compilation
2. **Catches syntax errors**: Finds Kotlin/Java syntax issues immediately  
3. **Validates dependencies**: Confirms all imports and dependencies are correct
4. **APK generation**: Ensures the final Android application can be built
5. **Dependency resolution**: Ensures all required dependencies are available

### Validation Workflow

**Standard Workflow (recommended):**
```bash
# 1. Make your code changes
# 2. Run validation
./gradlew assembleDebug

# 3. Check the result:
# ✅ BUILD SUCCESSFUL - your changes are valid
# ❌ BUILD FAILED - fix errors before proceeding
```

**Clean Build Workflow (for complex changes):**
```bash
# 1. Make your code changes
# 2. Run clean build validation
./gradlew clean assembleDebug

# 3. Check the result:
# ✅ BUILD SUCCESSFUL - your changes are valid
# ❌ BUILD FAILED - fix errors before proceeding
```

### Alternative Validation Commands

For specific validation needs:

1. **Quick compilation check**:
   ```
   ./gradlew compileDebugKotlin
   ```

2. **Gradle configuration check**:
   ```
   ./gradlew tasks
   ```

3. **Run unit tests**:
   ```bash
   ./gradlew test
   ```

4. **Check dependencies**:
   ```bash
   ./gradlew dependencies
   ```

### Expected Behavior

✅ **Successful validation shows:**
- `BUILD SUCCESSFUL` message
- No compilation errors
- APK file generated successfully
- All Gradle tasks complete without issues

❌ **Failed validation shows:**
- `BUILD FAILED` message  
- Specific error details
- Line numbers and file locations of issues

**DO NOT proceed with additional changes if validation fails. Fix all errors first.**

## Troubleshooting

### Common Build Issues

**Gradle Daemon Issues:**
```bash
# Stop all gradle daemons
./gradlew --stop

# Run build without daemon
./gradlew assembleDebug --no-daemon
```

**Dependency Resolution Issues:**
```bash
# Clear gradle cache
./gradlew clean

# Refresh dependencies
./gradlew --refresh-dependencies assembleDebug
```

**Android SDK Not Found:**
```bash
# Check if ANDROID_HOME is set
echo $ANDROID_HOME

# Verify local.properties exists
cat local.properties
```

**Out of Memory Errors:**
```bash
# Increase gradle memory
export GRADLE_OPTS="-Xmx2048m -XX:MaxPermSize=512m"
./gradlew assembleDebug
```

### If Build Validation Fails

1. **Check error messages carefully**:
   - Read the specific error details
   - Note file names and line numbers
   - Fix syntax or dependency issues

2. **Common solutions**:
   ```bash
   # Fix permissions
   chmod +x gradlew
   
   # Clean and rebuild
   ./gradlew clean assembleDebug
   ```

### Build Requirements

- **Validation mandatory**: Every code change must pass `./gradlew assembleDebug`
- **Network access**: Gradle requires internet access to download dependencies
- **Error-free code**: Build failures must be resolved before proceeding with development

### Development Workflow Summary

1. Make code changes
2. **ALWAYS validate**: `./gradlew assembleDebug`
3. Fix any build errors before continuing
4. Repeat steps 1-3 for each change

**Remember: A successful build validation is REQUIRED after every code modification.**