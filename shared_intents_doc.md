Sveobuhvatan vodič za testiranje deljenih Intenata u nativnim Android aplikacijamaOdeljak 1: Osnove rukovanja deljenim Intentima u AndroiduOvaj odeljak postavlja ključne temelje, osiguravajući duboko razumevanje mehanizama koji stoje iza primanja deljenih podataka. Dekonstruisaćemo ceo proces, od akcije deljenja u spoljnoj aplikaciji do dolaska podataka u vašu aktivnost, pružajući objašnjenje "zašto" iza "kako".1.1. Anatomija deljenog IntentaU srcu Androidovog okvira za deljenje nalaze se Intent objekti. Oni su apstraktni opisi operacije koja se treba izvršiti. Kada korisnik želi da podeli sadržaj iz jedne aplikacije u drugu, sistem koristi Intent da bi posredovao u ovoj komunikaciji. Dve primarne akcije koje se koriste za deljenje sadržaja su Intent.ACTION_SEND i Intent.ACTION_SEND_MULTIPLE.Intent.ACTION_SEND: Ova akcija se koristi kada se deli jedan komad sadržaja. To može biti tekst, slika, video ili bilo koji drugi tip datoteke. Sadržaj se prenosi putem Intent "extras", što su podaci u paru ključ-vrednost. Najčešći extras za ACTION_SEND su 1:Intent.EXTRA_TEXT: Koristi se za deljenje običnog teksta, kao što je URL adresa ili poruka. Ovo je String.Intent.EXTRA_SUBJECT: Koristi se za deljenje naslova, često kao naslov e-pošte. Ovo je String.Intent.EXTRA_STREAM: Koristi se za deljenje binarnih podataka, kao što su slike, video zapisi ili dokumenti. Vrednost je Uri koji ukazuje na lokaciju podataka.Intent.ACTION_SEND_MULTIPLE: Ova akcija se koristi kada se deli više delova sadržaja istovremeno, obično više slika ili datoteka. Umesto EXTRA_STREAM, koristi se Intent.EXTRA_STREAM sa ArrayList<Uri> koji sadrži URI-je za svaku datoteku.1Android sistem koristi kombinaciju akcije (npr. ACTION_SEND) i MIME (Multipurpose Internet Mail Extensions) tipa podataka da bi odredio koje aplikacije mogu da obrade zahtev. Kada aplikacija pokrene Intent za deljenje, sistem pretražuje sve instalirane aplikacije za one koje su deklarisale da mogu da rukuju datom akcijom i MIME tipom. Ove kvalifikovane aplikacije se zatim prikazuju korisniku u Android Sharesheet-u ili biraču namera (intent resolver), omogućavajući korisniku da izabere odredište za deljenje.11.2. Konfiguracija manifesta: Ulazna vrata vaše aplikacijeDa bi vaša aplikacija bila prikazana u Android Sharesheet-u, morate eksplicitno deklarisati njenu sposobnost da prima deljene podatke. To se radi u AndroidManifest.xml datoteci pomoću elementa <intent-filter>. Ovaj filter deluje kao javna objava sistemu o tome koje vrste Intent-a vaša aktivnost može da prihvati.1Pravilna konfiguracija <intent-filter>-a je apsolutno neophodna. Sastoji se od tri ključna dela:Deklaracija akcije (<action>): Morate navesti akcije koje vaša aktivnost podržava. Za primanje deljenog sadržaja, to će biti android.intent.action.SEND i/ili android.intent.action.SEND_MULTIPLE.Deklaracija kategorije (<category>): Obavezno je uključiti <category android:name="android.intent.category.DEFAULT" />. Bez ove kategorije, vaša aktivnost neće biti uzeta u obzir za implicitne Intent-e, što je česta greška koja dovodi do toga da se aplikacija ne pojavljuje u listi za deljenje.2 Kategorija <category android:name="android.intent.category.BROWSABLE" /> je takođe važna ako želite da primate linkove deljene direktno iz pregledača.Podaci i MIME tipovi (<data>): Ovo je najkritičniji deo filtera za deljeni sadržaj. Atribut android:mimeType specificira koje tipove podataka vaša aplikacija može da obradi. Možete biti veoma specifični ili opšti 1:Specifičan tip: android:mimeType="text/plain" prihvata samo običan tekst.Wildcard podtip: android:mimeType="image/*" prihvata bilo koji format slike (JPEG, PNG, GIF, itd.).Potpuno generički tip: android:mimeType="*/*" prihvata bilo koji tip podataka. Iako je ovo moguće, treba ga koristiti sa velikim oprezom jer otvara aplikaciju za potencijalno maliciozne ili neočekivane tipove datoteka.Evo primera <intent-filter>-a u AndroidManifest.xml za aktivnost koja može da primi jednu sliku bilo kog tipa ili običan tekst:XML<activity android:name=".ui.MyShareActivity" android:exported="true">
<intent-filter>
<action android:name="android.intent.action.SEND" />
<category android:name="android.intent.category.DEFAULT" />
<data android:mimeType="image/*" />
</intent-filter>
<intent-filter>
<action android:name="android.intent.action.SEND" />
<category android:name="android.intent.category.DEFAULT" />
<data android:mimeType="text/plain" />
</intent-filter>
</activity>
Važno je prepoznati da <intent-filter> nije samo konfiguracija; to je javni API ugovor koji vaša aplikacija sklapa sa celokupnim Android ekosistemom. Svaka druga aplikacija na uređaju može da postavi upit i vidi ovaj ugovor. Ova javna priroda ga inherentno čini primarnom metom za napade zasnovane na Intent-ima, kao što su presretanje (hijacking) i ubacivanje (injection).7 Previše permisivan filter, kao što je */*, bez robusne obrade sa druge strane, predstavlja otvoren poziv za neispravno formatirane podatke i zlonamerne Intent-e. Stoga, najbolja praksa nije samo "učiniti da radi", već "učiniti ga specifičnim". Programeri bi trebalo da budu što je moguće precizniji sa svojim MIME tipovima i da tretiraju sve podatke koji stižu kroz ovaj "javni API" kao nepouzdane.1.3. Životni ciklus podataka: Od deljenja do onCreateKada korisnik izabere vašu aplikaciju iz Sharesheet-a, Android sistem pokreće odgovarajuću aktivnost i isporučuje joj Intent objekat koji sadrži deljene podatke.1 Obrada ovih podataka se dešava unutar vaše aktivnosti.Rukovanje Intent-om u kodu:Unutar onCreate metode vaše aktivnosti, prvi korak je da preuzmete Intent koji je pokrenuo aktivnost pozivom getIntent(). Pre nego što pokušate da izvučete bilo kakve podatke, ključno je proveriti akciju i tip Intent-a. Ovo je važno jer ista aktivnost može biti pokrenuta sa različitih mesta, na primer, iz pokretača aplikacija (launcher), u kom slučaju Intent neće sadržati deljene podatke.1Kotlinoverride fun onCreate(savedInstanceState: Bundle?) {
super.onCreate(savedInstanceState)
setContentView(R.layout.activity_my_share)

    // Proverite da li je Intent došao od akcije deljenja
    if (intent?.action == Intent.ACTION_SEND && intent.type!= null) {
        when {
            intent.type!!.startsWith("text/") -> {
                handleSharedText(intent) // Obrada deljenog teksta
            }
            intent.type!!.startsWith("image/") -> {
                handleSharedImage(intent) // Obrada deljene slike
            }
        }
    } else {
        // Rukovanje slučajem kada je aktivnost pokrenuta na drugi način
    }
}

private fun handleSharedText(intent: Intent) {
intent.getStringExtra(Intent.EXTRA_TEXT)?.let { sharedText ->
// Prikazati tekst u EditText-u ili ga obraditi na drugi način
findViewById<EditText>(R.id.shared_text_input).setText(sharedText)
}
}

private fun handleSharedImage(intent: Intent) {
(intent.getParcelableExtra<Parcelable>(Intent.EXTRA_STREAM) as? Uri)?.let { imageUri ->
// Učitati sliku iz URI-ja u ImageView
// Oprez: Obrada binarnih podataka treba da se radi van glavne (UI) niti!
findViewById<ImageView>(R.id.shared_image_view).setImageURI(imageUri)
}
}
Zamka launchMode:Česta i često zanemarena zamka odnosi se na launchMode aktivnosti. Ako je aktivnost u manifestu deklarisana sa android:launchMode="singleTask" ili android:launchMode="singleTop", a instanca te aktivnosti već postoji u zadatku, novi Intent za deljenje neće pokrenuti onCreate. Umesto toga, Intent će biti isporučen postojećoj instanci putem onNewIntent(Intent) metode. Ako ne obradite ovaj slučaj, vaša aplikacija će ignorisati novi deljeni sadržaj i nastaviti da prikazuje stari.Da biste ovo ispravno rešili, morate predefinisati onNewIntent i unutar nje pozvati setIntent(intent). Ovo osigurava da će svaki sledeći poziv getIntent() (na primer, u onResume) vratiti najnoviji Intent sa novim podacima.9Kotlinoverride fun onNewIntent(intent: Intent?) {
super.onNewIntent(intent)
// Postavite novi intent kao trenutni za ovu aktivnost
setIntent(intent)
// Ponovo obradite intent, možda u onResume ili direktno ovde
// (pazite da ne duplirate logiku iz onCreate)
}

override fun onResume() {
super.onResume()
// Obrada intenta ovde osigurava da se rukuje i pri prvom pokretanju i pri novom intentu
if (intent?.action == Intent.ACTION_SEND) {
//... logika za obradu...
}
}
Odeljak 2: Strateški okvir za testiranje IntenataOvaj odeljak predstavlja strateški, višeslojni pristup testiranju, prelazeći sa jednostavne implementacije na profesionalnu metodologiju testiranja. Koristićemo model Piramide testiranja da bismo strukturirali našu strategiju.2.1. Piramida testiranja za deljene IntenteKlasična piramida testiranja je model koji vizualizuje idealan odnos između različitih vrsta testova u softverskom projektu. Prilagođena za testiranje deljenih Intent-a, ona pruža jasan putokaz za postizanje robusnosti i pouzdanosti.10Baza piramide (Jedinični testovi - Unit Tests): Ovo je najširi sloj piramide i sadrži veliki broj brzih, izolovanih testova. Za deljene Intent-e, ovi testovi proveravaju male, logičke delove koda, kao što su funkcije za parsiranje podataka iz Intent objekta. Oni se izvršavaju na lokalnoj JVM (Java Virtual Machine) i ne zahtevaju emulator ili fizički uređaj. Njihov cilj je da potvrde ispravnost logike u izolaciji.Srednji sloj (Integracioni testovi - Integration Tests): Ovaj sloj sadrži manji broj testova koji su nešto sporiji od jediničnih. Oni proveravaju interakciju između više komponenti. Na primer, kako Activity nakon primanja Intent-a komunicira sa ViewModel-om ili Repository-jem da bi sačuvao ili obradio podatke. Ovi testovi mogu da se izvršavaju na JVM-u pomoću alata kao što je Robolectric, ili kao mali instrumentacioni testovi na uređaju.Vrh piramide (End-to-End testovi - E2E Tests): Na vrhu se nalazi mali broj sveobuhvatnih, ali sporih testova. Oni validiraju ceo korisnički tok, od akcije deljenja u spoljnoj aplikaciji, preko interakcije sa Android Sharesheet-om, do konačnog rezultata u korisničkom interfejsu vaše aplikacije. Ovi testovi su neophodni za potvrdu da ceo sistem radi kako treba, ali su skupi za pisanje i održavanje, i moraju se izvršavati na stvarnom uređaju ili emulatoru.2.2. Lokalni vs. Instrumentacioni testovi: Izbor bojnog poljaU Android razvoju, testovi se organizuju u dva glavna direktorijuma, što definiše njihovo okruženje za izvršavanje 11:Lokalni testovi (/test): Ovi testovi se izvršavaju na vašoj razvojnoj mašini, unutar JVM-a. Oni su izuzetno brzi jer ne zahtevaju pokretanje Android sistema. Idealni su za jedinične testove čiste logike koja nema zavisnosti od Android framework-a (npr. klase Context, Intent).Instrumentacioni testovi (/androidTest): Ovi testovi se izvršavaju na fizičkom Android uređaju ili emulatoru. Oni imaju pristup punom Android framework-u i mogu da interaguju sa UI-jem, senzorima i drugim sistemskim servisima. Iako pružaju najviši nivo vernosti, znatno su sporiji od lokalnih testova. Koriste se za integracione i E2E testove.Alat Robolectric premošćava ovaj jaz. On omogućava izvršavanje testova koji imaju zavisnosti od Android framework-a (kao što je rad sa Intent objektima) na lokalnoj JVM, simulirajući Android okruženje. Ovo pruža značajnu prednost u brzini u poređenju sa instrumentacionim testovima, čineći ga odličnim izborom za integracione testove srednjeg sloja piramide.122.3. Tabela 1: Matrica za izbor alata za testiranjeIzbor pravog alata za pravi zadatak je ključan za efikasnu strategiju testiranja. Sledeća tabela pruža pregled glavnih alata i njihovu primenu u kontekstu testiranja deljenih Intent-a, sumirajući informacije iz brojnih izvora.12AlatIzvršno okruženjeBrzinaOpsegPrimarna upotreba za testiranje IntenataJUnit + MockKJVMVeoma brzoJedna klasa/funkcijaTestiranje logike parsiranja Intent podataka u izolaciji. Mock-ovanje Intent objekta za proveru ekstrakcije extras-a i URI-ja.RobolectricJVMBrzoUnutar aplikacijeSimulacija pokretanja Activity-ja sa specifičnim Intent-om. Provera da li Activity ispravno pokreće novi Intent kao odgovor na deljene podatke (nextStartedActivity).EspressoUređaj/EmulatorSrednjeUnutar aplikacijePokretanje Activity-ja sa prilagođenim Intent-om i provera da li se UI (npr. EditText, ImageView) ažurira ispravno. Validacija odlaznih Intent-a pomoću Espresso-Intents.UI AutomatorUređaj/EmulatorSporoViše aplikacija/SistemTestiranje kompletnog E2E toka: otvaranje druge aplikacije, deljenje sadržaja, odabir vaše aplikacije u sistemskom Sharesheet-u i provera rezultata.Odeljak 3: Manuelna i komandno-linijska validacija sa ADB-omPre nego što se napišu formalni automatizovani testovi, Android Debug Bridge (ADB) nudi moćan set alata za brzu, iterativnu validaciju i debagovanje rukovanja Intent-ima. Ovo je neprocenjiv alat za svakodnevni razvoj.3.1. Simulacija startActivity sa Activity Manager-om (am)Activity Manager (am) je ADB alatka koja omogućava izvršavanje različitih sistemskih akcija, uključujući pokretanje komponenti aplikacije. Korišćenjem komande adb shell am start, možete direktno pozvati aktivnost sa specifičnim Intent-om, zaobilazeći potrebu za interakcijom sa drugom aplikacijom.18Osnovna sintaksa je:adb shell am start [options] <INTENT>Ključne opcije za testiranje deljenih Intent-a su:-a <ACTION>: Specificira akciju Intent-a. Na primer, -a android.intent.action.SEND.21-t <MIME_TYPE>: Specificira MIME tip podataka. Na primer, -t "text/plain" ili -t "image/jpeg".22-d <DATA_URI>: Specificira URI podataka. Korisno za testiranje deljenja datoteka. Na primer, -d "file:///sdcard/image.jpg".23-n <COMPONENT>: Eksplicitno navodi komponentu (paket/aktivnost) koju treba pokrenuti, zaobilazeći sistemski birač. Ovo je izuzetno korisno za ciljano testiranje. Na primer, -n "com.example.myapp/.ui.MyShareActivity".25-c <CATEGORY>: Dodaje kategoriju Intent-u. Na primer, -c android.intent.category.DEFAULT.21Primer komande za slanje teksta:Shelladb shell am start -a android.intent.action.SEND -t "text/plain" \
-n "com.example.myapp/.ui.MyShareActivity" \
--es android.intent.extra.TEXT "Ovo je test poruka sa ADB-a"
3.2. Ubacivanje podataka sa ExtrasDa biste testirali kako vaša aplikacija rukuje različitim vrstama podataka, am start podržava dodavanje extras-a direktno iz komandne linije. Ovo je esencijalno za simulaciju realnih scenarija deljenja.18Najčešći tipovi extras-a su:--es <KEY> <STRING_VALUE>: Dodaje string.--ei <KEY> <INTEGER_VALUE>: Dodaje ceo broj.--ez <KEY> <BOOLEAN_VALUE>: Dodaje bulovu vrednost.--eu <KEY> <URI_VALUE>: Dodaje URI.Primer komande za slanje slike (URI-ja):Ova komanda simulira deljenje slike. Iako se Uri obično šalje preko EXTRA_STREAM, za am start je često lakše koristiti -d za glavni Uri podataka.Shelladb shell am start -a android.intent.action.SEND \
-t "image/jpeg" \
-d "content://media/external/images/media/12345" \
-n "com.example.myapp/.ui.MyShareActivity"
3.3. Tabela 2: Referenca ADB Intent komandiSledeća tabela služi kao brzi podsetnik za najčešće korišćene ADB flagove i tipove extras-a, centralizujući informacije iz izvora 18 radi lakšeg korišćenja.FlagOpisPrimer upotrebe-aAkcija (Action)-a android.intent.action.SEND-dURI podataka (Data URI)-d "https://developer.android.com"-tMIME tip-t "text/plain"-nKomponenta (Component)-n com.paket/.Aktivnost-cKategorija (Category)-c android.intent.category.LAUNCHER--esString Extra--es kljuc "vrednost"--eiInteger Extra--ei kljuc 100--ezBoolean Extra--ez kljuc true--euURI Extra--eu kljuc "content://..."Odeljak 4: Jedinično testiranje logike za rukovanje Intent-imaOvaj odeljak se fokusira na bazu piramide testiranja: kreiranje brzih, izolovanih i pouzdanih jediničnih testova za suštinsku logiku koja obrađuje dolazne Intent podatke.4.1. Čisti jedinični testovi sa Mock-ovanjem (MockK)Najbrži i najčistiji jedinični testovi se izvršavaju na JVM-u bez ikakvih zavisnosti od Android framework-a. Da bi se ovo postiglo, logika za obradu Intent-a mora biti izolovana od klasa kao što su Activity i Fragment. Kreiranje posebne klase, na primer ShareDataParser, koja uzima Intent kao ulaz i vraća strukturirani model podataka, je odlična praksa.Za testiranje takve klase, koristimo mocking biblioteku kao što je MockK da bismo kreirali lažne (mock) instance Intent klase. Ovo nam omogućava da precizno kontrolišemo šta Intent "sadrži" i da proverimo da li naša logika ispravno izvlači podatke.27Primer: Testiranje ShareDataParser-aPretpostavimo da imamo sledeću klasu za parsiranje:Kotlindata class SharedContent(val text: String?, val imageUri: Uri?)

class ShareDataParser {
fun parse(intent: Intent): SharedContent? {
if (intent.action!= Intent.ACTION_SEND) return null

        val text = intent.getStringExtra(Intent.EXTRA_TEXT)
        val imageUri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)

        return SharedContent(text, imageUri)
    }
}
Jedinični test za ovu klasu koristeći MockK bi izgledao ovako:Kotlinimport android.content.Intent
import android.net.Uri
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.*
import org.junit.Test

class ShareDataParserTest {

    private val parser = ShareDataParser()

    @Test
    fun `parse treba da vrati SharedContent sa tekstom kada je intent ispravan`() {
        // Priprema (Given)
        val mockIntent = mockk<Intent>()
        every { mockIntent.action } returns Intent.ACTION_SEND
        every { mockIntent.type } returns "text/plain"
        every { mockIntent.getStringExtra(Intent.EXTRA_TEXT) } returns "Zdravo svete"
        every { mockIntent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM) } returns null

        // Akcija (When)
        val result = parser.parse(mockIntent)

        // Provera (Then)
        assertNotNull(result)
        assertEquals("Zdravo svete", result?.text)
        assertNull(result?.imageUri)
    }

    @Test
    fun `parse treba da vrati null za nedostajuće podatke`() {
        // Priprema (Given)
        val mockIntent = mockk<Intent>()
        every { mockIntent.action } returns Intent.ACTION_SEND
        every { mockIntent.type } returns "text/plain"
        // Namerno ne mock-ujemo getStringExtra da bismo simulirali nedostajući extra
        every { mockIntent.getStringExtra(Intent.EXTRA_TEXT) } returns null
        every { mockIntent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM) } returns null

        // Akcija (When)
        val result = parser.parse(mockIntent)

        // Provera (Then)
        assertNotNull(result)
        assertNull(result?.text)
        assertNull(result?.imageUri)
    }
}
Ovi testovi su izuzetno brzi i proveravaju logiku parsiranja, uključujući i granične slučajeve kao što su nedostajući podaci.94.2. Jedinični testovi svesni Framework-a sa Robolectric-omPonekad je neizbežno da kod koji testiramo ima zavisnosti od Android framework-a (npr. Context, Resources). U tim slučajevima, umesto sporih instrumentacionih testova, možemo koristiti Robolectric da bismo i dalje izvršavali testove na brzoj JVM.12Robolectric simulira Android okruženje, omogućavajući nam da instanciramo prave Activity, Intent i druge framework klase. Njegova prava snaga leži u "senkama" (shadows) - testnim dvojnicima koji presreću pozive framework metoda i omogućavaju nam da proverimo interakcije.Primer: Provera da li ShareActivity pokreće DetailActivityPretpostavimo da naš ShareActivity, nakon što primi Intent sa specifičnim tekstom, treba da pokrene DetailActivity. Možemo testirati ovu interakciju koristeći Robolectric.Kotlinimport android.content.Intent
import android.widget.Button
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.Shadows.shadowOf
import org.robolectric.RuntimeEnvironment

@RunWith(RobolectricTestRunner::class)
class ShareActivityInteractionTest {

    @Test
    fun `primanje deljenog intenta treba da pokrene DetailActivity`() {
        // Priprema (Given)
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, "id:123")
        }

        // Pokrećemo aktivnost sa našim lažnim Intent-om
        val controller = Robolectric.buildActivity(ShareActivity::class.java, shareIntent).setup()
        val activity = controller.get()

        // Akcija (When)
        // Pretpostavimo da aktivnost automatski obrađuje intent u onCreate
        // i pokreće sledeću aktivnost.

        // Provera (Then)
        // Koristimo shadowOf da bismo dobili pristup "senkama" aplikacije
        val shadowApp = shadowOf(RuntimeEnvironment.getApplication())
        
        // Proveravamo koji je sledeći Intent pokrenut
        val nextStartedActivityIntent = shadowApp.nextStartedActivity

        // Proveravamo da li je to bio Intent za DetailActivity
        assertEquals(
            DetailActivity::class.java.name,
            nextStartedActivityIntent.component?.className
        )
        // Proveravamo da li su podaci prosleđeni ispravno
        assertEquals("id:123", nextStartedActivityIntent.getStringExtra("shared_data"))
    }
}
Iako se često klasifikuje kao alat za jedinično testiranje, sposobnost Robolectric-a da instancira stvarne Android framework objekte i posmatra njihove interakcije čini ga savršenim alatom za lokalne integracione testove. On može da verifikuje ceo lanac od Activity.onCreate() -> parsiranje Intent-a -> interakcija sa ViewModel-om -> pokretanje novog Intent-a za startovanje druge aktivnosti, sve unutar brzog JVM testa. Kada kod pozove startActivity(), Robolectric ne pokreće novi UI, ali njegova ShadowApplication beleži da je poziv napravljen i hvata Intent objekat.32 Ovo omogućava testiranje integracije između aktivnosti koja prima deljenje i aktivnosti koju ona následno odluči da pokrene, pokrivajući značajan deo logike aplikacije bez sporosti emulatora.Odeljak 5: Instrumentaciono testiranje: Provera integracije i korisničkog interfejsaOvaj odeljak pokriva testove koji se izvršavaju na fizičkom uređaju ili emulatoru, pružajući najviši nivo vernosti i osiguravajući da svi delovi aplikacije, uključujući i korisnički interfejs (UI), rade zajedno ispravno.5.1. Integraciono testiranje sa ActivityScenarioActivityScenario je moderan, preporučen način za pokretanje i upravljanje stanjem Activity-ja u instrumentacionim testovima. On pruža kontrolu nad životnim ciklusom aktivnosti i omogućava testiranje u realnom okruženju.33Pokretanje Activity-ja sa prilagođenim Intent-om:Da bismo testirali kako naš UI reaguje na deljene podatke, kreiramo testni Intent sa specifičnom akcijom, podacima i extras-ima, a zatim ga koristimo za pokretanje ciljane aktivnosti. Ovo se može uraditi direktno sa ActivityScenario.launch(intent) ili deklarativno pomoću ActivityScenarioRule.33Provera stanja UI-ja sa Espresso-m:Kada je aktivnost pokrenuta sa deljenim podacima, koristimo Espresso, Google-ov framework za UI testiranje, da bismo proverili da li se korisnički interfejs ispravno ažurirao. Espresso nam omogućava da pronađemo View-ove na ekranu i izvršimo provere (assertions) na njima.36Kotlinimport android.content.Intent
import android.net.Uri
import androidx.test.core.app.ActivityScenario
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class MyShareActivityUiTest {

    @Test
    fun `deljeni tekst se ispravno prikazuje u EditText-u`() {
        // Priprema (Given)
        val testIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, "Espresso test tekst")
        }

        // Akcija (When)
        // Pokrećemo aktivnost sa našim testnim Intent-om
        ActivityScenario.launch<MyShareActivity>(testIntent)

        // Provera (Then)
        // Koristimo Espresso da proverimo da li EditText sadrži deljeni tekst
        onView(withId(R.id.shared_text_input))
           .check(matches(withText("Espresso test tekst")))
           .check(matches(isDisplayed()))
    }
}
5.2. End-to-End (E2E) testiranje sa Espresso-Intents i UI Automator-omE2E testovi simuliraju kompletan korisnički put, uključujući interakcije koje izlaze van okvira vaše aplikacije.Espresso-Intents: Validacija i stubbing:Espresso-Intents je ekstenzija za Espresso koja omogućava testiranje Intent-a koji se šalju iz vaše aplikacije.Validacija (intended): Možemo proveriti da li je ispravan odlazni Intent poslat kada korisnik izvrši neku akciju. Na primer, ako naša share-aktivnost ima dugme "Otvori u pregledaču", možemo proveriti da li se pokreće ACTION_VIEW Intent sa ispravnim URL-om.38Stubbing (intending): Možemo presresti odlazne Intent-e i vratiti lažni rezultat (stub), bez stvarnog pokretanja druge aktivnosti. Ovo je izuzetno moćno za testiranje interakcija sa sistemskim biračima (npr. kontakti, datoteke), čineći testove bržim i pouzdanijim jer ne zavise od UI-ja spoljnih aplikacija.38UI Automator: Dosezanje izvan aplikacije:Espresso je ograničen na proces vaše aplikacije i ne može da interaguje sa sistemskim UI-jem kao što je Sharesheet ili sa drugim aplikacijama.15 Za pravo E2E testiranje, potreban nam je UI Automator.UI Automator može da automatizuje interakcije na nivou celog sistema. Test scenario bi izgledao ovako 15:Pokrenuti izvornu aplikaciju (npr. Galerija).Izvršiti akciju deljenja slike.Pronaći i kliknuti na ikonu vaše aplikacije u sistemskom Sharesheet-u.Proveriti da li se vaša aplikacija ispravno pokrenula i obradila Intent.Ovo je ultimativna validacija da je vaš manifest ispravno konfigurisan i da se aplikacija pravilno integriše sa Android OS-om. Međutim, ovi testovi su najsporiji i najkrhkiji (najpodložniji pucanju usled promena u OS-u), pa ih treba koristiti štedljivo, samo za najkritičnije korisničke tokove.Zaista robusna i održiva E2E strategija testiranja za deljene Intent-e je hibridna. Nepraktično je i krhko koristiti UI Automator za svaki testni slučaj deljenja. Najefikasniji pristup je kombinovanje ActivityScenario sa UI Automator-om. Velika većina E2E testova treba da koristi brži i pouzdaniji ActivityScenario.launch(intent) za testiranje svih različitih tipova podataka i scenarija nakon što je Intent primljen. Zatim, treba kreirati samo jedan ili dva "smoke testa" koristeći UI Automator. Jedina svrha ovih smoke testova je da potvrde da se aplikacija pojavljuje u Sharesheet-u za primarni MIME tip i da se može pokrenuti. Ovaj hibridni pristup pruža visoku pouzdanost E2E testiranja bez ogromnog tereta održavanja i sporog izvršavanja koje donosi čista UI Automator strategija.Odeljak 6: Ojačavanje aplikacije: Testiranje bezbednosti i graničnih slučajevaPoslednji i najkritičniji odeljak se fokusira na izgradnju otporne i bezbedne funkcije deljenja, proaktivnim testiranjem grešaka i ranjivosti. Ovo podiže izveštaj sa nivoa jednostavnog "kako da" na profesionalni vodič za izgradnju softvera spremnog za produkciju.6.1. Testiranje otpornosti: Prihvatanje neuspehaRobustna aplikacija mora graciozno da rukuje neočekivanim i neispravnim unosima. Testiranje "nesrećnih puteva" (unhappy paths) je ključno za sprečavanje padova aplikacije i lošeg korisničkog iskustva.42Neispravno formatirani Intent-i: Koristeći ADB (iz Odeljka 3) i instrumentacione testove (iz Odeljka 5), treba simulirati sledeće slučajeve:Pogrešan MIME tip: Slanje Intent-a sa type = "image/png", ali sa data URI-jem koji ukazuje na tekstualnu datoteku. Aplikacija ne sme da padne, već treba da prikaže korisniku odgovarajuću poruku o grešci.2Neusklađena akcija/podaci: Slanje ACTION_SEND Intent-a bez EXTRA_TEXT ili EXTRA_STREAM extra-a.Nepotpuni podaci: Testiranje mora da pokrije slučajeve gde su Intent extras-i null, Uri podaci null, ili gde ContentResolver ne može da razreši Uri (npr. datoteka je obrisana). Kod mora graciozno da rukuje NullPointerException i FileNotFoundException izuzecima.9Iscrpljivanje resursa: Posebnu pažnju treba posvetiti rukovanju veoma velikim datotekama (npr. 4K video). Ključno je obrađivati binarne podatke van glavne (UI) niti da bi se izbeglo blokiranje interfejsa. Takođe, preporučljivo je proveriti veličinu datoteke pre pokušaja učitavanja u memoriju kako bi se sprečili OutOfMemoryError izuzeci.16.2. Testiranje bezbednosnih ranjivosti: Razmišljanje kao napadačOvaj deo pretvara apstraktna bezbednosna upozorenja u konkretne, testabilne scenarije.Presretanje Intent-a (Intent Hijacking): Zlonamerna aplikacija može da registruje identičan intent-filter sa višim prioritetom kako bi presrela implicitne Intent-e namenjene vašoj aplikaciji.7 Primarna odbrana je korišćenje eksplicitnih Intent-a pozivom setPackage() kad god se šalju osetljivi podaci između vaših sopstvenih aplikacija ili kada je odredište poznato.7Ubacivanje/Preusmeravanje Intent-a (Intent Injection/Redirection): Ovo je suptilniji napad. Napadač može da kreira Intent koji cilja eksportovanu proxy aktivnost u vašoj aplikaciji. Ovaj Intent kao extra sadrži ugnježdeni, parcelable Intent. Proxy aktivnost može naivno da izvuče i pokrene ovaj ugnježdeni Intent, dajući napadaču pristup neeksportovanoj komponenti kojoj inače ne bi mogao da pristupi.8Proaktivna odbrana: Programeri moraju da testiraju svoje proxy komponente na ovu ranjivost. To se može uraditi simulacijom napada pomoću ADB-a.Primer ADB komande za testiranje ubacivanja Intent-a:Shell# Simulira napad gde je "evil_intent" ugnježden unutar glavnog intenta
# Cilja se ExportedProxyActivity u aplikaciji com.victim.app
# Ugnježdeni intent pokušava da pokrene neeksportovanu InternalSecretActivity
adb shell am start -n com.victim.app/.ExportedProxyActivity \
--es "extra_intent" "intent:#Intent;component=com.victim.app/.InternalSecretActivity;end"
Ako ova komanda uspe da pokrene InternalSecretActivity, vaša aplikacija je ranjiva.Validacija unosa: Svi podaci koji stižu putem Intent-a, posebno iz ACTION_SEND, dolaze iz nepouzdanog izvora i moraju biti rigorozno validirani i sanitizovani pre upotrebe.496.3. Tabela 3: Uobičajene ranjivosti Intenata i mere zaštiteSledeća tabela služi kao bezbednosna kontrolna lista, povezujući ranjivosti sa strategijama testiranja i ublažavanja, destilirajući ključne pretnje iz izvora.7RanjivostVektor napada / PrimerUticajUblažavanje i strategija testiranjaPresretanje implicitnog IntentaZlonamerna aplikacija registruje filter za vaš implicitni Intent.Krađa osetljivih podataka.Koristite eksplicitne Intent-e sa setPackage() gde je god moguće. Pre slanja, proverite da li Intent može biti razrešen sa resolveActivity().Ubacivanje/Preusmeravanje IntentaNapadač šalje ugnježdeni Intent eksportovanoj proxy aktivnosti.Neovlašćen pristup internim komponentama, krađa podataka.Ne eksportujte proxy komponente. Rigorozno validirajte sve ugnježdene Intent-e. Testirajte pomoću ADB simulacije ubacivanja.DoS usled neispravnih podatakaSlanje Intent-a sa pogrešnim MIME tipom ili neispravnim URI-jem.Pad aplikacije (Denial of Service).Koristite try-catch blokove oko obrade podataka. Testirajte sa neusklađenim MIME tipovima i nevažećim URI-jevima putem ADB-a i instrumentacionih testova.ZaključakTestiranje obrade deljenih Intent-a u Android aplikaciji je višeslojan proces koji zahteva više od puke provere funkcionalnosti. Robusna strategija mora da obuhvati ceo spektar, od brzih jediničnih testova logike parsiranja do sveobuhvatnih E2E testova koji potvrđuju ispravnu integraciju sa operativnim sistemom.Ključni zaključci su:Strateško testiranje je efikasno testiranje: Primena modela Piramide testiranja, sa velikom bazom brzih jediničnih testova (koristeći MockK i Robolectric) i malim brojem ciljanih, sporijih UI testova (koristeći Espresso i UI Automator), pruža najbolji odnos između pouzdanosti, brzine izvršavanja i troškova održavanja.ADB je vaš prvi saveznik: Pre automatizacije, adb shell am start je nezamenljiv alat za brzu, iterativnu proveru konfiguracije manifesta i osnovne obrade Intent-a.Bezbednost nije opcija, već obaveza: Intent-filter je javni API vaše aplikacije i mora se tretirati kao potencijalni bezbednosni rizik. Proaktivno testiranje na ranjivosti kao što su presretanje i ubacivanje Intent-a, kao i rigorozna validacija svih dolaznih podataka, su apsolutno neophodni za zaštitu korisnika i integriteta aplikacije.Otpornost se gradi na greškama: Najbolje aplikacije ne samo da rade ispravno na "srećnom putu", već i graciozno rukuju neočekivanim i neispravnim unosima. Testiranje graničnih slučajeva, kao što su neispravni MIME tipovi, nedostajući podaci i veliki fajlovi, je ono što razlikuje dobru aplikaciju od odlične.Usvajanjem sveobuhvatnog pristupa koji integriše funkcionalno, integraciono, E2E i bezbednosno testiranje, programeri mogu sa sigurnošću da izgrade funkciju deljenja koja nije samo moćna i korisna, već i otporna i bezbedna u dinamičnom Android ekosistemu.