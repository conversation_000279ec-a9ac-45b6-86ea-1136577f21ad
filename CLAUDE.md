# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🔴 CRITICAL MANDATORY REQUIREMENT 🔴

**YOU MUST READ AND FOLLOW ALL INSTRUCTIONS FROM `memory-bank/AI_AGENT_WORKFLOW.md` FILE**

This is NOT optional guidance - it is a MANDATORY procedure that MUST be followed for EVERY development task. The AI_AGENT_WORKFLOW.md contains a 13-step checklist that is CRITICAL for code quality, proper testing, and successful delivery.

**FAILURE TO FOLLOW THE AI_AGENT_WORKFLOW.md IS A CRITICAL ERROR - NO EXCEPTIONS**

## 🔴 CRITICAL MEMORY BANK MAINTENANCE 🔴

**YOU MUST UPDATE THE MEMORY BANK REGULARLY - THIS IS MANDATORY**

Memory resets completely between sessions. The Memory Bank is the ONLY way to maintain context and continue work effectively. You MUST:

1. **Read ALL memory bank files** at the start of EVERY task
2. **Update `activeContext.md`** after completing ANY code changes
3. **Update `progress.md`** when features are completed or status changes
4. **Update `systemPatterns.md`** when discovering new architectural patterns
5. **Check if memory bank needs updates** after EVERY development task

### Memory Bank Structure (Required Files)

- `projectbrief.md` - Foundation document, project scope and goals
- `productContext.md` - Why project exists, problems it solves
- `activeContext.md` - **CRITICAL**: Current work focus, recent changes, next steps
- `systemPatterns.md` - Architecture, technical decisions, design patterns
- `techContext.md` - Technologies, setup, constraints, dependencies
- `progress.md` - **CRITICAL**: What works, what's left, current status

### Memory Bank Update Triggers

**MANDATORY updates when:**
- Implementing significant changes
- Discovering new project patterns
- After completing features or fixing bugs
- When user requests "update memory bank"
- When context needs clarification

**IMPORTANT: WHEN YOU FINISH CODE EDITING ALWAYS CHECK IF WE NEED TO UPDATE `activeContext.md`**

The Memory Bank is your lifeline to project continuity - treat it as critically as the code itself.

## Project Overview

LyricsGenerator is an Android app for creating and managing synchronized lyrics (LRC) files. It follows Clean Architecture with MVVM pattern using Jetpack Compose.

## Architecture Requirements

**MANDATORY: Follow Clean Architecture + MVVM pattern with strict layer separation**

- **Presentation Layer**: Jetpack Compose UI with ViewModels (`ui/` package)
- **Domain Layer**: Business logic and use cases (`domain/` package)
- **Data Layer**: Repository implementations (`data/` package)

**Package Structure**: Feature-based organization under `soly.lyricsgenerator`

## Critical Code Standards

**ZERO TOLERANCE policies - code reviews MUST reject violations:**

1. **No Force Unwrapping**: Never use `!!` - always use safe calls `?.` and Elvis operator `?:`
2. **No When Expressions**: Use sealed classes with polymorphic dispatch across ALL layers
3. **No Hardcoded Strings**: UI text via `stringResource()`, constants for keys/identifiers
4. **Analytics Constants**: Use `AnalyticsConstants.kt` - never hardcode analytics strings
5. **Hilt DI**: All ViewModels with `@HiltViewModel`, constructor injection only
6. **Timber Logging Standards**: MANDATORY logging format and usage patterns (see [Timber Logging Standards](#timber-logging-standards) section)

## Timber Logging Standards

**MANDATORY: All debugging and logging MUST follow these exact patterns**

### Required Logging Format
```kotlin
// MANDATORY format: Use DEBUG_FLOW tag with class prefix
Timber.tag("DEBUG_FLOW").d("ClassName: Descriptive message with context")

// Examples:
Timber.tag("DEBUG_FLOW").d("OverlayService: calculateMaxOverlayHeight() called")
Timber.tag("DEBUG_FLOW").d("OverlayService: Height calculated: ${height}px (${height/density}dp)")
Timber.tag("DEBUG_FLOW").e(exception, "OverlayService: Error in createOverlayView")
```

### Logging Requirements

**Critical Logging Points (MANDATORY):**
1. **Method Entry/Exit**: Log when entering and exiting important methods
2. **State Changes**: Log all state transitions and data updates
3. **Size/Layout Changes**: Log all dimension calculations and layout modifications
4. **Error Conditions**: Log all exceptions with full context
5. **User Interactions**: Log user actions that trigger functionality
6. **Service Lifecycle**: Log service start/stop/state changes

**Log Levels:**
- `Timber.tag("DEBUG_FLOW").d()` - Debug information, method calls, state changes
- `Timber.tag("DEBUG_FLOW").e()` - Errors and exceptions (include exception object)
- `Timber.tag("DEBUG_FLOW").w()` - Warnings and unusual conditions
- `Timber.tag("DEBUG_FLOW").i()` - Important information and milestones

**Examples for Overlay Debugging:**
```kotlin
// Size calculations
Timber.tag("DEBUG_FLOW").d("OverlayService: Screen dimensions: ${width}x${height}")
Timber.tag("DEBUG_FLOW").d("OverlayService: Container padding: ${containerPadding}px")

// Layout changes  
Timber.tag("DEBUG_FLOW").d("OverlayService: Overlay position updated: x=${params.x}, y=${params.y}")
Timber.tag("DEBUG_FLOW").d("OverlayService: WindowManager.LayoutParams: width=${params.width}, height=${params.height}")

// Content changes
Timber.tag("DEBUG_FLOW").d("OverlayService: Lyrics content changed: ${lyrics.size} lines")
Timber.tag("DEBUG_FLOW").d("OverlayService: UI recomposition triggered: isDragging=$isDragging")
```

## Common Development Commands

```bash
# Build and validation (MANDATORY sequence)
./gradlew clean build          # Must pass
./gradlew test                 # Must pass  
./gradlew connectedAndroidTest # Must pass

# Development workflow
./gradlew assembleDebug
./gradlew :app:installDebug

# Specific test execution
./gradlew test --tests "ClassName"
./gradlew :app:connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=ClassName
```

## Key Technologies

- **Jetpack Compose**: Material3 UI with custom theme
- **Hilt**: Dependency injection throughout
- **Room**: Local database with migrations
- **Navigation Compose**: Single activity architecture
- **Firebase**: Analytics, Crashlytics, remote config
- **Media**: ExoPlayer integration, audio metadata editing
- **Security**: EncryptedSharedPreferences with AES256_GCM

## Feature Implementation Pattern

1. **Domain First**: Create use cases and repository interfaces
2. **Data Layer**: Implement repositories with Room/API
3. **Presentation**: ViewModels with sealed state classes, Compose UI
4. **Testing**: Unit tests for domain, UI tests for components, integration tests for flows
5. **Constants**: Extract strings to resources, create constant objects for keys

## Testing Requirements

**MANDATORY: All three test types must pass before task completion**

- **Unit Tests**: ViewModels, use cases, business logic
- **UI Tests**: Compose components and screens
- **Integration Tests**: Complete user flows end-to-end

## Task Completion Policy

**🔴 CRITICAL: NEVER MARK TASKS AS COMPLETED WITHOUT USER APPROVAL 🔴**

**MANDATORY WORKFLOW:**
1. **Implement the solution** - Write code, fix bugs, add features
2. **Test compilation** - Ensure code compiles successfully  
3. **Update memory bank** - Document changes and current status
4. **Present solution to user** - Explain what was implemented and how
5. **WAIT FOR USER FEEDBACK** - User must explicitly approve or request changes
6. **Only mark completed after approval** - Never assume task is done without user confirmation

**FORBIDDEN:**
- ❌ Marking tasks as "completed" or "resolved" without user feedback
- ❌ Assuming implementation is correct without user testing
- ❌ Moving on to next tasks before current task approval
- ❌ Closing issues or updating status to "done" unilaterally

**REQUIRED:**
- ✅ Present solution and ask "Please test this and let me know if it works"
- ✅ Keep task status as "pending user verification" until approved
- ✅ Wait for explicit user confirmation before marking anything complete
- ✅ Be ready to make adjustments based on user feedback

## Build Configuration

- **Target SDK**: 34, **Min SDK**: 26
- **Gradle**: 8.10.2, **AGP**: 8.5.0, **Kotlin**: 1.9.23
- **ProGuard**: Enabled for release (61.7% size reduction)
- **Multi-language**: 10 languages supported

## Security & Privacy

- **Storage**: No MANAGE_EXTERNAL_STORAGE permission - use SAF only
- **Analytics**: UUID-based privacy-friendly tracking
- **Permissions**: Runtime permission handling with proper fallbacks
