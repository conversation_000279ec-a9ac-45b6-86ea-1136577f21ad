# Sorting Logic Refactoring Plan

## 1. Objective

To refactor the song sorting logic to be handled by the `MusicPlayerService`. This will make the sorting mechanism consistent with the existing "Show Favorites Only" filtering feature, where the service is responsible for preparing the final playback queue.

## 2. Analysis of the Current Flow (Favorites Filter)

-   `MusicViewModel` calls `MusicPlayerService.startService` with the `ACTION_LOAD_SONGS` action when the favorites filter is toggled.
-   `MusicPlayerService` receives the action and triggers its internal `loadSongs()` method.
-   Inside `loadSongs()`, the service reads the state of the favorites filter from `SharedPreferences`.
-   It then fetches all songs and, if the filter is active, filters the list *within the service*.
-   The resulting list is stored in the service's `songsQueue` and broadcast back to the `ViewModel`.

## 3. Proposed Flow (New Sorting Logic)

We will replicate the exact same flow for sorting.

-   When the user changes the sort order, `MusicViewModel` will call `MusicPlayerService.startService` with a new action (`ACTION_UPDATE_SORTING`) and the new sort parameters.
-   `MusicPlayerService` will receive the action, save the new sort parameters to `SharedPreferences`, and then call its internal `loadSongs()` method.
-   The `loadSongs()` method will be modified to read the saved sort parameters and apply the sorting to the list *after* applying the favorites filter.
-   The final, sorted list will be stored in the `songsQueue` and broadcast back to the `ViewModel`.

## 4. Task Breakdown

### Task 1: Enhance `MusicPlayerService.kt`

-   [ ] **Create New Action**: In the `companion object`, add a new constant for the sorting action.
    ```kotlin
    const val ACTION_UPDATE_SORTING = "ACTION_UPDATE_SORTING"
    ```
-   [ ] **Create New Extras**: In the `companion object`, add new constants for the sort parameters.
    ```kotlin
    const val EXTRA_SORT_TYPE = "EXTRA_SORT_TYPE"
    const val EXTRA_SORT_ORDER = "EXTRA_SORT_ORDER"
    ```
-   [ ] **Handle New Action**: In the `onStartCommand` method's `when` block, add a case for `ACTION_UPDATE_SORTING`.
    -   This case will extract the `sortType` and `sortOrder` from the intent's extras.
    -   It will save these new values to `SharedPreferences`.
    -   It will then call the `loadSongs()` method to trigger a refresh of the playlist.
-   [ ] **Update `loadSongs()` Method**:
    -   After fetching the raw list of songs from the `MusicUseCase`, and after applying the existing favorites filter logic, add a new step.
    -   Read the saved `sortType` and `sortOrder` from `SharedPreferences`.
    -   Apply the sorting to the song list.
    -   Set the final, sorted list to the `songsQueue`.

### Task 2: Update `MusicViewModel.kt`

-   [ ] **Modify `onSortChanged()`**:
    -   This function will no longer update the local `_sortType` and `_sortOrder` state flows directly.
    -   Instead, it will call `MusicPlayerService.startService`, passing the `ACTION_UPDATE_SORTING` action and the new `sortType` and `sortOrder` as extras.
-   [ ] **Simplify `filteredSongsFlow`**:
    -   The `combine` function for `filteredSongsFlow` will be simplified.
    -   Remove `sortType` and `sortOrder` from its inputs.
    -   Remove the `.sortedWith{...}` logic from its body. The `_songsFlow` will now provide a pre-sorted list from the service. The `filteredSongsFlow` will now only be responsible for applying the text-based search query.

### Task 3: Verification

-   [ ] **Verify `MusicBroadcastReceiver`**: No changes should be needed, but verify that the receiver correctly updates the `_songsFlow` in the `MusicViewModel` when it receives the `ACTION_SONGS_LOADED` broadcast from the service.
-   [ ] **Build and Test**: After all changes are implemented, build the application and test the following scenarios:
    -   Changing sort order updates the list correctly.
    -   The "Next" and "Previous" buttons respect the new sort order.
    -   Sorting works correctly when combined with the "Show Favorites Only" filter.
    -   Sorting works correctly when combined with a search query.

This plan ensures that the responsibility for preparing the playback queue (both filtering and sorting) resides solely within the `MusicPlayerService`, creating a consistent and centralized data management pattern. 