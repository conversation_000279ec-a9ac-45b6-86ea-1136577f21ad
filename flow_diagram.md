graph TB

%% =================================================================
%% App Entry & File Management (Outside the main wrapper)
%% =================================================================

subgraph "App Entry Points"
Open["App<br/>Opened"]
Open --> Music["Music<br/>Tab"]
Open --> Create["Create<br/>Tab"]
Open --> Files["Files<br/>Tab"]
end

%% =================================================================
%% Tag Editor Screen
%% =================================================================

subgraph "Tag Editor"
TagEditorScreen["🏷️ Tag Editor"]
end


%% =================================================================
%% Settings Screen
%% =================================================================

subgraph "Settings Screen"
SettingsScreen["⚙️ Settings<br/>Screen"]
end


%% =================================================================
%% Music Screen Flow (Complete Music Tab functionality)
%% =================================================================

subgraph "Music Screen"
Music --> MusicScreen["🎵 Music<br/>Screen"]

    subgraph "Music Top App Bar"
        MusicScreen --> MusicTopBar["📱 Music<br/>Top Bar"]
        MusicTopBar --> MusicMenuBtn["☰ Menu"]
        MusicTopBar --> MusicSearchBtn["🔍 Search<br/>Songs"]
        MusicTopBar --> FavoritesToggle["❤️ Favorites<br/>Filter"]
        MusicTopBar --> ArtistFilterBtn["👥 Artist<br/>Filter"]
        MusicTopBar --> SortBtn["↕️ Sort<br/>Songs"]
        
        MusicMenuBtn --> SettingsScreen["⚙️ Settings<br/>Screen"]
        MusicSearchBtn --> MusicSearchMode["🔎 Search<br/>Mode Active"]
        FavoritesToggle --> FavoritesOnlyView["❤️ Favorites<br/>Only View"]
        ArtistFilterBtn --> ArtistFilterSheet["🎭 Artist Filter<br/>Bottom Sheet"]
        SortBtn --> SortSheet["📊 Sort<br/>Bottom Sheet"]
        
        ArtistFilterSheet --> MusicScreen
        SortSheet --> MusicScreen
        MusicSearchMode --> MusicScreen
        FavoritesOnlyView --> MusicScreen
    end
    
    subgraph "Music Screen States"
        MusicScreen --> PermissionCheck{Permission?}
        PermissionCheck -->|No| NoPermState["⚠️ No Permission<br/>Grant Button"]
        PermissionCheck -->|Yes| LoadingCheck{Loading?}
        LoadingCheck -->|Yes| LoadingState["🔄 Loading<br/>Songs"]
        LoadingCheck -->|No| SongsCheck{Has Songs?}
        SongsCheck -->|No| NoSongsState["⚠️ No Music<br/>Found"]
        SongsCheck -->|Yes| SongsList["📱 Songs<br/>List"]
        
        NoPermState -->|Grant| PermissionRequest["📝 Request<br/>Permission"]
        PermissionRequest --> MusicScreen
    end
    
    subgraph "Song List Features"
        SongsList --> EachSong["🎵 Each Song<br/>Item has:"]
        EachSong -->|Click| PlaySong["▶️ Play<br/>Song"]
        EachSong -->|⋮| SongMenu["3-dot<br/>Menu"]
        
        SongMenu --> SongDetails["ℹ️ Song<br/>Details"]
        SongMenu --> TagEditorFromMusic["✏️ Tag<br/>Editor"]
        SongMenu --> FavoriteToggle["❤️ Toggle<br/>Favorite"]
        
        SongDetails --> SongDetailsSheet["📋 Song Details<br/>Bottom Sheet"]
        TagEditorFromMusic --> TagEditorScreen["🏷️ Tag Editor"]
        SongDetailsSheet --> SongsList
    end
    
    subgraph "Compact Music Player"
        MusicScreen --> CompactPlayer["🎮 Compact<br/>Player"]
        CompactPlayer --> PlayPauseBtnCompact["⏯️ Play/<br/>Pause"]
        CompactPlayer --> PrevBtnCompact["⏮️ Previous<br/>Song"]
        CompactPlayer --> NextBtnCompact["⏭️ Next<br/>Song"]
        CompactPlayer --> ExpandPlayer["🔍 Expand<br/>Player"]
        
        ExpandPlayer --> SongDetailsFromPlayer["🎵 Song Details<br/>Screen"]
    end
end

%% External connections from Music Screen
PlaySong --> CompactPlayer
SongDetailsFromPlayer --> MusicScreen

%% =================================================================
%% Song Details Screen (Full Player & Lyrics Display)
%% =================================================================

subgraph "Song Details Screen"
SongDetailsFromPlayer --> SongDetailsMain["🎵 Song Details<br/>Screen"]

    subgraph "Song Details Top App Bar"
        SongDetailsMain --> SongDetailsTopBar["📱 Song Details<br/>Top Bar"]
        SongDetailsTopBar --> BackToMusic["← Back to<br/>Music"]
        SongDetailsTopBar --> TagEditorFromDetails["✏️ Tag<br/>Editor"]
        SongDetailsTopBar --> FloatingLyrics["🔄 Floating<br/>Lyrics"]
        SongDetailsTopBar --> ImportLRCBtn["📁 Import<br/>LRC File"]
        
        BackToMusic --> MusicScreen
        TagEditorFromDetails --> TagEditorScreen["🏷️ Tag Editor"]
        FloatingLyrics --> OverlayPermCheck{Overlay Permission?}
        ImportLRCBtn --> FileChooserSheet["📄 File Chooser<br/>Bottom Sheet"]
        
        OverlayPermCheck -->|Yes| OverlayService["📱 Overlay<br/>Service"]
        OverlayPermCheck -->|No| OverlayPermDialog["⚠️ Permission<br/>Dialog"]
        OverlayPermDialog --> OverlaySettings["⚙️ System<br/>Settings"]
        OverlaySettings --> SongDetailsMain
        
        FileChooserSheet --> LRCFilePicker["📂 LRC File<br/>Picker"]
        LRCFilePicker --> LRCContentUpdate["🔄 Update<br/>Lyrics"]
        LRCContentUpdate --> SongDetailsMain
    end
    
    subgraph "Song Details Content"
        SongDetailsMain --> ContentRenderer["📝 Content<br/>Renderer"]
        ContentRenderer --> ContentCheck{Content Type?}
        ContentCheck -->|LRC| LRCRenderer["🎵 LRC Lyrics<br/>Display"]
        ContentCheck -->|TXT| TXTRenderer["📄 TXT Content<br/>Display"]  
        ContentCheck -->|Empty| NoContentState["⚠️ No Lyrics<br/>Available"]
        
        LRCRenderer --> SyncedLyrics["✨ Synced<br/>Lyrics"]
        TXTRenderer --> StaticText["📄 Static<br/>Text"]
        SyncedLyrics --> HighlightCurrent["🎯 Highlight<br/>Current Line"]
        SyncedLyrics --> AutoScroll["🔄 Auto<br/>Scroll"]
    end
    
    subgraph "Full Music Player"
        SongDetailsMain --> FullPlayer["🎮 Full Music<br/>Player"]
        FullPlayer --> SeekBarFull["━━━━━<br/>Seek Bar"]
        FullPlayer --> PlayPauseFull["⏯️ Play/<br/>Pause"]
        FullPlayer --> PreviousFull["⏮️ Previous<br/>Song"]
        FullPlayer --> NextFull["⏭️ Next<br/>Song"]
        FullPlayer --> ShuffleToggle["🔀 Shuffle<br/>Toggle"]
        FullPlayer --> FavoriteBtn["❤️ Favorite<br/>Toggle"]
        
        SeekBarFull --> PlaybackSeek["🎯 Seek to<br/>Position"]
        PlaybackSeek --> UpdateLyrics["🔄 Update Lyrics<br/>Sync"]
    end
    
    subgraph "Song Details States"
        SongDetailsMain --> SongLoadCheck{Song Available?}
        SongLoadCheck -->|No| NoSongState["⚠️ No Song<br/>Selected"]
        SongLoadCheck -->|Yes| LyricsLoadCheck{Has Lyrics?}
        LyricsLoadCheck -->|No| ImportPrompt["📁 Import<br/>Prompt"]
        LyricsLoadCheck -->|Yes| ShowLyrics["📝 Display<br/>Lyrics"]
        
        ImportPrompt --> FileChooserSheet
        NoSongState --> BackToMusic
    end
    
    subgraph "Dialogs & Feedback"
        SongDetailsMain --> StatusSnackbars["📢 Status<br/>Messages"]
        StatusSnackbars --> ExportStatus["💾 Export<br/>Status"]
        StatusSnackbars --> PermissionStatus["🔐 Permission<br/>Messages"]
        StatusSnackbars --> FileStatus["📁 File Operation<br/>Status"]
    end
end

%% External connections from Song Details Screen
OverlayService --> FloatingLyricsActive["📱 Floating Lyrics<br/>Active"]



subgraph "Files Flow"
Files --> FileList["Files<br/>List"]
FileList --> FileItem["Each File<br/>Item has:"]
FileItem -->|⋮| FileMenu["3-dot<br/>Menu"]
FileMenu --> SyncOpt["🔄 Sync<br/>(TXT/RTF)"]
FileMenu --> TagEditOpt["✏️ Tag<br/>Editor"]
FileMenu --> ExportOpt["📤 Export"]
FileMenu --> DeleteOpt["🗑️ Delete"]
FileItem -->|Click| SongDetailsMain["🎵 Song Details<br/>Screen"]
end

subgraph "Files Tab - Top App Bar"
Files --> FilesTopBar["📱 Files<br/>Top Bar"]
FilesTopBar --> HamburgerMenu["☰ Menu"]
FilesTopBar --> SearchBtn["🔍 Search"]
FilesTopBar --> ImportBtn["➕ Import"]
HamburgerMenu --> SettingsScreen["⚙️ Settings<br/>Screen"]
SearchBtn --> SearchMode["🔎 Search<br/>Mode Active"]
ImportBtn --> FilePickerFromFiles["File Picker<br/>(TXT/RTF/LRC)"]
FilePickerFromFiles --> SongLinking["🎵 Song<br/>Picker"]
SongLinking --> FileList
end

%% External connections from File Flow to other screens
TagEditOpt --> TagEditorScreen["Tag Editor<br/>Screen"]
ExportOpt --> FileSaver["System<br/>File Picker"]
DeleteOpt --> DeleteDialog["Delete<br/>Confirm"]


%% =================================================================
%% Wrapper for Core Sync Functionality
%% =================================================================

subgraph "Create Screen"

    subgraph "Create Flow"
        Create --> Steps["Steps<br/>Screen"]
        Steps --> ChooseSong["Choose<br/>a song"]
        Steps --> AddLyrics["Add<br/>lyrics"]
        AddLyrics --> ImportTXT["Import<br/>from TXT"]
        AddLyrics --> PasteLyrics["Paste<br/>Lyrics"]
        ImportTXT --> LyricsInput["Lyrics<br/>Ready"]
        PasteLyrics --> LyricsInput
        ChooseSong --> StartCondition{Both Ready?}
        LyricsInput --> StartCondition
        StartCondition --> StartSync["Start<br/>Syncing"]
    end

    %% Entry points into Sync Mode from different flows
    StartSync --> SyncMode["🎵 Sync Mode<br/>Screen"]
    SyncOpt --> SyncMode["🎵 Sync Mode<br/>Screen"]

    subgraph "Sync Mode Interface"
        direction LR
        SyncMode --> TopBarSection["📱 Top<br/>App Bar"]
        SyncMode --> LinesSection["📝 Lyrics<br/>Lines"]
        SyncMode --> PlayerSection["🎮 Media<br/>Controller"]
    end

    subgraph "Auto Features"
        SyncMode --> Highlight["✨ Line<br/>Highlight"]
        SyncMode --> RealTime["⏱️ Real-time<br/>Tracking"]
    end

    subgraph "Player Controls"
        PlayerSection --> SeekBar["━━━━━<br/>Seek Bar"]
        PlayerSection --> BackBtn["-5<br/>sec"]
        PlayerSection --> PlayPauseBtn["⏯️ Play/<br/>Pause"]
        PlayerSection --> FwdBtn["+5<br/>sec"]
    end

    subgraph "Line Controls"
        LinesSection --> ClickLine["📍 Click: Add<br/>Timestamp"]
        LinesSection --> PlusBtn["➕ +500ms"]
        LinesSection --> MinusBtn["➖ -500ms"]
        LinesSection --> PlayLineBtn["▶️ Play<br/>from Line"]
    end

    subgraph "Top Bar Actions"
        TopBarSection --> ChangeSongBtn["🎵 Change<br/>Song"]
        TopBarSection --> ChangeTextBtn["📄 Change<br/>Text"]
        TopBarSection --> SaveBtn["💾 Save<br/>LRC"]
        TopBarSection --> PreviewBtn["👁️ Preview"]
        TopBarSection --> UndoBtn["↩️ Undo<br/>All"]
    end

    subgraph "Navigation Result"
        ChangeSongBtn -.-> SongPicker["Song<br/>Picker"]
        ChangeTextBtn -.-> FilePicker["File<br/>Picker"]
        SaveBtn -.-> SaveDialog["Save<br/>Dialog"]
        PreviewBtn -.-> PreviewMode["Preview<br/>Display"]
        UndoBtn -.-> UndoDialog["Confirm<br/>Dialog"]
        SongPicker -.-> SyncMode
        FilePicker -.-> SyncMode
        SaveDialog -.-> FilesUpdate["Updates<br/>Files Tab"]
    end
end
