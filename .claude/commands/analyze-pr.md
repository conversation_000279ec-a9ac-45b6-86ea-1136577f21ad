# Analyze PR Command

**Analysis Only Mode - No Implementation Without Approval**

This command fetches a GitHub Pull Request, analyzes reviewer comments and feedback, investigates the current branch source code, and offers detailed solution recommendations. **CRITICAL: This is analysis-only mode - no code changes will be made without explicit user approval.**

## Usage
```
/analyze-pr <PR-NUMBER>
```

## Workflow

You are Claude Code operating in **ANALYSIS MODE ONLY**. Follow this exact workflow:

### 1. PR Investigation Phase
- Fetch the PR using: gh pr view $ARGUMENTS
  - Read all reviewer comments, feedback, and requested changes
- Understand the PR context, scope, and purpose
- Identify all issues that need resolution

### 2. Source Code Analysis Phase  
- Investigate the current branch source code, use gh pr diff $ARGUMENTS
- Map reviewer comments to specific code locations
- Analyze the technical context and architecture
- Identify root causes of flagged issues

### 3. Test Execution Phase
**MANDATORY: Run tests to verify PR quality**
- Run unit tests: `./gradlew test`
- Run UI tests: `./gradlew connectedAndroidTest`
- Document any test failures or warnings
- Include test results in the analysis report

### 4. Solution Research Phase
- Research best practices for identified issues
- Consider multiple solution approaches
- Evaluate trade-offs and implementation complexity
- Align solutions with project architecture (Clean Architecture + MVVM)

### 5. Recommendation Phase
**CRITICAL: Present findings without taking action**

For each identified issue, provide:
- **Problem Summary**: Clear description of the issue
- **Root Cause**: Technical explanation of why it occurs
- **Test Results**: Summary of unit and UI test execution
- **Architecture Alignment**: How solutions align with Clean Architecture + MVVM patterns
- **Solution Options**: 2-3 different approaches with pros/cons, following project structure
- **Recommended Approach**: Your preferred solution with rationale, aligned with:
  - Feature-based package organization under `soly.lyricsgenerator`
  - Sealed classes with polymorphic dispatch (NO when expressions)
  - Hilt dependency injection with `@HiltViewModel`
  - ZERO tolerance policies (no force unwrapping `!!`, no hardcoded strings)
  - Analytics constants from `AnalyticsConstants.kt`
  - Timber logging with `DEBUG_FLOW` tag format
- **Implementation Steps**: High-level steps following project conventions
- **Risk Assessment**: Potential impacts and considerations

### 6. Approval Gate
**MANDATORY: Wait for explicit user approval before any implementation**

Present your analysis and ask:
> "I've analyzed the PR and identified [X] issues with recommended solutions. Which approach would you like me to implement for [specific issue]? Should I proceed with the recommended solution or would you prefer a different approach?"

### 7. Implementation Phase (Only After Approval)
- Only proceed after user explicitly approves a specific solution
- Follow the AI_AGENT_WORKFLOW.md mandatory procedures
- Update memory bank with changes
- Test thoroughly before marking complete

## Key Principles

- **Analysis First**: Always analyze before acting
- **Multiple Options**: Present alternative solutions
- **User Choice**: User decides which approach to implement  
- **No Surprises**: Never implement without clear approval
- **Quality Focus**: Ensure solutions meet project standards

## Example Usage

```bash
/analyze-pr 123
```

This will trigger a comprehensive analysis of PR #123 with solution recommendations, waiting for your approval before any implementation.