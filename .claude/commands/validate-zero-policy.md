---
name: "validate-zero-policy"
description: "Validate zero tolerance policies on staged git changes only"
version: "1.0.0"
author: "LyricsGenerator Project"
tags: ["validation", "git", "staged-changes", "zero-tolerance"]
---

# Validate Zero Policy Command

**Fast Zero Tolerance Policy Validation for Staged Changes**

This command validates only your staged git changes against zero tolerance policies, making it perfect for pre-commit validation without scanning the entire project.

## Usage
```bash
/validate-zero-policy [--fix]
```

**Arguments:**
- `--fix`: Automatically attempt to fix violations in staged files

## Workflow

**Step 1: Get Staged Files**
```bash
# Get only staged Kotlin files
git diff --cached --name-only --diff-filter=AM | grep "\.kt$"
```

**Step 2: Run Zero Policy Checks on Staged Files Only**
For each staged file, run these focused checks:

### Critical Violations (Must Fix)

#### 1. No Force Unwrapping (`!!`)
```bash
# Check staged files for !!
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n "!!" 2>/dev/null
```

#### 2. No Hardcoded Strings
```bash
# Check for hardcoded strings (comprehensive check)
# This catches:
# - Direct string literals in method calls
# - Default values in getString() calls  
# - String parameters in analytics/logging
# - Any string literal over 3 characters (excluding common operators)

# First pass: Check for common hardcoded string patterns
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n '"[^"]\{3,\}"' 2>/dev/null | \
  grep -v "Timber\." | \
  grep -v "stringResource" | \
  grep -v "^[[:space:]]*\/\/" | \
  grep -v "^[[:space:]]*\*"

# Second pass: Specifically check for getString default values
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n 'getString([^,]*,[[:space:]]*"[^"]*"' 2>/dev/null

# Third pass: Check for SharedPreferences names
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n 'getSharedPreferences([[:space:]]*"[^"]*"' 2>/dev/null

# Fourth pass: Check for intent actions and extras
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n 'putExtra([[:space:]]*"[^"]*"' 2>/dev/null
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n 'getStringExtra([[:space:]]*"[^"]*"' 2>/dev/null
```

**Common violations to watch for:**
- SharedPreferences names: `getSharedPreferences("app_prefs", ...)`
- Preference keys: `putString("SORT_TYPE", value)`
- Default values: `getString("key", "Default Value")`
- Intent extras: `putExtra("EXTRA_DATA", data)`
- Action strings: `Intent("com.app.ACTION_NAME")`

**Excluded from checks:**
- Timber logging statements (e.g., `Timber.d("Debug message")`)
- String resources (e.g., `stringResource(R.string.app_name)`)
- Comments and documentation
- Constant declarations

#### 3. No When Expressions
```bash
# Check for when expressions in staged files
git diff --cached --name-only --diff-filter=AM | grep "\.kt$" | xargs grep -n "when\s*(" 2>/dev/null
```

### **Step 3: Report Results**
- ✅ **PASS**: No violations in staged changes
- ❌ **FAIL**: Show specific files and line numbers with violations


## Usage Examples

```bash
# Validate staged changes (before commit)
/validate-zero-policy

# Validate and attempt auto-fix on staged files
/validate-zero-policy --fix
```

## Perfect for Pre-Commit Workflow

```bash
# Typical workflow:
git add MusicPlayerService.kt AnalyticsConstants.kt
/validate-zero-policy
# Fix any violations
git commit -m "fix: improve error handling with proper constants"
```

## Complete Validation Script

Here's a comprehensive script that runs all checks:

```bash
#!/bin/bash
# validate-zero-policy.sh

echo "🔍 Validating Zero Tolerance Policies on Staged Changes..."

# Get staged Kotlin files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=AM | grep "\.kt$")

if [ -z "$STAGED_FILES" ]; then
    echo "✅ No staged Kotlin files to validate"
    exit 0
fi

echo "📁 Checking files: $STAGED_FILES"
VIOLATIONS=0

# Check 1: Force Unwrapping
echo -n "Checking for force unwrapping (!!)... "
if echo "$STAGED_FILES" | xargs grep -n "!!" 2>/dev/null; then
    echo "❌ FOUND"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo "✅ PASS"
fi

# Check 2: Hardcoded Strings (Multiple passes)
echo -n "Checking for hardcoded strings... "
HARDCODED_FOUND=0

# Check getString default values
if echo "$STAGED_FILES" | xargs grep -n 'getString([^,]*,[[:space:]]*"[^"]*"' 2>/dev/null; then
    HARDCODED_FOUND=1
fi

# Check SharedPreferences names  
if echo "$STAGED_FILES" | xargs grep -n 'getSharedPreferences([[:space:]]*"[^"]*"' 2>/dev/null; then
    HARDCODED_FOUND=1
fi

# Check general strings (excluding allowed patterns)
if echo "$STAGED_FILES" | xargs grep -n '"[^"]\{3,\}"' 2>/dev/null | \
   grep -v "Timber\." | grep -v "stringResource" | \
   grep -v "^[[:space:]]*\/\/" | grep -v "^[[:space:]]*\*" | \
   grep -v "const val"; then
    HARDCODED_FOUND=1
fi

if [ $HARDCODED_FOUND -eq 1 ]; then
    echo "❌ FOUND"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo "✅ PASS"
fi

# Check 3: When Expressions
echo -n "Checking for when expressions... "
if echo "$STAGED_FILES" | xargs grep -n "when\s*(" 2>/dev/null; then
    echo "❌ FOUND"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo "✅ PASS"
fi

# Summary
if [ $VIOLATIONS -eq 0 ]; then
    echo "✅ All zero tolerance policies passed!"
    exit 0
else
    echo "💥 Found $VIOLATIONS violation type(s) in staged changes"
    echo "Fix violations before committing!"
    exit 1
fi
```

## What This Would Have Caught

In our recent changes, this enhanced validation would have detected:

```bash
📁 Staged files: MusicPlayerService.kt
❌ Hardcoded strings detected!
MusicPlayerService.kt:283: getSharedPreferences("lyric_generator_prefs", ...)
MusicPlayerService.kt:285: putString("SORT_TYPE", type)
MusicPlayerService.kt:786: getString(PreferenceKeys.SORT_TYPE, "Title")
MusicPlayerService.kt:792: getString(PreferenceKeys.SORT_ORDER, "Ascending")
💥 Found 1 violation type(s) in staged changes
```

The enhanced checks specifically catch:
- Default values in `getString()` calls that we missed before
- SharedPreferences names
- All string literals that should be constants

This **comprehensive approach** ensures no hardcoded strings slip through!