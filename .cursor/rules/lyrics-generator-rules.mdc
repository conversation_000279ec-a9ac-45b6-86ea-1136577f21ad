---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
C1: Before you start changing the android project code base fetch android_best_practices.md using list_guidelines mcp call 
C2: Always use MVVM architecture pattern, clean architecture
C3: Use java.time classes for date/time operations
C4: Use timber.log.Timber for logging with DEBUG_FLOW tag, class prefixes, and proper import management 
C5: When you finish with changing the code build and run the app using build_and_run.sh
C6: Instructions for log monitoring:
* **Start log monitoring ONLY ONCE** using the command:
    ```bash
    adb logcat -c && adb logcat | grep -i "DEBUG_FLOW"
    ```
* **Key point:** After starting logcat once using the command above, **DO NOT START** a new `adb logcat` process again (using any command that would initiate new log monitoring). The existing process should remain active.
* The only exception is if the user **explicitly requests a logcat restart** (which would involve stopping the existing one and starting a new one, likely after clearing with `adb logcat -c`).
* When analyzing or referring to logs, always assume they are being collected by the **already active**, initially started logcat process.








