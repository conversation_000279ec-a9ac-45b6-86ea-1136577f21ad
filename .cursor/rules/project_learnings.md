## Project Learnings & Patterns

### Jetpack Compose State Delegation
- **Rule/Pattern:** When using Jetpack Compose state variables delegated with the `by` keyword (e.g., `val myState by remember { mutableStateOf(false) }` or `val data by viewModel.someFlow.collectAsState()`), the variable itself directly holds the current value. Access the state's value directly (e.g., `if (myState)` or `Text(data.someProperty)`). Avoid using `.value` (e.g., `myState.value`), as this is for non-delegated `State` objects and will lead to 'Unresolved reference' compilation errors when used with delegated properties. 