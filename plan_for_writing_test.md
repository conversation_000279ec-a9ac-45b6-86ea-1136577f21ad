# Detaljni Plan: Poboljšanje SharedAudioIntegrationTest za Proveru Reprodukcije Audio Fajla

## Problem Analize

Trenutni `SharedAudioIntegrationTest` proverava samo da li se aplikacija navigira na `Music screen`, ali **NE proverava** da li se deljeni audio fajl stvarno reprodukuje. Treba dodati provere:
1.  Da li se media player kontrole prikazuju
2.  Da li je naziv pesme vidljiv u UI
3.  Da li je play/pause dugme u "pause" stanju (što znači da se reprodukuje)
4.  Da li se `MusicViewModel` state pravilno ažurira

---

## Faza 1: Istraživanje Postojeće Test Infrastrukture (15 min)

### 1.1 Analiza TestTags za Media Player

**Fajlovi za analizu:**
-   `app/src/main/java/soly/lyricsgenerator/ui/constants/UITestTags.kt`
-   `app/src/main/java/soly/lyricsgenerator/ui/screens/songs_list_screen/MusicScreen.kt`
-   `app/src/main/java/soly/lyricsgenerator/ui/screens/components/CompactMusicPlayerController.kt`

**Što tražiti:**
-   `TestTags` za play/pause dugme
-   `TestTags` za current song display
-   `TestTags` za media player komponente
-   `TestTags` za progress bar i position text

### 1.2 Analiza Test Dependency Structure

**Fajlovi za analizu:**
-   `app/src/androidTest/java/.../testModules/TestAppModule.kt`
-   `app/src/androidTest/java/.../testConstants/TestConstants.kt`
-   `app/src/androidTest/java/.../fake/FakeMusicRepository.kt`

**Što tražiti:**
-   Kako su *fake* repository-ji implementirani
-   Kako se test podaci definišu
-   Da li postoji `FakeContentResolverRepository`
-   Kako Hilt dependency injection radi u testovima

---

## Faza 2: Kreiranje Missing Test Infrastructure (45 min)

### 2.1 Kreiranje `FakeContentResolverRepository` (15 min)

**Novi fajl:** `app/src/androidTest/java/soly/lyricsgenerator/fake/FakeContentResolverRepository.kt`

```kotlin
@Singleton
class FakeContentResolverRepository @Inject constructor() : ContentResolverRepository {

    private val uriMappings = mutableMapOf<Uri, FakeAudioInfo>()

    data class FakeAudioInfo(
        val path: String,
        val displayName: String,
        val mimeType: String
    )

    fun setupTestUri(uri: Uri, path: String, displayName: String) {
        uriMappings[uri] = FakeAudioInfo(path, displayName, "audio/mpeg")
    }

    override suspend fun resolveUriToPath(uri: Uri): String? {
        return uriMappings[uri]?.path
    }

    override suspend fun getDisplayName(uri: Uri): String? {
        return uriMappings[uri]?.displayName
    }

    fun clear() {
        uriMappings.clear()
    }
}
```

### 2.2 Ažuriranje `TestConstants` (10 min)

**Fajl:** `app/src/androidTest/java/.../testConstants/TestConstants.kt`

```kotlin
object TestConstants {

    // Postojeći test constants...

    object SharedAudio {
        val TEST_URI = Uri.parse("content://fake.provider/test_audio.mp3")
        val TEST_SONG_PATH = "/fake/path/test_audio.mp3"
        val TEST_SONG_TITLE = "Test Shared Audio"
        val TEST_SONG_ARTIST = "Test Artist"
        val TEST_DISPLAY_NAME = "test_audio.mp3"

        val TEST_SONG = Song(
            id = 999L,
            title = TEST_SONG_TITLE,
            artist = TEST_SONG_ARTIST,
            path = TEST_SONG_PATH,
            duration = 180000L // 3 minutes
        )
    }
}
```

### 2.3 Kreiranje Test Utility Functions (10 min)

**Novi fajl:** `app/src/androidTest/java/soly/lyricsgenerator/utils/SharedAudioTestUtils.kt`

```kotlin
object SharedAudioTestUtils {

    fun setupFakeSharedAudio(
        fakeContentResolverRepository: FakeContentResolverRepository,
        fakeMusicRepository: FakeMusicRepository
    ) {
        // Setup URI mapping
        fakeContentResolverRepository.setupTestUri(
            uri = TestConstants.SharedAudio.TEST_URI,
            path = TestConstants.SharedAudio.TEST_SONG_PATH,
            displayName = TestConstants.SharedAudio.TEST_DISPLAY_NAME
        )

        // Add corresponding song to music repository
        fakeMusicRepository.addSong(TestConstants.SharedAudio.TEST_SONG)
    }

    fun createTestSharedAudioIntent(): Intent {
        return Intent(IntentConstants.Actions.ACTION_SHARE_AUDIO).apply {
            type = "audio/mpeg"
            putExtra(Intent.EXTRA_STREAM, TestConstants.SharedAudio.TEST_URI)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
    }
}
```

> **Napomena o Testnoj Infrastrukturi:**
> Faza 2.2 iz originalnog plana (Ažuriranje `TestAppModule`) je namerno preskočena. Umesto globalne zamene zavisnosti u `TestAppModule`, plan se oslanja na `@BindValue` anotaciju direktno u test klasi (Faza 3.1). Ovaj pristup pruža bolju izolaciju testova i smatra se čistijom praksom za Hilt UI testiranje.

---

## Faza 3: Ažuriranje `SharedAudioIntegrationTest` (30 min)

### 3.1 Setup Test Dependencies (10 min)

```kotlin
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SharedAudioIntegrationTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @BindValue
    @JvmField
    val fakeContentResolverRepository = FakeContentResolverRepository()

    @BindValue
    @JvmField
    val fakeMusicRepository = FakeMusicRepository()

    @Before
    fun setup() {
        hiltRule.inject()

        // Setup fake shared audio data
        SharedAudioTestUtils.setupFakeSharedAudio(
            fakeContentResolverRepository,
            fakeMusicRepository
        )
    }
}
```

### 3.2 Kreiranje Novog Test Case za Reprodukciju (20 min)

```kotlin
@Test
fun testSharedAudioIntent_StartsPlaybackAutomatically() {
    // Given: Intent sa fake shared audio URI
    val intent = SharedAudioTestUtils.createTestSharedAudioIntent()

    // When: Pokretamo MainActivity sa intent-om
    ActivityScenario.launch<MainActivity>(intent)

    // Then: Proveravamo navigaciju na Music screen
    composeTestRule.waitUntil(timeoutMillis = 10000) {
        composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
            .fetchSemanticsNodes().size == 1
    }

    val musicTabText = composeTestRule.activity.getString(R.string.music)
    composeTestRule.onNode(
        hasText(musicTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))
    ).assertIsSelected()

    // KLJUČNO: Proveravamo da li se audio reprodukuje

    // 1. Čekamo da se media player komponente učitaju
    composeTestRule.waitUntil(timeoutMillis = 8000) {
        composeTestRule.onAllNodes(hasTestTag(UITestTags.COMPACT_PLAYER_CONTAINER))
            .fetchSemanticsNodes().isNotEmpty()
    }

    // 2. Proveravamo da li je naziv pesme prikazan
    composeTestRule.onNode(hasText(TestConstants.SharedAudio.TEST_SONG_TITLE))
        .assertIsDisplayed()

    // 3. Proveravamo da li je artist prikazan
    composeTestRule.onNode(hasText(TestConstants.SharedAudio.TEST_SONG_ARTIST))
        .assertIsDisplayed()

    // 4. Proveravamo da li je pause dugme vidljivo (znači da se reprodukuje)
    composeTestRule.waitUntil(timeoutMillis = 5000) {
        try {
            composeTestRule.onNode(hasTestTag(UITestTags.PAUSE_BUTTON))
                .assertExists()
            true
        } catch (e: AssertionError) {
            false
        }
    }

    composeTestRule.onNode(hasTestTag(UITestTags.PAUSE_BUTTON))
        .assertIsDisplayed()

    // 5. Proveravamo da li se progress bar pomera (opciono)
    composeTestRule.onNode(hasTestTag(UITestTags.PROGRESS_BAR))
        .assertIsDisplayed()

    // 6. Proveravamo da li je current position text vidljiv
    composeTestRule.onNode(hasTestTag(UITestTags.CURRENT_POSITION_TEXT))
        .assertIsDisplayed()
}
```

---

## Faza 4: Dodavanje Helper Functions za UI Provere (15 min)

### 4.1 Media Player State Verification

```kotlin
private fun assertMediaPlayerIsPlaying() {
    // Multiple checks to ensure playback started
    composeTestRule.onNode(hasTestTag(UITestTags.PAUSE_BUTTON))
        .assertIsDisplayed()
        .assertIsEnabled()

    // Verify play button is not visible (should be pause button instead)
    composeTestRule.onNode(hasTestTag(UITestTags.PLAY_BUTTON))
        .assertDoesNotExist()
}

private fun assertSongInformationDisplayed(expectedTitle: String, expectedArtist: String) {
    composeTestRule.onNode(hasText(expectedTitle))
        .assertIsDisplayed()

    composeTestRule.onNode(hasText(expectedArtist))
        .assertIsDisplayed()
}

private fun waitForMediaPlayerToLoad(timeoutMs: Long = 8000) {
    composeTestRule.waitUntil(timeoutMillis = timeoutMs) {
        composeTestRule.onAllNodes(hasTestTag(UITestTags.COMPACT_PLAYER_CONTAINER))
            .fetchSemanticsNodes().isNotEmpty()
    }
}
```

---

## Faza 5: Edge Cases i Additional Tests (20 min)

### 5.1 Test za Multiple Audio URIs

```kotlin
@Test
fun testSharedMultipleAudioIntent_PlaysFirstMatchingSong() {
    // Setup multiple fake songs
    val secondSong = TestConstants.SharedAudio.TEST_SONG.copy(
        id = 1000L,
        title = "Second Test Song",
        path = "/fake/path/second_test.mp3"
    )
    fakeMusicRepository.addSong(secondSong)

    val secondUri = Uri.parse("content://fake.provider/second_test.mp3")
    fakeContentResolverRepository.setupTestUri(
        uri = secondUri,
        path = "/fake/path/second_test.mp3",
        displayName = "second_test.mp3"
    )

    // Create intent with multiple URIs
    val intent = Intent(IntentConstants.Actions.ACTION_SHARE_MULTIPLE_AUDIO).apply {
        type = "audio/mpeg"
        putParcelableArrayListExtra(Intent.EXTRA_STREAM, arrayListOf(
            TestConstants.SharedAudio.TEST_URI,
            secondUri
        ))
    }

    ActivityScenario.launch<MainActivity>(intent)

    waitForMediaPlayerToLoad()

    // Should play the first matching song
    assertSongInformationDisplayed(
        TestConstants.SharedAudio.TEST_SONG_TITLE,
        TestConstants.SharedAudio.TEST_SONG_ARTIST
    )
    assertMediaPlayerIsPlaying()
}
```

### 5.2 Test za `VIEW` Intent

```kotlin
@Test
fun testViewAudioIntent_StartsPlaybackAutomatically() {
    val intent = Intent(IntentConstants.Actions.ACTION_VIEW_AUDIO).apply {
        data = TestConstants.SharedAudio.TEST_URI
        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
    }

    ActivityScenario.launch<MainActivity>(intent)

    waitForMediaPlayerToLoad()
    assertSongInformationDisplayed(
        TestConstants.SharedAudio.TEST_SONG_TITLE,
        TestConstants.SharedAudio.TEST_SONG_ARTIST
    )
    assertMediaPlayerIsPlaying()
}
```

---

## Faza 6: Cleanup i Optimizacija (10 min)

### 6.1 Dodavanje Proper Cleanup

```kotlin
@After
fun cleanup() {
    // Reset fake repositories
    fakeContentResolverRepository.clear()
    fakeMusicRepository.clear()
}
```

### 6.2 Optimizacija Postojećih Testova

-   Ažurirati postojeće testove da koriste istu *fake* infrastrukturu
-   Ukloniti `MediaStore` operacije koje nisu potrebne
-   Dodati provere reprodukcije u postojeće test cases

---

## Očekivani Rezultati

Po završetku implementacije:
1.  Svi testovi prolaze bez dependency na stvarne audio fajlove
2.  Reprodukcija se proverava kroz UI state provere
3.  Edge cases pokriveni - multiple URIs, `VIEW` intents, no matching songs
4.  Brži testovi - bez file I/O operacija
5.  Deterministički rezultati - kontrolisani test podaci

---

## Rizici i Mitigacija

-   **Rizik:** `TestTags` možda ne postoje za sve potrebne komponente
    -   **Mitigacija:** Dodati potrebne `TestTags` u production kod
-   **Rizik:** `Fake` `MusicPlayerService` možda ne podržava sve operacije
    -   **Mitigacija:** Proširiti `fake` implementaciju po potrebi
-   **Rizik:** Timing issues u asinhronim operacijama
    -   **Mitigacija:** Dodati robust `wait conditions` sa proper timeout-ima