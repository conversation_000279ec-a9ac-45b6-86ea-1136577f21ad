# Commit Message Template for LyricsGenerator
# 
# Format: <type>: <description>
#
# Types:
# feat: new feature
# fix: bug fix
# refactor: code change that neither fixes a bug nor adds a feature
# perf: performance improvement
# docs: documentation changes
# style: formatting changes (no functional changes)
# test: adding or updating tests
# build: changes to build system or dependencies
#
# Examples:
# feat: add shuffle button to music player controls
# fix: resolve crash when rapidly clicking favorite button
# perf: optimize database queries to eliminate N+1 problem
# refactor: extract common UI components for reusability
#
# Rules:
# - Keep first line under 50 characters
# - Use imperative mood ("add" not "added")
# - Include details in body if needed
# - 🚨 NEVER include AI/Claude references 🚨
#
# What changed:
# - 
# 
# Why:
# - 
#