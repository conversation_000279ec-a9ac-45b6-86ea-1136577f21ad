Here’s a clean update you can paste into Issue #138 to lock specs + tests for Shuffle/Sort/Filter with the single-list + cursor model.

⸻

Summary

Unify playback logic (Shuffle, Sort, Filter, Repeat) using one authoritative list (timeline) and a cursor. Eliminate multiple parallel queues; rebuild only the future slice when rules change.

Goals
	•	Correct, predictable transitions when toggling Shuffle.
	•	Respect current sorting/filtering when Shuffle is OFF.
	•	Do not disrupt the current run when Shuffle is ON (until toggled OFF).
	•	Handle live changes (add/remove tracks, filtering) without losing the user’s place.
	•	Cover Repeat (Off/All/One).

Data model
	•	timeline: MutableList<TrackId> — contains past…current…future.
	•	cursor: Int — index of current in timeline.
	•	shuffleOn: Boolean
	•	repeat: RepeatMode { OFF, ALL, ONE }
	•	baseProvider(): List<TrackId> — returns Base = playlist as currently sorted + filtered.

History = timeline[0 until cursor] (no extra stack).

Expected behavior matrix

Current	User action	Result
Shuffle OFF	Toggle ON	future = (Base − {past ∪ current}).shuffled(); keep current. No repeats until cycle ends.
Shuffle OFF	Change sort	Recompute Base; future = Base.after(current) + Base.before(current) (wrap only if Repeat=All).
Shuffle OFF	Filter narrows	If current still matches → rebuild future from filtered Base after current. If current filtered out → jump to next matching (wrap if Repeat=All) or stop if none.
Shuffle OFF	Filter widens	Recompute Base; rebuild future from new Base after current.
Shuffle ON	Toggle OFF	Snap to linear: find current in latest Base; future = Base.after(current) + Base.before(current) (wrap per Repeat).
Shuffle ON	Change sort	Do not touch current shuffled run now. Apply new Base only when user toggles Shuffle OFF later.
Shuffle ON	Filter narrows	Keep current if it matches; prune non-matching items from remaining future. If current no longer matches → advance to next matching (wrap if Repeat=All); stop if none.
Shuffle ON	Filter widens	Append newly visible tracks into future at random positions (or re-shuffle remaining once; pick and document one policy).

Edge rules
	•	RepeatOne: replay current; ignore queue rebuilds for next().
	•	End of shuffled cycle:
	•	RepeatAll → reseed future from current Base (shuffled).
	•	RepeatOff → stop.
	•	Current removed (deleted or filtered out): jump to next valid track (wrap per Repeat). If none → stop.
	•	Add/remove while Shuffle ON: remove deleted from timeline; append new tracks randomly into future.
	•	Add/remove while Shuffle OFF: rebuild future from Base after current.

Minimal implementation (Kotlin-ish)

data class PlayerState(
  val timeline: MutableList<TrackId>,
  var cursor: Int,
  var shuffleOn: Boolean,
  var repeat: RepeatMode,
  val baseProvider: () -> List<TrackId>
)

fun PlayerState.rebuildFutureLinear(base: List<TrackId>) {
  val cur = timeline[cursor]
  val i = base.indexOf(cur).coerceAtLeast(0)
  val future = base.drop(i + 1) + base.take(i) // wrap sequence
  timeline.subList(cursor + 1, timeline.size).apply { clear(); addAll(future) }
}

fun PlayerState.toggleShuffle(on: Boolean) {
  shuffleOn = on
  if (repeat == RepeatMode.ONE) return
  val base = baseProvider()
  if (on) {
    val seen = timeline.subList(0, cursor + 1).toSet()
    val future = base.filter { it !in seen }.shuffled()
    timeline.subList(cursor + 1, timeline.size).apply { clear(); addAll(future) }
  } else {
    rebuildFutureLinear(base)
  }
}

fun PlayerState.next(): TrackId? {
  if (repeat == RepeatMode.ONE) return timeline[cursor]
  if (cursor + 1 < timeline.size) { cursor++; return timeline[cursor] }
  return when (repeat) {
    RepeatMode.ALL -> { toggleShuffle(shuffleOn); if (cursor + 1 < timeline.size) { cursor++; timeline[cursor] } else null }
    RepeatMode.OFF -> null
    RepeatMode.ONE -> timeline[cursor]
  }
}

fun PlayerState.onSortChanged() {
  if (!shuffleOn) rebuildFutureLinear(baseProvider())
}

fun PlayerState.onFilterChanged() {
  val base = baseProvider()
  val cur = timeline[cursor]
  if (cur !in base) {
    // advance to next matching or stop
    val idx = base.indexOfFirst { true } // first available
    if (idx < 0) return /* stop: no matches */ else { timeline[cursor] = base[idx] }
  }
  if (shuffleOn) {
    // prune non-matching from future
    val keep = base.toSet()
    val future = timeline.subList(cursor + 1, timeline.size).filter { it in keep }
    timeline.subList(cursor + 1, timeline.size).apply { clear(); addAll(future) }
  } else {
    rebuildFutureLinear(base)
  }
}

fun PlayerState.onTracksChanged(added: List<TrackId>, removed: List<TrackId>) {
  val cur = timeline[cursor]
  timeline.removeAll(removed.toSet())
  cursor = timeline.indexOf(cur).takeIf { it >= 0 } ?: minOf(cursor, timeline.lastIndex).coerceAtLeast(0)
  if (shuffleOn) {
    val present = timeline.toSet()
    val toAdd = added.filterNot { it in present }.shuffled()
    timeline.addAll(cursor + 1, toAdd)
  } else rebuildFutureLinear(baseProvider())
}

QA checklist (acceptance criteria)
	•	Toggling Shuffle OFF mid-song continues from that song in current sort order.
	•	Changing sort while Shuffle ON doesn’t reorder current run; toggling OFF later uses new order.
	•	Filtering to a subset keeps playback if current matches; otherwise advances to next match or stops if none.
	•	No repeats within a shuffled cycle (until all remaining tracks are played).
	•	RepeatOne always replays current, regardless of sort/shuffle/filter.
	•	Add/remove tracks handled without losing current or history; deleted tracks never play again in the same cycle.

Test cases (unit)
	1.	Toggle OFF from last element (RepeatOff) → stop.
	2.	Toggle OFF from last (RepeatAll) → wraps to first in Base.
	3.	Sort change + Shuffle ON → OFF continues from current’s index in new Base.
	4.	Filter narrows (current survives) → future pruned, order preserved for OFF; random for ON.
	5.	Current removed → advance correctly; stop if playlist empty.
	6.	Shuffled cycle exhausts → reseed only with RepeatAll.

⸻

If you want, I can open a PR scaffold with this engine + JUnit tests and wire it into your ViewModel.