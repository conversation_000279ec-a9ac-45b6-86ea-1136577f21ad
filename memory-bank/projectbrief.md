# LRC Music Player App Description

When the app opens, we have three screens with top bar and bottom navigation. In the bottom navigation we have three options: music, create and files. By default the first bottom menu is active with Music Screen displayed.

# Main Navigation Screens

## Music Screen

It displays top app bar with search button, fragment with its Music Screen content and bottom navigation.  
The search icon from the top app bar filters the list of songs. When the filter input field is active, the X icon is visible instead of search icon. When X button is clicked, the list resets to its initial state (complete song list) and the search input field is removed from the top app bar with search icon presented.  
The Music screen contains the list of songs. The songs are fetched from a device MediaStore.  
Song list item has just text while the song is not playing. When the song is playing we have a progress bar in that list item. Click on a song item opens Song Details Screen with data about that song and current playing position.

## Create Screen Flow

### Overview

When the Create tab is selected in bottom navigation, the Create Screen opens with the Steps component always displayed initially. The Steps component requires selecting both song and text file. It remains visible until Start is pressed, allowing changes to selections. On Start, it's replaced by lyrics, timestamps, and buttons: Replace Song, Replace Text File, Save, Preview, and Discard Changes (X). Replace Song/Text File functions identically to the Steps component, without showing it again. Save prompts for a filename. Preview hides text and plays synced lyrics. Discard Changes asks for confirmation, then discards unsaved changes and allows free navigation.

### Lyric Synchronization Process

#### 1. Preparation

- User selects a song from the list.  
- Then loads lyrics text by either:  
  - Importing a TXT file  
  - Pasting text  
  - Using transcription  
- When both song and text are ready, user clicks "Start creating LRC".

#### 2. Entering Synchronization Mode

- The screen displays:  
  - A list of all text lines (verses, lyrics)  
  - Song playback controls (play/pause, position slider)  
  - The song is ready for playback

#### 3. Tap-Based Synchronization

- User starts the song by clicking Play.  
- While the song plays:  
  - User views the text displayed as a list of lines  
  - When a specific line should be sung, user taps on that line  
  - The app records the current timestamp and assigns it to that line  
  - User repeats tapping for each subsequent line at the moment it begins  
  - This way, the entire text is synchronized with the music

#### 4. Correction and Fine-Tuning

- If the user misses the moment or wants more precision:  
  - Pauses the song  
  - Moves the slider to the exact time where the line should start  
  - Taps on the line again – the timestamp is updated  
  - This can be done multiple times until satisfied  
  - User can also play the song from any point to check synchronization

#### 5. Play Button Next to Each Line

- A small Play button exists next to each line  
- When clicked:  
  - Song automatically jumps to that line's timestamp  
  - Begins playing from that point  
- User can:  
  - Listen to the synchronization for that line  
  - Check if it's well-timed  
  - Correct the timestamp again if needed

#### 6. Plus (+) and Minus (–) Buttons for Micro-Adjustments

- Plus (+) and minus (–) buttons appear next to each line  
- Used for fine-tuning timestamps:  
  - Plus (+) → moves timestamp forward (e.g., +100ms)  
  - Minus (–) → moves timestamp backward (e.g., –100ms)  
- User workflow:  
  - Taps on a line to record time  
  - If text:  
    - Starts too early → clicks plus (+) to move it forward  
    - Starts too late → clicks minus (–) to move it backward  
  - Each click shifts the timestamp by a small step (typically 50–200 ms)  
  - Can click multiple times until completely satisfied  
  - Clicks play next to the line to check  
  - Repeats correction if necessary

#### 7. Active Line Display During Playback

- While the song plays:  
  - App automatically highlights the current line being sung in a component below the tapping list above the progress bar  
  - This helps:  
    - Track synchronization in real-time  
    - Easily spot and correct errors

#### 8. Completion

- When synchronization is finished:  
  - User can save the LRC file  
  - The file contains text and timestamps for each line  
  - LRC file can be used for:  
    - Karaoke  
    - Videos with text  
    - Export and sharing

#### User Experience Summary

- Very intuitive – tap on lines while the song plays  
- Can be corrected anytime by tapping or seeking  
- Play buttons help check each line  
- Plus and minus allow micro-adjustments without re-tapping  
- Highlighting the active line makes tracking easier  
- In the end, user gets a precisely synchronized LRC file

There are four ways to exit the Create screen:

- The save button  
- The discard changes button  
- Navigation to another tab  
- The back button

Returning to the Create screen after leaving it restarts the process (song and text selections should be cleared) of syncing text with song, beginning with selecting files from the Steps Component. This doesn't apply if we go from Create screen to file chooser or Paste Lyrics Screen.

### Steps Component

- Requires selecting both a song and a text file before proceeding.  
- Remains visible until the user clicks "Start", even if they try to change the selected song.  
- Allows the user to replace both the song and the text file selections.

### Post-Start Screen

- Replaces the Steps Component when "Start" is clicked.  
- Displays:  
  - A list of lyrics lines.  
  - A text file with timestamps.  
  - Buttons:  
    - Replace Song  
    - Replace Text File  
    - Save  
    - Preview  
    - Discard Changes (X)

### Button Functionality

#### Replace Song

- Opens the Song List Screen, which is the same screen used for song selection in the Steps Component.  
- Replaces the current song in both the UI and the app's functionality.  
- Does not display the Steps Component.

#### Replace Text File

- Opens a File Chooser.  
- Replaces the current lyrics lines in the UI with the content from the newly selected file.  
- Does not display the Steps Component.

#### Save

- Prompts the user to enter a filename.  
- Saves the file using the entered filename.

#### Preview

**Preview Component Behavior**

The `LyricsDisplay` component must present the **entire list** of lyric lines (`lrcLines`) in a vertically scrollable area, with the top of the list aligned under the top app bar. As `currentTimeMs` updates:

**Phase 1 – Static List**  
- No scrolling: the active line moves down the list naturally.

**Phase 2 – Center Lock**  
- When the active line would move below the vertical midpoint of the viewport, the component **automatically scrolls** so that the active line is **locked at the vertical center** of the visible area.

**Styling**  
- **Active line**: full opacity, bold weight, slightly scaled up (e.g. 1.1×), and using the designated active color.  
- **Inactive lines**: gray color with reduced opacity.

**Scroll logic**  
1. Compute active index from `currentTimeMs`.  
2. Calculate its y-position: `index * lineHeight`.  
3. If y-position < `viewportHeight/2`, keep scroll at 0.  
4. Once y-position ≥ `viewportHeight/2`, set scroll offset to:  
   `(index * lineHeight) - (viewportHeight/2 - lineHeight/2)`.  
5. Apply this offset to center the active line continuously.

This ensures a seamless, focus-centered preview experience.

#### Discard Changes (X)

- Displays a dialog box asking the user to confirm that they want to discard changes.  
- If confirmed, discards all changes and allows the user to switch between tabs without further warnings.

### Note

Two distinct behaviors of Steps Component:

- During initial setup with the Steps component: Steps remain visible when changing songs  
- After clicking "Start" (Post-Start screen): Steps remain hidden whatever we do

## Files Screen

The screen has top app bar, fragment with its content and bottom navigation.  
In the top app bar navigation we have search button, import and refresh button.

The search filters the list of files in the Files fragment. When the filter input field is active the X icon is presented, the import and refresh buttons are hidden. When the X icon is clicked filter clears completely and the files list resets to its initial state.  
The refresh button reloads files from the database.

The screen also features a Floating Action Button (FAB) for importing new LRC files.
- Tapping the FAB initiates a two-step process:
  1.  The user is prompted to select an LRC file using the system file chooser (similar to the import functionality on the Song Details Screen).
  2.  After selecting an LRC file, the user is navigated to the Song List Screen to choose a song to associate with the imported lyrics.
- Once both the LRC file and the song are selected:
  - The LRC file is copied into the app's internal sandbox storage.
  - A new entry is created in the database, linking the imported LRC file (and its content) to the chosen song.
  - The list of files on the Files Screen is refreshed to display the newly added item.

The main content is the list of files. Each files item is a cell with the lrc file title at the top of the cell, below is the corresponding audio file and the lrc file extension below. At the right end of the cell is menu icon (three vertical dots). Clicking on the three dots displays popup window with delete and export option.  
Tapping on the files item opens Song Details Screen.

# Additional Screens

## Song Details Screen

This screen is a player screen. To navigate to this screen we need to click on a song in the Music Screen, click on files item in Files Screen or click on the title in the app notification.  
The screen contains, top app bar (back button, export video button and import lrc button), central content and the Media Controller at the bottom.  
The back button always navigates to the Music screen, export button exports video with lrc, and import opens system file picker. When a file is picked its content is displayed in the central content of the screen.

Central content is responsible for displaying the synchronized lyrics. Here, the same `LyricsDisplay` component is used as in the Create screen's Preview mode, maintaining identical scrolling, centering, and highlighting behavior for active lyric lines.  
If there is no lrc file for currently playing song, the "No lyrics available" text is presented in the center of the screen. If the lrc file is presented, the text is animated. The currently playing line is in the center of the screen. Above and below it are two lines, above are two past lines and below are two next lines.

The Media Controller component at the bottom of the screen contains, shuffle button, song title in one line, below we have a progress bar, with the current timestamp to its left side and total song duration to its right side. Below are controllers, play previous, play/pause and play next buttons.

## Paste Text Screen

This screen allows users to manually enter or paste lyrics text instead of selecting a text file. The screen contains:

- A text input area where users can type or paste lyrics  
- Save button to confirm and use the entered text  
- Back navigation which automatically discards changes and returns to the previous screen  

The Paste Text option is available as an alternative to choosing a text file in the Create Screen. After saving the pasted text, the flow continues the same way as if a text file had been selected.

## Song List Screen

This is a specialized screen for selecting songs that appears in two contexts:

1. When "Choose a song" is selected in the Create Screen's Steps Component  
2. When importing an LRC file in the Files Screen and needing to link it with a song  

The Song List Screen displays:

- A song list of all available songs from the device's MediaStore  
- Search functionality to filter songs  

When a song is tapped, it is immediately selected and the user is returned to the previous screen with steps component presented.

# General App Features

## Music Playback Continuity

When a user is playing music in the Music Screen and navigates to the Create tab, then returns to the Music Screen, the music should continue to play uninterrupted.

## Notification

The notification is dismissible when the app is closed and music is paused. The notification is able to open the Song Details Screen or move the Song Details Screen into the foreground when the song title is clicked in the notification. The notification has progress bar and three buttons. Buttons are play previous, play/pause and play next. The play/pause button must accurately reflect the media playback status. We must be able to control Media Player Service via notification and must always correctly represent media playback state.

## Bottom Navigation

The bottom navigation always must represent current active screen which can be Music, Create or Files screen. The icon is in the accent color when the corresponding screen is displayed.