# Android Jetpack Compose Project Structure Guide

This document outlines the organizational structure and architectural patterns for a modern Android project using **Jetpack Compose** with **Clean Architecture** and **MVVM** pattern.

## Project Overview

This project follows **Clean Architecture** principles with **MVVM** (Model-View-ViewModel) pattern, leveraging Jetpack Compose for the UI layer to ensure separation of concerns, testability, and maintainability.

## Root Directory Structure

```
project-root/
├── app/                    # Main Android application module
├── gradle/                 # Gradle wrapper files
├── build/                  # Build outputs (auto-generated)
├── build.gradle.kts       # Root build configuration
├── settings.gradle.kts    # Project settings
├── gradle.properties      # Gradle properties
└── [documentation]/       # Project documentation files
```

## Architecture: Clean Architecture + MVVM

### Feature Module Structure

Each feature follows a consistent three-layer architecture:

```
features/[feature_name]/
├── data/              # DATA LAYER
│   ├── repositories/  # Repository implementations
│   ├── sources/       # Data sources
│   │   ├── remote/   # API calls
│   │   └── local/    # Database/cache
│   ├── models/       # Data Transfer Objects (DTOs)
│   └── mappers/      # DTO to Domain model mappers
│
├── domain/            # DOMAIN LAYER
│   ├── models/        # Business entities
│   ├── repositories/  # Repository interfaces
│   ├── use_cases/     # Business logic/rules
│   └── validators/    # Business validation
│
├── presentation/      # PRESENTATION LAYER
│   ├── screens/       # Compose screens
│   ├── components/    # Reusable Compose components
│   ├── viewmodels/    # ViewModels
│   ├── states/        # UI state models
│   ├── navigation/    # Navigation logic
│   └── theme/         # Compose theme
│
└── di/               # Dependency injection modules
```

### Data Flow

```
Composable Screen 
    ↓↑
ViewModel 
    ↓↑
UseCase 
    ↓↑
Repository Interface
    ↓↑
Repository Implementation
    ↓↑
Data Source (API/Database)
```

## Package Structure

### Base Package Structure

```
com.example.app/
├── common/           # Shared components
├── features/         # Feature modules
├── widget/           # App widgets
└── AppClass.kt      # Application class
```

### Common Module Organization

```
common/
├── data/
│   ├── api/
│   │   ├── interceptors/    # HTTP interceptors
│   │   ├── clients/         # API client setup
│   │   └── services/        # Retrofit/GraphQL services
│   ├── database/            # Room database setup
│   └── preferences/         # SharedPreferences
│
├── domain/
│   ├── models/              # Shared domain models
│   ├── use_cases/           # Common use cases
│   └── interfaces/          # Common interfaces
│
├── presentation/
│   ├── base/                # Base classes (BaseViewModel)
│   ├── components/          # Reusable Compose components
│   ├── modifiers/           # Custom Compose modifiers
│   ├── utils/               # Compose utilities
│   └── theme/               # Material3 theme definition
│       ├── Color.kt         # Color palette
│       ├── Type.kt          # Typography
│       ├── Shape.kt         # Shapes
│       └── Theme.kt         # Theme composition
│
├── di/                      # Dependency injection
│   ├── modules/             # Dagger/Hilt modules
│   └── qualifiers/          # DI qualifiers
│
└── utils/                   # General utilities
    ├── extensions/          # Kotlin extensions
    ├── constants/           # App constants
    └── helpers/             # Helper classes
```

## Compose UI Structure

### Presentation Layer Organization

```
presentation/
├── screens/                 # Screen-level composables
│   ├── login/
│   │   ├── LoginScreen.kt
│   │   └── LoginViewModel.kt
│   ├── home/
│   │   ├── HomeScreen.kt
│   │   ├── HomeViewModel.kt
│   │   └── components/     # Screen-specific components
│   └── profile/
│       ├── ProfileScreen.kt
│       └── ProfileViewModel.kt
│
├── components/              # Reusable components
│   ├── buttons/
│   │   ├── PrimaryButton.kt
│   │   └── SecondaryButton.kt
│   ├── cards/
│   │   └── ContentCard.kt
│   ├── inputs/
│   │   ├── TextField.kt
│   │   └── PasswordField.kt
│   └── layouts/
│       └── ScaffoldLayout.kt
│
├── navigation/
│   ├── NavGraph.kt         # Navigation graph definition
│   ├── NavHost.kt          # Navigation host setup
│   └── Routes.kt           # Route definitions
│
├── theme/
│   ├── Color.kt            # Color definitions
│   ├── Type.kt             # Typography definitions
│   ├── Shape.kt            # Shape definitions
│   ├── Theme.kt            # Theme composition
│   └── Dimensions.kt       # Spacing and sizing
│
└── utils/
    ├── ComposeExtensions.kt
    └── PreviewUtils.kt
```

### Compose Component Structure

```kotlin
// Examples of sealed classes with polymorphism across ALL layers - NO when expressions anywhere

// === PRESENTATION LAYER ===
sealed class UserProfileState {
    abstract fun render(): @Composable () -> Unit
    
    data object Loading : UserProfileState() {
        override fun render(): @Composable () -> Unit = {
            CircularProgressIndicator()
        }
    }
    
    data class Success(val user: User) : UserProfileState() {
        override fun render(): @Composable () -> Unit = {
            UserContent(user = user)
        }
    }
    
    data class Error(val message: String) : UserProfileState() {
        override fun render(): @Composable () -> Unit = {
            ErrorContent(message = message)
        }
    }
}

// === DOMAIN LAYER ===
sealed class ValidationResult {
    abstract fun handle(): String
    
    data object Valid : ValidationResult() {
        override fun handle(): String = "Success"
    }
    
    data class Invalid(val errors: List<String>) : ValidationResult() {
        override fun handle(): String = errors.joinToString(", ")
    }
}

sealed class BusinessRule {
    abstract fun apply(input: String): ValidationResult
    
    data object EmailRule : BusinessRule() {
        override fun apply(input: String): ValidationResult {
            return if (input.contains("@")) ValidationResult.Valid 
                   else ValidationResult.Invalid(listOf("Invalid email"))
        }
    }
    
    data object PasswordRule : BusinessRule() {
        override fun apply(input: String): ValidationResult {
            return if (input.length >= 8) ValidationResult.Valid 
                   else ValidationResult.Invalid(listOf("Password too short"))
        }
    }
}

// === DATA LAYER ===
sealed class ApiResult<T> {
    abstract suspend fun process(): T
    
    data class Success<T>(val data: T) : ApiResult<T>() {
        override suspend fun process(): T = data
    }
    
    data class Error<T>(val exception: Exception) : ApiResult<T>() {
        override suspend fun process(): T = throw exception
    }
    
    data class Loading<T> : ApiResult<T>() {
        override suspend fun process(): T {
            delay(1000) // Simulate loading
            throw IllegalStateException("Still loading")
        }
    }
}

sealed class DatabaseResult<T> {
    abstract fun map(): T?
    
    data class Found<T>(val data: T) : DatabaseResult<T>() {
        override fun map(): T? = data
    }
    
    data class NotFound<T> : DatabaseResult<T>() {
        override fun map(): T? = null
    }
    
    data class DatabaseError<T>(val error: String) : DatabaseResult<T>() {
        override fun map(): T? = null
    }
}

// Usage examples - NO when expressions anywhere
@Composable
fun userProfileCard(
    state: UserProfileState,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        // Polymorphic dispatch instead of when
        state.render().invoke()
    }
}

// Domain layer usage
class UserValidator {
    fun validateUser(email: String, password: String): List<String> {
        val rules = listOf(BusinessRule.EmailRule, BusinessRule.PasswordRule)
        val inputs = listOf(email, password)
        
        return rules.zip(inputs).map { (rule, input) ->
            rule.apply(input).handle() // Polymorphic dispatch
        }.filter { it != "Success" }
    }
}

// Data layer usage
class UserRepository {
    suspend fun getUser(id: String): User? {
        val apiResult: ApiResult<User> = fetchFromApi(id)
        val dbResult: DatabaseResult<User> = fetchFromDatabase(id)
        
        return try {
            apiResult.process() // Polymorphic dispatch
        } catch (e: Exception) {
            dbResult.map() // Polymorphic dispatch
        }
    }
}
```

### Resource Management in Compose

```
res/
├── drawable/                # Vector drawables and images
│   └── ic_*.xml            # Icons
│
├── values/
│   ├── strings.xml         # String resources
│   └── themes.xml          # Android theme (for activities)
│
├── font/                    # Font files
│   └── *.ttf
│
└── raw/                     # Raw resources (JSON, etc.)
```

### Naming Conventions

- **Screens**: `<Feature>Screen.kt`
  - `LoginScreen.kt`
  - `ProfileScreen.kt`
  - `SettingsScreen.kt`

- **Components**: `<Type><Description>.kt`
  - `PrimaryButton.kt`
  - `UserCard.kt`
  - `LoadingIndicator.kt`

- **ViewModels**: `<Feature>ViewModel.kt`
  - `LoginViewModel.kt`
  - `ProfileViewModel.kt`

- **States**: `<Feature>UiState.kt`
  - `LoginUiState.kt`
  - `ProfileUiState.kt`

## Dependency Injection Structure

Using Hilt/Dagger for dependency injection:

```
di/
├── modules/
│   ├── AppModule.kt         # App-level dependencies
│   ├── NetworkModule.kt     # Network dependencies
│   ├── DatabaseModule.kt    # Database dependencies
│   └── RepositoryModule.kt  # Repository bindings
│
├── qualifiers/
│   ├── ApiUrl.kt           # API URL qualifier
│   └── IoDispatcher.kt     # Coroutine dispatcher
│
└── scopes/
    └── ActivityScope.kt     # Custom scopes
```

## Testing Structure

```
app/
├── src/test/java/          # Unit tests
│   ├── [package]/
│   │   ├── domain/         # Domain layer tests
│   │   ├── data/           # Data layer tests
│   │   └── presentation/   # ViewModel tests
│   └── utils/              # Test utilities
│
└── src/androidTest/java/   # Instrumentation tests
    ├── [package]/
    │   ├── compose/        # Compose UI tests
    │   └── integration/    # Integration tests
    └── utils/              # Android test utilities
```

## Key Architectural Principles

### 1. **DRY (Don't Repeat Yourself)**
- Eliminate code duplication through abstraction
- Extract common functionality into reusable components
- Use base classes, extension functions, and shared utilities
- Create reusable Composables for common UI patterns

### 2. **SOLID Principles**

#### **S** - Single Responsibility Principle
- Each class has one reason to change
- Use cases handle one specific business operation
- ViewModels manage UI state for one screen
- Components have a single, well-defined purpose

#### **O** - Open/Closed Principle
- Classes open for extension, closed for modification
- Use interfaces and abstract classes for extensibility
- Leverage polymorphism for behavior variation

#### **L** - Liskov Substitution Principle
- Derived classes must be substitutable for base classes
- Interface implementations must honor contracts
- Maintain behavioral consistency in inheritance hierarchies

#### **I** - Interface Segregation Principle
- Many client-specific interfaces better than one general-purpose interface
- Split large interfaces into smaller, focused ones
- Clients should not depend on interfaces they don't use

#### **D** - Dependency Inversion Principle
- High-level modules don't depend on low-level modules
- Both depend on abstractions (interfaces)
- Repository pattern for data access abstraction
- Use Hilt for dependency injection throughout the app

### 3. **Hilt Dependency Injection**
- Use `@HiltViewModel` for all ViewModels
- Inject dependencies through constructors, not manual instantiation
- Create `@Module` classes for dependency provision
- Use `@Binds` for interface implementations
- Leverage `@Provides` for complex object creation
- Apply proper scoping (`@Singleton`, `@ActivityScope`, etc.)

### 4. **Separation of Concerns**
- Each layer has a specific responsibility
- Dependencies flow inward (Presentation → Domain → Data)
- Domain layer contains pure business logic (no Android dependencies)

### 5. **Feature Isolation**
- Features are self-contained modules
- Minimal dependencies between features
- Shared functionality in common module

### 6. **Testability**
- Each layer can be tested independently
- Use interfaces for mocking dependencies
- ViewModels are tested with unit tests
- UI is tested with instrumentation tests

## Best Practices

### Code Organization
1. Keep related files together (feature-based organization)
2. Use consistent naming conventions
3. Minimize cross-feature dependencies
4. Extract common functionality to shared modules

### Architecture Guidelines
1. **DRY Implementation:**
   - Extract duplicate code into extension functions or utility classes
   - Create base ViewModels for common functionality
   - Use sealed classes for consistent state management patterns
   - Build reusable Composable components with proper parameter abstraction

2. **SOLID Implementation:**
   - Keep classes focused on single responsibilities
   - Use interface abstractions for all repository and use case dependencies
   - Design for extension through composition over inheritance
   - Apply dependency inversion consistently across all layers

3. **Hilt Integration:**
   - All ViewModels must use `@HiltViewModel` annotation
   - Inject all dependencies through constructors
   - Repository interfaces belong in domain layer, implementations in data layer
   - Use `@Binds` in abstract modules for interface-to-implementation binding
   - Apply appropriate scopes (`@Singleton` for app-wide, `@ViewModelScoped` for ViewModel dependencies)

4. **Sealed Classes Instead of When Expressions (ALL LAYERS):**
   - **MANDATORY:** Use sealed classes with polymorphism instead of when expressions throughout the entire codebase
   - Apply this pattern in ALL layers: Presentation, Domain, and Data
   - Each sealed class subtype implements its own behavior through method overrides
   - Eliminates the need for when statements by using object-oriented dispatch
   - Provides better encapsulation and follows the Open/Closed principle
   - Examples across layers:
     - **Presentation:** `UiState.render()`, `UserAction.execute()`
     - **Domain:** `ValidationResult.handle()`, `BusinessRule.apply()`
     - **Data:** `ApiResult.process()`, `DatabaseResult.map()`, `NetworkError.recover()`

5. **Clean Architecture:**
   - ViewModels should not reference Android framework classes (except AndroidViewModel)
   - Use cases should be single-purpose and testable
   - Data models (DTOs) should be separate from domain models
   - UI models should be separate from domain models when needed
   - Domain layer must have no Android dependencies

6. **Compose Guidelines:**
   - Compose UI should be stateless with state hoisted to ViewModels
   - Use `remember` and `rememberSaveable` appropriately
   - Prefer smaller, focused composables over large ones
   - Extract common UI patterns into reusable components

### Code Quality Standards
1. **Null Safety Policy:**
   - **MANDATORY:** All code must be null-safe using Kotlin's null safety features
   - **ZERO TOLERANCE:** No force unwrapping (`!!`) operators anywhere in the codebase
   - **Required Patterns:**
     ```kotlin
     // ✅ CORRECT - Safe null handling
     albumArt?.let { art -> 
         Image(bitmap = art.asImageBitmap())
     } ?: run {
         Icon(imageVector = Icons.Default.MusicNote)
     }
     
     // ✅ CORRECT - Safe calls and Elvis operator
     val result = data?.process() ?: defaultValue
     
     // ❌ WRONG - Force unwrapping
     val result = data!!.process()
     ```
   - **Safe Navigation:** Always use `?.` for nullable properties and method calls
   - **Elvis Operator:** Use `?:` for providing fallback values
   - **Let/Run Blocks:** Use `let`, `run`, `also`, `apply` for safe null handling
   - **Scope Functions:** Prefer `let` for null-safe transformations, `run` for fallback blocks
   - **Enforcement:** Code reviews must catch and reject any force unwrapping operators
   - **Exception:** Only in cases where null is impossible and well-documented

2. **NO When Expressions Policy:**
   - **ZERO TOLERANCE:** No when expressions anywhere in the codebase
   - All conditional logic must use sealed classes with polymorphic dispatch
   - Apply across ALL layers: Presentation, Domain, Data
   - Use abstract methods that each sealed subtype implements
   - Examples: `State.render()`, `Result.process()`, `Action.execute()`, `Rule.apply()`

3. **Resource and Styling:**
   - Use `stringResource()` for all user-facing text
   - Define dimensions in theme for consistent spacing
   - Use Material3 theme for consistent styling
   - Implement proper preview annotations for development

4. **Hardcoded Strings Policy:**
   - **ZERO TOLERANCE:** No hardcoded strings anywhere in the UI layer
   - ALL user-facing text must use `stringResource(R.string.*)` calls
   - This includes:
     - Display text, labels, titles, messages
     - Error messages and status text
     - Placeholder text and hints
     - Content descriptions for accessibility
     - Button text and menu items
   - **Exception:** Debug/logging strings and internal constants are acceptable
   - **Enforcement:** Code reviews must catch and reject hardcoded strings
   - **Migration:** Any existing hardcoded strings must be moved to strings.xml immediately

5. **Localization Policy:**
   - **MANDATORY:** All new user-facing strings must be fully localized.
   - When a new string is added to the default `values/strings.xml` file, it **must** also be added to all translation files.
   - **Supported Languages:**
     - German (`values-de`)
     - Spanish (`values-es`)
     - French (`values-fr`)
     - Italian (`values-it`)
     - Polish (`values-pl`)
     - Portuguese (`values-pt`)
     - Russian (`values-ru`)
     - Serbian (`values-sr`)
     - Turkish (`values-tr`)
   - **Enforcement:** Code reviews must verify that new strings are present in all listed `strings.xml` files. Pull requests missing translations will be rejected.

6. **String Constants Policy:**
   - **ZERO TOLERANCE:** No hardcoded strings for keys, identifiers, or internal values
   - ALL non-UI strings (savedStateHandle keys, intent extras, preference keys, etc.) must be defined as constants
   - Create dedicated constant objects/files for different categories:
     - Navigation constants: `NavigationConstants.kt` for savedStateHandle keys, route parameters
     - Preference constants: `PreferenceConstants.kt` for SharedPreferences keys
     - Intent constants: `IntentConstants.kt` for intent extras and actions
     - Database constants: `DatabaseConstants.kt` for column names, table names
   - **Examples:**
     ```kotlin
     // Good - using constants
     navController.savedStateHandle?.set(NavigationConstants.KEY_SONG_ID, songId)
     preferences.getString(PreferenceConstants.KEY_USER_THEME, "default")
     
     // Bad - hardcoded strings
     navController.savedStateHandle?.set("song_id", songId)
     preferences.getString("user_theme", "default")
     ```
   - **Enforcement:** Code reviews must catch and reject hardcoded string identifiers
   - **Organization:** Group related constants in dedicated objects within appropriate packages

7. **Firebase Analytics Constants Policy:**
   - **MANDATORY:** ALL Firebase Analytics implementations must use centralized constants
   - **File Location:** `/app/src/main/java/soly/lyricsgenerator/analytics/AnalyticsConstants.kt`
   - **ZERO TOLERANCE:** No hardcoded analytics strings anywhere
   - **Required Pattern:**
     ```kotlin
     // ✅ CORRECT - Always use constants
     analytics.logEvent(AnalyticsConstants.Events.MUSIC_PLAY_SONG) {
         param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
         param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.SONG_ITEM_CLICK)
     }
     
     // ❌ WRONG - Never use hardcoded strings
     analytics.logEvent("music_play_song") {
         param("song_id", song.id.toString())
         param("source", "song_item_click")
     }
     ```
   - **Constants Organization:**
     - `Events` - All event names (50+ constants)
     - `Params` - All parameter names (30+ constants)
     - `ScreenNames` - Screen identifiers for screen_view events
     - `ErrorMessages` - Standardized error messages
   - **Benefits:** Consistency, maintainability, no typos, IDE autocomplete, refactoring safety
   - **Enforcement:** Code reviews must reject any hardcoded analytics strings
   - **Standard Events:** Use `FirebaseAnalytics.Event.SCREEN_VIEW` instead of `"screen_view"`

8. **State and Lifecycle:**
   - Handle configuration changes with `rememberSaveable`
   - Use `LaunchedEffect` and `DisposableEffect` correctly
   - Hoist state to ViewModels using sealed classes with polymorphic behavior

### Testing Strategy
1. **Sealed Class Testing:**
   - Test each sealed class subtype's behavior methods independently
   - Verify polymorphic dispatch works correctly across all layers
   - No when expression testing needed - test the abstract method implementations
   
2. **Layer Testing:**
   - Unit test domain layer extensively with sealed class behavior verification
   - Test ViewModels with mock repositories and test dispatchers
   - Use Compose testing APIs for UI tests with sealed state testing
   - Test composables in isolation with `createComposeRule()`
   - Use semantics for accessible and testable UI
   - Write integration tests for critical user flows
   
3. **Code Quality Verification:**
   - Ensure NO when expressions exist in any test code
   - Use sealed classes for test result states and assertions
   - Apply polymorphic patterns in test utilities and helpers

## Module Dependencies

```
app (module)
  └── common
      └── features/
          ├── feature_a
          ├── feature_b
          └── feature_c
```

Features can depend on common module but not on each other, ensuring loose coupling and high cohesion.

---

This structure promotes:
- **Scalability**: Easy to add new features
- **Maintainability**: Clear separation of concerns
- **Testability**: Each component can be tested in isolation
- **Team Collaboration**: Multiple developers can work on different features simultaneously
- **Code Reusability**: Common components can be shared across features