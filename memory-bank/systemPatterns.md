# System Patterns

## Architecture

-   **MVVM (Model-View-ViewModel):** The primary architectural pattern used, separating UI (Views/Composables), state and UI logic (ViewModels), and data/business logic (Model - Repositories, UseCases, Services).
-   **Clean Architecture Principles:** Applied with MVVM, aiming for separation of concerns between UI, Domain, and Data layers.
-   **Single Activity Architecture:** Uses a single `MainActivity` hosting Jetpack Compose screens.

## Reference Documentation

This project follows standard Android architectural patterns documented in:
- `@memory-bank/ANDROID_PROJECT_STRUCTURE.md` - Comprehensive guide for Clean Architecture + MVVM with Jetpack Compose structure

## Key Technical Decisions

-   **Jetpack Compose:** Used for building the UI.
-   **Jetpack Navigation Component:** Used for navigating between composable screens.
    -   Custom horizontal slide transitions implemented between `MusicScreen` and `SettingsScreen`.
-   **Hilt:** Used for dependency injection.
-   **Room:** Used for local database storage (likely for saved LRC files and metadata).
-   **Foreground Service (`MusicPlayerService`):** Manages background music playback and state persistence. Implements Android 12+ compliance with smart service start logic (`startForegroundService()` vs `startService()`) and proper foreground notification management to prevent `BackgroundServiceStartNotAllowedException`.
-   **BroadcastReceiver:** Used for communication between the `MusicPlayerService` and UI components (ViewModels).
-   **MediaStore API:** Used to fetch audio files from the device.
-   **File Handling for User-Selected Files (e.g., LRC import):** Files selected by the user via `content://` URIs (e.g., through Storage Access Framework or `ActivityResultContracts.GetContent()`) are copied to the app's internal storage (e.g., `filesDir`). The absolute path to this internal copy is then stored and used within the app for reliable access. This avoids issues with direct content URI permissions and lifecycle.
-   **Audio File Access with Dual-Approach Fallback:** For music playback, the app uses a resilient dual-approach:
    1. **Primary**: Direct file path access (backward compatibility for older Android versions)
    2. **Fallback**: Content URI construction using `ContentUris.withAppendedId()` with `MediaStore.Audio.Media.EXTERNAL_CONTENT_URI` (required for Android 10+ scoped storage)
    - This pattern prevents permission denied crashes and ensures compatibility across all Android versions.

## Design Patterns

-   **Repository Pattern:** Used in the data layer to abstract data sources.
-   **UseCase Pattern:** Used in the domain layer to encapsulate specific business logic.
-   **StateFlow/SharedFlow:** Used extensively in ViewModels to expose UI state and one-time events to the UI layer in a lifecycle-aware manner.
-   **Observer Pattern:** Implicitly used via `StateFlow`/`SharedFlow` collection.
-   **Firebase Analytics:** Integrated for tracking screen views and key user events across multiple features (including navigation, settings, file actions, and the create workflow). **MANDATORY:** All analytics implementations must use centralized constants from `AnalyticsConstants.kt` - no hardcoded strings allowed.
-   **Service-Hosted Compose View:** For UI that must exist outside of an `Activity` or `Fragment` (e.g., a floating overlay window), a custom `ServiceLifecycleOwner` class is used. This class implements `LifecycleOwner`, `ViewModelStoreOwner`, and `SavedStateRegistryOwner` to provide a stable, self-contained lifecycle for a `ComposeView` running within a `Service` context. This prevents crashes related to incorrect lifecycle state.
-   **Database Optimization Patterns:**
    -   **Batch Query Pattern:** Used in MusicRepository to eliminate N+1 query problems by collecting IDs first, then performing single batch queries with IN clauses
    -   **Two-Pass Data Processing:** Separate data collection and processing phases to optimize database access patterns
    -   **Map-based Caching:** Use Room's @MapInfo annotation for type-safe batch lookups with O(1) access time
-   **Android 12+ Service Compliance Patterns:**
    -   **Smart Service Start Logic:** Intelligent selection between `startService()` and `startForegroundService()` based on action type and Android version
    -   **Action Classification Pattern:** Categorize service actions into foreground-required vs regular operations
    -   **Temporary Notification Pattern:** Create immediate temporary notifications for foreground services to satisfy 5-second rule
    -   **Graceful Background Restriction Handling:** Comprehensive exception handling for `BackgroundServiceStartNotAllowedException`
    -   **Cross-Version Compatibility:** Maintain functionality across all Android versions while respecting modern restrictions

## Embedded Lyrics Integration Pattern

**Lesson Learned:** When implementing features that extend existing functionality, follow the established architectural boundaries strictly:

**✅ CORRECT APPROACH - Domain Layer Integration:**
- Create dedicated UseCase (`GetEmbeddedLyricsUseCase`) for embedded lyrics extraction
- UseCase handles format detection (LRC vs plain text) and creates appropriate `FileContentState`
- ViewModel integrates UseCase as fallback mechanism when linked files not found
- UI remains completely unaware of lyrics source through polymorphic FileContentRenderer

**❌ WRONG APPROACH - UI Layer Modifications:**
- Never modify UI components (SongDetailsScreen) to handle different data sources
- Avoid creating custom UI components when existing ones can be reused
- Don't mix business logic (format detection) with presentation logic

**Key Pattern:**
```
UI Layer: FileContentRenderer (unchanged)
    ↓↑
ViewModel: Coordinates linked files + embedded fallback
    ↓↑  
Domain: GetEmbeddedLyricsUseCase (new) + existing linked file logic
    ↓↑
Data: AudioTagger (existing)
```

**Benefits:**
- UI ignorance: SongDetailsScreen doesn't know lyrics source
- Consistent UX: Same rendering components for all lyrics
- Clean separation: Business logic in domain, coordination in ViewModel
- Extensible: Easy to add more lyrics sources in future

## Critical Development Patterns & Lessons Learned

### 🚨 **CRITICAL PATTERN: Android Overlay Window Height Management**

**Problem:** WindowManager overlay windows with dynamic content cause visible resizing during runtime, breaking user experience.

**Root Cause:** TWO-LEVEL sizing issue:
1. **WindowManager Level:** Dynamic height recalculation during playback 
2. **Jetpack Compose Level:** Intrinsic content sizing within fixed window

**Lesson Learned:** 
> Ali problem je što se Jetpack Compose UI još uvek INTRINSIČNO resajzuje unutar tog fiksnog prozora. Overlay je 847px visok, ali sadržaj se skuplja/širi na osnovu broja vidljivih linija.

**Complete Solution Pattern - DUAL-LEVEL HEIGHT FIXING:**

**Level 1: WindowManager Flag System**
```kotlin
// Prevent WindowManager height recalculation during same song
private var heightCalculatedForSongId: Long? = null

internal fun updateOverlayHeightForNewSong(song: Song) {
    // CRITICAL: Block recalculation during same song playback
    if (heightCalculatedForSongId == song.id) {
        Timber.tag("DEBUG_FLOW").d("Height already calculated - SKIPPING to prevent resizing")
        return
    }
    
    // Calculate once and mark as complete
    val newOptimalHeight = calculateSongSpecificOverlayHeight(song)
    updateOverlayHeight(newOptimalHeight)
    heightCalculatedForSongId = song.id // PREVENTS further recalculations
}
```

**Level 2: Compose UI Force Height**
```kotlin
// Force Compose UI to use full available height - prevent intrinsic resizing
Box(
    modifier = Modifier
        .fillMaxWidth()
        .fillMaxHeight(), // CRITICAL: Forces full height usage!
    contentAlignment = Alignment.Center
) {
    // Content here will not shrink based on visible items
}
```

**Key Implementation Requirements:**
1. **Calculate height ONCE per song** at start of playback
2. **Block all recalculation attempts** during same song with flag system
3. **Force Compose UI** to use full calculated height with `.fillMaxHeight()`
4. **Reset flag only** when song changes or overlay recreated

**Debugging Pattern:**
- Look for TWO separate sizing behaviors: WindowManager vs Compose UI
- Add flag-based blocking for service-level height updates
- Force fixed height constraints in Compose UI layer
- Never assume fixing one level solves both problems

**Benefits:**
- Eliminates visible overlay resizing during playback
- Maintains consistent user experience regardless of content
- Prevents both WindowManager and Compose UI dynamic sizing
- Provides proper separation of window vs content sizing concerns

### Time-based Logic Best Practices
-   **Unit Consistency Rule:** Always maintain consistent time units throughout the entire data flow (milliseconds vs seconds)
-   **Variable Naming Convention:** Use descriptive names that include the unit (e.g., `positionMilliseconds` vs `positionSeconds`)
-   **Documentation Requirement:** Comment expected time units in function parameters and return values
-   **Validation Pattern:** Add debug logging at critical time conversion/comparison points
-   **Real Data Testing:** Test time-based features with actual music files and realistic timestamps, not just dummy data

### "Data Loads But Doesn't Display" Debugging Pattern
-   **Common Root Cause:** Selection/filtering/matching logic fails silently while data loading succeeds
-   **Debugging Approach:**
    1. Verify data is loaded correctly (✓)
    2. Add logging to selection/matching criteria
    3. Check for unit mismatches or type conversions
    4. Validate edge cases with real data
-   **Prevention:** Add comprehensive logging to matching/filtering logic from the start

### Critical Bug Prevention
-   **Subtle Logic Errors:** The most dangerous bugs are ones where 99% of the code works correctly - they're hardest to spot
-   **Type Safety for Units:** Consider wrapper classes or type aliases for different time units to prevent mixing
-   **Edge Case Validation:** Always test with realistic production data, not just simple test cases

## Interactive UI Component Patterns

### 🚀 **Pattern: Interactive Scrollbar Implementation**

**Successfully Applied:** InteractiveScrollbar component for MusicScreen (January 2025)

**Key Implementation Principles:**
```kotlin
// ✅ CORRECT - Sealed classes for state management
sealed class ScrollbarState {
    abstract fun shouldShow(): Boolean
    abstract fun getAlpha(): Float
    
    data object Hidden : ScrollbarState() {
        override fun shouldShow(): Boolean = false
        override fun getAlpha(): Float = 0f
    }
    
    data object Visible : ScrollbarState() {
        override fun shouldShow(): Boolean = true  
        override fun getAlpha(): Float = 1f
    }
}

// ✅ CORRECT - No when expressions, polymorphic dispatch
val scrollbarVisible by remember {
    derivedStateOf { 
        scrollbarState.shouldShow() && itemCount > 0 && 
        (listState.layoutInfo.totalItemsCount > listState.layoutInfo.visibleItemsInfo.size)
    }
}
```

**Architecture Pattern:**
```
LazyColumn (scrollable content)
    ↓↑ 
Box (container)
    ↓↑
InteractiveScrollbar (overlay component)
    ↓↑
ScrollbarState & DragState (sealed classes)
```

**Critical Success Factors:**
1. **State Management:** Use sealed classes with abstract methods instead of when expressions
2. **Performance:** Use `derivedStateOf` for expensive calculations that depend on state
3. **Coroutine Management:** Proper job cancellation for hide delays and drag operations
4. **Touch Handling:** `detectDragGestures` for interactive thumb dragging
5. **Animation:** `AnimatedVisibility` with fade in/out for smooth UX
6. **Responsive Design:** Dynamic thumb sizing based on content ratio

**Layout Pattern:**
```kotlin
Box(modifier = Modifier.fillMaxWidth().weight(1f)) {
    LazyColumn(state = listState, modifier = Modifier.fillMaxSize()) {
        // Content items
    }
    
    InteractiveScrollbar(
        listState = listState,
        itemCount = itemCount,
        hideDelayMs = 1500L
    )
}
```

**Benefits Achieved:**
- Smooth scrolling navigation for large lists (300+ items)
- Non-intrusive UI that appears only when needed
- Maintains Clean Architecture with sealed class polymorphism
- Zero performance impact when scrollbar is hidden
- Material 3 design compliance
- Seamless integration with existing filtering systems

**Reusability:** This pattern can be applied to any LazyColumn or LazyRow component that needs enhanced navigation for large datasets.

### 🚨 **Critical Pattern: MediaStore File Modification (Android R+)**

**Problem:** Android R+ requires specific MediaStore URIs for `MediaStore.createWriteRequest()`, not file:// URIs.

**Root Cause:** `MediaStore.createWriteRequest()` fails with "All requested items must be referenced by specific ID" when using file:// URIs.

**Solution Pattern:**
```kotlin
// ❌ WRONG: Using file URI
val fileUri = Uri.fromFile(File(filePath))
MediaStore.createWriteRequest(contentResolver, listOf(fileUri)) // FAILS

// ✅ CORRECT: Using MediaStore URI  
val mediaStoreUri = ContentUris.withAppendedId(
    MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, 
    songId
)
MediaStore.createWriteRequest(contentResolver, listOf(mediaStoreUri)) // WORKS
```

**Key Implementation Details:**
- **MediaStore URI Format:** `content://media/external/audio/media/{id}` not `file:///storage/...`
- **Song ID Requirement:** Must use the MediaStore database ID, not file path
- **URL Decoding:** Handle navigation-encoded paths (spaces as %20 or +) before file operations
- **Permission Strategy:** Follows app policy - no dangerous storage permissions, MediaStore API only

**Debugging Approach:**
1. Check if using proper MediaStore URI format
2. Verify Song ID is correct MediaStore database ID  
3. Add logging for URI format and song data availability
4. Test with actual song data, not dummy paths

**Prevention:** Always use `song.mediaStoreUri` for MediaStore operations, never construct URIs from file paths.

## Component Relationships

-   `MainActivity` hosts the `NavHost` and manages top-level UI elements like the `TopAppBar` (conditionally shown/configured for Music, Files, PasteLyrics screens) and `BottomNavigationBar`.
-   `NavGraph` defines the navigation routes and transitions between composable screens.
-   Composable screens (`MusicScreen`, `CreateScreen`, `FilesScreen`, `SongDetailsScreen`, `SettingsScreen`, etc.) represent individual UI destinations.
-   `ViewModels` (`MusicViewModel`, `SharedViewModel`, etc.) provide state to screens and handle user interactions, often interacting with UseCases or Repositories.
-   `MusicPlayerService` runs in the background, manages `MediaPlayer`, handles song loading via `MediaStore`, and communicates state changes back to the UI via broadcasts.
-   `MusicBroadcastReceiver` listens for broadcasts from the service and updates the `MusicViewModel`.
-   UI components like `MusicScreen` observe `ViewModel` state (`uiState`) and send user interaction events to the `ViewModel`. For playback controls within a `SongItem`, `MusicScreen` now uses `uiState.currentPosition` to decide whether to issue a `playSong(song)` or `resumePlaying()` command to the `ViewModel`, making it more resilient to `MusicPlayerService` state resets.

## Error Handling Patterns

### 🚨 **Critical Pattern: Audio File Access with Permission Fallback**

**Problem:** Android 10+ scoped storage restrictions cause `EACCES (Permission denied)` errors when accessing audio files via direct file paths.

**Solution:** Dual-approach fallback pattern in `MusicPlayer.play()`:

```kotlin
try {
    // Primary: Direct file path (backward compatibility)
    setDataSource(song.data)
} catch (e: Exception) {
    // Fallback: Content URI approach
    val contentUri = ContentUris.withAppendedId(
        MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
        song.id
    )
    setDataSource(context, contentUri)
}
```

**Key Principles:**
- **Always try direct path first** for backward compatibility
- **Automatic fallback to content URI** using song ID from MediaStore
- **Comprehensive error logging** for debugging permission issues
- **Throw descriptive SecurityException** if both approaches fail

### **Service-Level Resilience Pattern**

**Implementation in `MusicPlayerService.playSong()`:**

```kotlin
try {
    musicPlayer.play(song)
    // ... success handling
} catch (e: SecurityException) {
    // Permission denied - try next song
    abandonAudioFocus()
    sendPlaybackErrorBroadcast(song, "Permission denied")
    playNextSong() // Continue operation
} catch (e: Exception) {
    // Other errors - try next song
    sendPlaybackErrorBroadcast(song, "Playback error: ${e.message}")
    playNextSong()
}
```

**Key Principles:**
- **Never crash the service** - always catch and handle errors
- **Maintain audio focus consistency** - abandon focus on failures
- **Graceful degradation** - skip problematic songs and continue
- **User feedback** - broadcast error information for UI handling
- **Operational continuity** - attempt to play next available song

### **Error Broadcasting Pattern**

**Components:**
- `ACTION_PLAYBACK_ERROR` broadcast action
- `EXTRA_ERROR_MESSAGE` for detailed error information  
- `MusicBroadcastReceiver` handles error states
- Update UI flows with error state management

**Usage:**
```kotlin
// Service sends error
sendPlaybackErrorBroadcast(song, "Permission denied: Unable to access audio file")

// Receiver handles error  
MusicPlayerService.ACTION_PLAYBACK_ERROR -> {
    val errorMessage = intent.getStringExtra(MusicPlayerService.EXTRA_ERROR_MESSAGE)
    _errorMessageFlow?.value = errorMessage
    isPlayingFlow.value = false
}
```

**Benefits:**
- Prevents app crashes from permission issues
- Maintains user experience with automatic fallbacks
- Provides debugging information for development
- Ensures compatibility across all Android API levels 