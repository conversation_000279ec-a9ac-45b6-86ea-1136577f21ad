# Product Context

## Purpose

The Lyrics Generator app allows users to listen to music available on their device and create/manage synchronized lyrics files (.lrc format) for those songs.

## Problems Solved

-   Provides a way to view and play local music files.
-   Enables users to create time-synced lyrics for songs, enhancing the listening experience (e.g., for karaoke or just following along).
-   Manages the created .lrc files, linking them to the corresponding audio files.

## Core Functionality

-   **Music Playback:** Browse and play music from the device's MediaStore.
-   **LRC Creation:** A dedicated workflow allows users to select a song and a text file (or paste text) and synchronize the lyrics line-by-line with the music playback using tapping, seeking, and fine-tuning controls.
-   **LRC Management:** Import, view, export, and delete .lrc files associated with songs.
-   **Lyrics Display:** Show synchronized lyrics during playback in both the creation preview and the main player view.
-   **Settings:** (Newly added) Provides a dedicated screen for application settings, accessed via a menu in the Music screen.

## User Experience Goals

-   Intuitive navigation between music playback, lyrics creation, and file management.
-   A straightforward and accurate process for synchronizing lyrics.
-   Seamless music playback continuity when navigating between different sections of the app.
-   Clear visual feedback during lyric synchronization and playback. 