# Tech Context

## Core Technologies

-   **Programming Language:** Kotlin
-   **UI Toolkit:** Jetpack Compose
-   **Architecture:** MVVM, Clean Architecture principles
-   **Navigation:** Jetpack Navigation Component (Compose)
-   **Dependency Injection:** Hilt
-   **Asynchronous Programming:** Kotlin Coroutines, Flow (StateFlow, SharedFlow)
-   **Database:** Room Persistence Library
-   **Background Execution:** Android Foreground Service
-   **Media:** MediaStore API (for accessing local audio files), Android MediaPlayer (likely used within the service)
-   **Analytics:** Firebase Analytics (with centralized constants pattern)

## Development Setup

-   **IDE:** Android Studio / Cursor
-   **Build System:** Gradle
-   **Version Control:** Git (assumed)

## Build Configuration & Security

### ProGuard/R8 Minification (Release Builds)
-   **Enabled:** `isMinifyEnabled = true` with `isShrinkResources = true`
-   **Configuration:** Uses `proguard-android-optimize.txt` + custom `proguard-rules.pro`
-   **Performance Impact:** 61.7% APK size reduction (21.7MB → 8.3MB)
-   **Security Features:**
    -   Class/method name obfuscation with package repackaging
    -   Dead code elimination and resource shrinking
    -   Complete logging removal in release builds (Android Log + Timber)
    -   Debug symbols preserved for crash reporting (line numbers + source files)
-   **Framework Compatibility:** Custom rules for Jetpack Compose, Room, Hilt, Firebase, Navigation
-   **Mapping Files:** Generated for crash deobfuscation (mapping.txt, configuration.txt, usage.txt)

### String Internationalization
-   **Languages Supported:** 9 languages (EN, DE, ES, FR, IT, PL, PT, RU, SR, TR)
-   **Implementation:** All user-facing strings use `stringResource(R.string.*)` pattern
-   **Coverage:** 28+ internationalized string resources including dialogs, buttons, status messages, content descriptions
-   **Quality:** Context-appropriate translations following Android UI conventions
-   **Parameter Support:** Proper string formatting (%1$s, %1$d) maintained across all languages
-   **Logging:** Timber
-   **Testing Commands:**
    -   Run specific UI tests (e.g., `MainActivityBasicUITest`) ensuring the app remains installed afterward (permissions handled via `GrantPermissionRule` in the test itself for `POST_NOTIFICATIONS` and `READ_MEDIA_AUDIO`):
        ```bash
        ./gradlew :app:connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=soly.lyricsgenerator.MainActivityBasicUITest && ./gradlew :app:installDebug
        ```
-   **Key Testing Dependencies:**
    -   `androidx.test:rules:1.5.0` (for `GrantPermissionRule`)

## Technical Constraints

-   Requires specific Android permissions:
    -   `READ_MEDIA_AUDIO` (Android 13+) or `READ_EXTERNAL_STORAGE` (older versions) for accessing music files.
    -   `POST_NOTIFICATIONS` (Android 13+) for showing playback notifications.
    -   `ACCESS_MEDIA_LOCATION` for accessing location metadata from media files.

## ⚠️ CRITICAL: Storage Permission Policy

**THE APP MUST NEVER USE `MANAGE_EXTERNAL_STORAGE` PERMISSION!**

This is a hard requirement due to Google Play Store policies. The app uses privacy-respecting alternatives:

1. **Internal Storage** (`context.filesDir`) - For saving and managing LRC files
2. **Storage Access Framework (SAF)** - For file export functionality (user chooses location)
3. **MediaStore API** - For accessing music files with granular permissions

**Why this matters:**
- Google Play strictly limits apps that use `MANAGE_EXTERNAL_STORAGE` 
- The app would require special approval and justification
- All current app functionality works perfectly without this permission
- Using this permission would violate privacy best practices

**If tempted to add this permission:** DON'T! Find an alternative approach using SAF, MediaStore, or internal storage.

## ⚠️ IMPORTANT: Video Permission Policy

**THE APP MUST NOT USE `READ_MEDIA_VIDEO` PERMISSION!**

This app is focused solely on audio/music functionality. The video export feature was planned but has been completely disabled:

- VideoExporter.kt utility class is entirely commented out
- All video export UI elements are commented out  
- FFmpeg dependency is commented out
- No active code accesses video files

**Current app functionality is audio-only:**
- Browse and play local music files
- Create synchronized lyrics (LRC files) 
- Import and manage existing LRC files
- View synchronized lyrics during music playback

**Do not add video functionality** without careful consideration of Google Play policies for media permissions.
-   Relies on device's `MediaStore` for song discovery.
-   Compatibility needed across different Android versions (handling permission differences, API level checks).
-   **Localization:** Supports multiple languages via Android resource qualifiers (`values-<lang_code>`). Currently includes English (default), Spanish, Portuguese, German, French, Russian, Turkish, Italian, Polish, and Serbian.

## Key Dependencies (Inferred)

-   `androidx.core:core-ktx`
-   `androidx.lifecycle:lifecycle-runtime-ktx`
-   `androidx.activity:activity-compose`
-   `androidx.compose.ui:ui`
-   `androidx.compose.ui:ui-graphics`
-   `androidx.compose.ui:ui-tooling-preview`
-   `androidx.compose.material3:material3`
-   `androidx.navigation:navigation-compose`
-   `com.google.dagger:hilt-android`
-   `androidx.hilt:hilt-navigation-compose`
-   `androidx.room:room-runtime`
-   `androidx.room:room-ktx`
-   `com.jakewharton.timber:timber`
-   `org.jetbrains.kotlinx:kotlinx-coroutines-android`
-   `com.google.firebase:firebase-bom` (Bill of Materials)
-   `com.google.firebase:firebase-analytics`

## 🔥 MANDATORY: Firebase Analytics Constants Pattern

**ALL Firebase Analytics implementations MUST use centralized constants!**

### Analytics Constants File
**Location:** `/app/src/main/java/soly/lyricsgenerator/analytics/AnalyticsConstants.kt`

### Required Usage Pattern

```kotlin
// ✅ CORRECT - Always use constants
analytics.logEvent(AnalyticsConstants.Events.MUSIC_PLAY_SONG) {
    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
    param(AnalyticsConstants.Params.SONG_TITLE, song.title)
    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.SONG_ITEM_CLICK)
}

// ❌ WRONG - Never use hardcoded strings
analytics.logEvent("music_play_song") {
    param("song_id", song.id.toString())
    param("song_title", song.title) 
    param("source", "song_item_click")
}
```

### Constants Organization
**AnalyticsConstants.kt** contains 4 objects:

1. **`Events`** - All Firebase Analytics event names (50+ constants)
2. **`Params`** - All parameter names (30+ constants) 
3. **`ScreenNames`** - Screen identifiers for screen_view events
4. **`ErrorMessages`** - Standardized error messages for analytics

### Adding New Analytics Events

**Step 1:** Add constants to `AnalyticsConstants.kt`
```kotlin
object Events {
    const val NEW_FEATURE_USED = "new_feature_used"
}

object Params {
    const val FEATURE_TYPE = "feature_type" 
    const val USER_ACTION = "user_action"
}
```

**Step 2:** Use constants in implementation
```kotlin
analytics.logEvent(AnalyticsConstants.Events.NEW_FEATURE_USED) {
    param(AnalyticsConstants.Params.FEATURE_TYPE, "lyrics_sync")
    param(AnalyticsConstants.Params.USER_ACTION, "timestamp_added")
}
```

### Enforcement Rules

**🚫 ABSOLUTELY FORBIDDEN:**
- Hardcoded event names: `"user_action"`, `"screen_view"`, etc.
- Hardcoded parameter names: `"song_id"`, `"error_message"`, etc.
- Using `Bundle.putString("key", value)` with string literals
- Any hardcoded analytics strings whatsoever

**✅ ALWAYS REQUIRED:**
- Use `AnalyticsConstants.Events.*` for all event names
- Use `AnalyticsConstants.Params.*` for all parameter names  
- Use `AnalyticsConstants.ScreenNames.*` for screen tracking
- Use `AnalyticsConstants.ErrorMessages.*` for error logging
- Use `FirebaseAnalytics.Event.SCREEN_VIEW` instead of `"screen_view"`

### Benefits
- **Consistency:** No typos in analytics events
- **Maintainability:** Change event names globally from one location
- **Discoverability:** IDE autocomplete shows all available events/params
- **Refactoring Safety:** Rename constants safely with IDE refactoring
- **Code Review:** Easily spot violations during PR reviews

### Code Review Checklist
Before merging any PR that adds analytics:
- [ ] All event names use `AnalyticsConstants.Events.*`
- [ ] All parameter names use `AnalyticsConstants.Params.*`  
- [ ] No hardcoded analytics strings anywhere
- [ ] New constants added to `AnalyticsConstants.kt` if needed
- [ ] Screen view events use standard Firebase constants

**This pattern is MANDATORY for all future Firebase Analytics implementations.**