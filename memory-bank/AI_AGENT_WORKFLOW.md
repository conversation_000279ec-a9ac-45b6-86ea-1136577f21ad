# AI Agent Development Workflow Checklist

**⚠️ MANDATORY: The AI agent MUST follow ALL steps in this checklist for EVERY development task. This is NOT optional guidance - it is a required procedure that ensures code quality, proper testing, and successful delivery. Skipping any step is a violation of development protocol. ⚠️**

## When Receiving a Development Task

### Step 1: Initial Context Loading
- [ ] Read `/memory-bank/activeContext.md` to understand current development state
- [ ] Check `/memory-bank/progress.md` to know what's implemented and what's pending
- [ ] Review `/memory-bank/systemPatterns.md` for established patterns and lessons learned
- [ ] Load `/memory-bank/techContext.md` for technical implementation details
- [ ] Review `ANDROID_PROJECT_STRUCTURE.md` for architectural requirements you MUST FOLLOW

### Step 2: Task Analysis
- [ ] Identify which layers will be affected (Presentation/Domain/Data)
- [ ] Check if similar functionality already exists (DRY principle)
- [ ] Determine what sealed classes need to be created/modified
- [ ] List all UI strings that will need extraction
- [ ] Identify constants needed (navigation, analytics, etc.)

### Step 3: Pre-Implementation Planning
- [ ] Present implementation plan to user before coding
- [ ] Ask clarifying questions if requirements unclear
- [ ] Confirm approach aligns with existing patterns
- [ ] Mention any potential issues or considerations

### Step 4: Implementation Rules

#### 4.1 Architecture Compliance
- [ ] Follow Clean Architecture + MVVM strictly
- [ ] Maintain layer separation (Presentation → Domain → Data)
- [ ] Use feature-based package organization

#### 4.2 Code Quality Standards
- [ ] **NEVER use force unwrapping (`!!`)**
- [ ] **NEVER create when expressions** - use sealed classes with polymorphism
- [ ] **NEVER hardcode strings in UI** - use stringResource()
- [ ] **NEVER hardcode analytics events** - use AnalyticsConstants
- [ ] **ALWAYS use Hilt dependency injection** for ViewModels and repositories

#### 4.3 Implementation Patterns
```kotlin
// ALWAYS follow these patterns:

// ✅ Sealed State Classes
sealed class FeatureUiState {
    abstract fun render(): @Composable () -> Unit
}

// ✅ Null Safety
data?.let { /* safe processing */ } ?: defaultValue

// ✅ String Resources
Text(text = stringResource(R.string.feature_title))

// ✅ Analytics Constants
analytics.logEvent(AnalyticsConstants.Events.FEATURE_ACTION)

// ✅ Hilt ViewModel
@HiltViewModel
class FeatureViewModel @Inject constructor(
    private val useCase: FeatureUseCase
) : ViewModel()
```

### Step 5: During Implementation

#### 5.1 File Creation/Modification
- [ ] Create proper package structure following feature organization
- [ ] Place files in correct layers (presentation/domain/data)
- [ ] Follow naming conventions exactly

#### 5.2 Code Implementation
- [ ] Implement sealed classes with abstract methods
- [ ] Create ViewModels with @HiltViewModel
- [ ] Extract ALL user-facing strings to strings.xml
- [ ] Add constants to appropriate constant files
- [ ] Implement proper error handling with sealed results

#### 5.3 Dependency Injection
- [ ] Add @Inject constructors
- [ ] Create necessary @Module classes
- [ ] Use @Binds for interface implementations
- [ ] Apply proper scoping annotations

### Step 6: Testing Implementation
**MANDATORY: Write comprehensive tests at all levels**

- [ ] Create unit tests for ViewModels (test business logic in isolation)
- [ ] Create unit tests for use cases (test domain rules)
- [ ] Add Compose UI tests for new components (test UI behavior)
- [ ] **Create integration tests for complete user flows (test everything works together)**
- [ ] Ensure tests also follow no-when-expression rule

**Testing Strategy:**
- **Unit Tests**: Test individual classes/functions in isolation with mocked dependencies
- **UI Tests**: Test UI components and their interactions
- **Integration Tests**: Test complete features from user action to data persistence

**Why Integration Tests Are Critical:**
- Catch issues that unit tests miss (e.g., incorrect dependency wiring)
- Verify that all layers work together correctly
- Ensure data flows properly through the entire architecture
- Validate that user actions produce expected results in the database
- Confirm that error handling works end-to-end

#### 6.1 Integration Test Requirements
**MANDATORY: Create integration tests that verify the entire feature flow**

```kotlin
// Integration test example structure
@HiltAndroidTest
class FeatureIntegrationTest {
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()
    
    @Test
    fun completeFeatureFlow_fromUserActionToDataPersistence_worksCorrectly() {
        // 1. Navigate to feature screen
        composeTestRule.onNodeWithTag("feature_nav_button").performClick()
        
        // 2. Perform user actions
        composeTestRule.onNodeWithTag("input_field").performTextInput("test data")
        composeTestRule.onNodeWithTag("submit_button").performClick()
        
        // 3. Verify UI updates
        composeTestRule.onNodeWithText("Success").assertIsDisplayed()
        
        // 4. Verify data persistence
        val savedData = runBlocking {
            repository.getData()
        }
        assertThat(savedData).contains("test data")
        
        // 5. Verify analytics events fired
        verify(analytics).logEvent(eq(AnalyticsConstants.Events.FEATURE_COMPLETED), any())
    }
}
```

#### 6.2 What Integration Tests Must Cover
- [ ] Complete user journey from UI interaction to data persistence
- [ ] Navigation flows between screens
- [ ] Data flow through all architecture layers (Presentation → Domain → Data)
- [ ] Side effects (analytics, logging, notifications)
- [ ] Error scenarios and recovery flows
- [ ] State persistence across configuration changes
- [ ] Proper cleanup and resource management
- [ ] Concurrent operations and race conditions

#### 6.3 Integration Test Patterns
```kotlin
// Test complete feature flow
@Test
fun createNewItem_withAllSteps_persistsCorrectly() {
    // Arrange: Set up initial state
    clearDatabase()
    navigateToFeatureScreen()
    
    // Act: Perform complete user flow
    enterItemDetails("Test Item", "Description")
    selectCategory("Important")
    clickSaveButton()
    
    // Assert: Verify all layers
    // 1. UI shows success
    verifySuccessMessageDisplayed()
    
    // 2. Navigation occurred
    verifyNavigatedToListScreen()
    
    // 3. Item appears in list
    verifyItemInList("Test Item")
    
    // 4. Database contains item
    val savedItem = repository.getItemByName("Test Item")
    assertThat(savedItem).isNotNull()
    assertThat(savedItem.category).isEqualTo("Important")
    
    // 5. Analytics tracked
    verifyAnalyticsEvent(Events.ITEM_CREATED)
}

// Test error handling across layers
@Test
fun createItem_whenNetworkFails_showsErrorAndAllowsRetry() {
    // Arrange: Set up network failure
    mockNetworkError()
    
    // Act: Attempt to save
    enterItemDetails("Test Item", "Description")
    clickSaveButton()
    
    // Assert: Proper error handling
    verifyErrorMessageDisplayed("Network error")
    verifyRetryButtonVisible()
    verifyCachedDataSaved() // Should save locally
    
    // Act: Fix network and retry
    restoreNetwork()
    clickRetryButton()
    
    // Assert: Success flow
    verifySuccessMessageDisplayed()
    verifyDataSyncedToServer()
}

// Test data persistence and retrieval
@Test
fun dataFlow_fromUIToDatabase_maintainsIntegrity() {
    // Create item through UI
    composeTestRule.onNodeWithTag("name_input").performTextInput("Test Song")
    composeTestRule.onNodeWithTag("save_button").performClick()
    
    // Verify immediate UI feedback
    composeTestRule.onNodeWithText("Saved successfully").assertExists()
    
    // Verify database state directly
    runBlocking {
        val songs = songRepository.getAllSongs().first()
        assertThat(songs).hasSize(1)
        assertThat(songs[0].name).isEqualTo("Test Song")
    }
    
    // Navigate away and back to verify persistence
    composeTestRule.activity.finish()
    composeTestRule.activity.recreate()
    
    // Verify data still exists after recreation
    composeTestRule.onNodeWithText("Test Song").assertExists()
}
```

#### 6.4 Integration Test Setup
```kotlin
@HiltAndroidTest
@UninstallModules(AppModule::class) // Replace with test modules
class FeatureIntegrationTest {
    @Module
    @InstallIn(SingletonComponent::class)
    object TestModule {
        @Provides
        @Singleton
        fun provideTestDatabase(@ApplicationContext context: Context): AppDatabase {
            return Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
                .allowMainThreadQueries()
                .build()
        }
    }
}
```

### Step 7: Self-Validation Checks

Run these checks before presenting solution:
```bash
# Check for violations:
grep -rn "!!" --include="*.kt" [modified files]
grep -rn "when\s*(" --include="*.kt" [modified files]
grep -rn '"[^"]\{3,\}"' --include="*.kt" [UI files]
```

### Step 8: Mandatory Build & Test Validation

**CRITICAL: Never declare task complete without successful validation**

#### 8.1 Build Validation
```bash
# Must pass before proceeding
./gradlew clean build
```

**If Build Fails:**
1. Read error message completely
2. Identify exact failure cause:
   - Missing imports → Add required imports
   - Unresolved references → Check dependency injection
   - Type mismatches → Fix type safety issues
   - Syntax errors → Correct Kotlin syntax
3. Fix the root cause (not symptoms)
4. Re-run build until successful
5. Document what was fixed and why

#### 8.2 Unit Test Validation
```bash
# Run all unit tests
./gradlew test

# For specific test class
./gradlew test --tests "*.FeatureViewModelTest"
```

**If Unit Tests Fail:**
1. Analyze failure output:
   ```
   Expected: <value>
   Actual: <different_value>
   ```
2. Determine failure category:
   - **Regression**: New code broke existing functionality → Fix implementation
   - **Outdated Test**: Legitimate behavior change → Update test with explanation
   - **Missing Mock**: Dependency not properly mocked → Add proper mocking
   - **Timing Issue**: Coroutine/async problems → Use proper test dispatchers
3. Fix implementation first, update tests only if behavior legitimately changed
4. Document any test updates with reasoning
5. Re-run until all pass

#### 8.3 UI Test Validation
```bash
# Run all UI tests
./gradlew connectedAndroidTest

# Run specific UI test
./gradlew :app:connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=soly.lyricsgenerator.FeatureUITest
```

**If UI Tests Fail:**
1. Check device/emulator connection:
   ```bash
   adb devices
   ```
2. Analyze failure type:
   - **Element Not Found**:
      - Verify compose test tags exist
      - Check if element is actually visible
      - Add proper wait conditions
   - **Assertion Failed**:
      - Verify expected vs actual UI state
      - Check if state is properly initialized
   - **Navigation Error**:
      - Ensure navigation routes are correct
      - Verify NavController setup
3. Debug using:
   ```kotlin
   composeTestRule.onRoot().printToLog("UI_TREE")
   ```
4. Fix implementation to match test expectations
5. Re-run tests in isolation first, then full suite

#### 8.4 Integration Test Validation
```bash
# Run all integration tests
./gradlew connectedAndroidTest --tests "*IntegrationTest"

# Run specific integration test
./gradlew :app:connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=soly.lyricsgenerator.FeatureIntegrationTest
```

**If Integration Tests Fail:**
1. Identify which layer is failing:
   - **UI Layer**: Check navigation and user interactions
   - **Domain Layer**: Verify use case logic and data transformations
   - **Data Layer**: Check database operations and API calls
2. Common integration test issues:
   - **Dependency Injection**: Ensure test modules provide correct dependencies
   - **Async Operations**: Add proper synchronization and waiting
   - **Database State**: Clear database between tests or use test data
   - **Network Calls**: Mock external services appropriately
3. Debug strategies:
   ```kotlin
   // Add logging at each layer
   Timber.d("Integration Test: UI action performed")
   Timber.d("Integration Test: UseCase called with $data")
   Timber.d("Integration Test: Repository saved $entity")
   ```
4. Verify end-to-end flow:
   - User action triggers ViewModel
   - ViewModel calls UseCase
   - UseCase processes business logic
   - Repository persists data
   - UI reflects updated state

#### 8.5 App Installation & Runtime Validation
```bash
# Install and verify no crashes
./gradlew :app:installDebug

# Monitor for crashes
adb logcat | grep "FATAL EXCEPTION"
```

**If Runtime Crashes Occur:**
1. Capture full stack trace
2. Identify exception type:
   - **NullPointerException**: Add null checks with safe calls
   - **IllegalStateException**: Fix state management logic
   - **ClassCastException**: Fix type casting with safe casts
   - **Resources.NotFoundException**: Ensure resources exist
3. Test specific user flow causing crash
4. Add defensive programming:
   ```kotlin
   // Instead of crashing
   data?.let { processData(it) } ?: showError()
   ```
5. Verify fix with same user flow

### Step 9: Validation Report Template

After successful validation, prepare report:

```markdown
## Task Completion Report

### Implementation Summary
- Feature: [Name]
- Changes: [Brief description]
- Files Modified: [Count]

### Validation Results ✅
- Build: PASSED
- Unit Tests: PASSED (X tests)
- UI Tests: PASSED (Y tests)
- Integration Tests: PASSED (Z tests)
- Runtime: No crashes detected
- Code Standards: All checks passed

### Test Commands for Verification
```bash
./gradlew clean build
./gradlew test
./gradlew connectedAndroidTest
./gradlew :app:connectedDebugAndroidTest --tests "*IntegrationTest"
./gradlew :app:installDebug
```

### Known Limitations (if any)
- [List any edge cases or limitations]
```

### Step 10: Failure Recovery Protocol

**If unable to fix issues after reasonable attempts:**

1. **Document the blocker:**
   ```markdown
   ## Implementation Blocked
   
   ### Issue
   - Type: [Build/Test/Runtime failure]
   - Error: [Exact error message]
   
   ### Attempted Solutions
   1. [What was tried]
   2. [Why it didn't work]
   
   ### Possible Causes
   - [Root cause analysis]
   
   ### Recommended Next Steps
   - [Suggestions for resolution]
   ```

2. **Partial Implementation Delivery:**
   - Commit working code to a feature branch
   - Mark incomplete sections with TODO comments
   - Document what works and what doesn't

3. **Ask for user guidance:**
   - Present the specific blocker
   - Offer alternative approaches
   - Request permission to proceed differently

### Step 11: Memory Bank Updates

After completing implementation, update:

#### 11.1 activeContext.md
- [ ] Document what was implemented
- [ ] Note any challenges encountered
- [ ] Specify next logical steps
- [ ] Update current development focus

#### 11.2 progress.md
- [ ] Add new features to "What Works"
- [ ] Update "What's Left to Build"
- [ ] Note any improvements made

#### 11.3 systemPatterns.md
- [ ] Document any new patterns discovered
- [ ] Add lessons learned from debugging
- [ ] Include prevention strategies for issues
- [ ] Update best practices

### Step 12: Final Presentation to User

Only after ALL validation passes:

```markdown
## Task Completed Successfully ✅

### What Was Implemented
[Detailed summary of changes]

### Validation Passed
- Build: ✅ Success
- Unit Tests: ✅ All passing
- UI Tests: ✅ All passing
- Integration Tests: ✅ All passing
- App Runs: ✅ No crashes

### How to Verify
1. Pull latest changes
2. Run: `./gradlew clean build`
3. Run: `./gradlew test` (unit tests)
4. Run: `./gradlew connectedAndroidTest` (all UI tests)
5. Run: `./gradlew :app:connectedDebugAndroidTest --tests "*IntegrationTest"` (integration tests)
6. Run app and test [specific feature] manually

### Memory Bank Updated
- activeContext.md ✅
- progress.md ✅
- systemPatterns.md ✅

### Ready for Review
All changes are tested and working. Should I commit these changes?
```

### Step 13: Commit Preparation

**CRITICAL: NEVER commit without explicit permission**
- [ ] Show user the changes: `git status` and `git diff --staged`
- [ ] Ask: "Should I commit these changes?"
- [ ] WAIT for explicit "yes" or "commit" instruction
- [ ] Create commit message with:
   - [ ] Conventional format (feat:, fix:, etc.)
   - [ ] NO AI/Claude references
   - [ ] Based on actual staged changes

## Critical Success Criteria

The AI agent MUST NOT declare task complete unless:
- ✅ Code builds successfully
- ✅ All existing tests still pass
- ✅ New tests pass (unit, UI, and integration)
- ✅ Integration tests verify complete user flows
- ✅ App runs without crashes
- ✅ Feature works as requested end-to-end
- ✅ All code standards followed
- ✅ Memory bank updated

## Continuous Behavior Rules

### Always:
- ✅ Ask for clarification when requirements are ambiguous
- ✅ Present options when multiple solutions exist
- ✅ Respect previous architectural decisions
- ✅ Follow user's explicit instructions exactly
- ✅ Update memory bank after significant changes
- ✅ Validate all code before declaring completion

### Never:
- ❌ Override user decisions without permission
- ❌ Add MANAGE_EXTERNAL_STORAGE permission
- ❌ Use force unwrapping (!!)
- ❌ Create when expressions
- ❌ Hardcode strings or analytics
- ❌ Commit without explicit permission
- ❌ Make assumptions about user intent
- ❌ Declare task complete without successful validation

## Error Recovery Protocol

If issues arise:
1. **Stop and explain** the problem clearly
2. **Present multiple solutions** with trade-offs
3. **Ask user preference** instead of choosing
4. **Document the issue** in systemPatterns.md after resolution

## Emergency Protocols

### If Everything Breaks
1. **DON'T PANIC**
2. Git stash changes: `git stash`
3. Return to last working state: `git checkout .`
4. Analyze what went wrong
5. Start with smaller incremental changes
6. Test after each small change

### If Tests Are Flaky
1. Run failed test in isolation 3 times
2. If passes 2/3 times, investigate timing issues
3. Add proper synchronization
4. Consider test environment issues
5. Document flaky test for future fix

## Quick Reference for Common Tasks

### Adding a New Screen
1. Create in `presentation/screens/[feature]/`
2. Add route to `NavRoutes.kt`
3. Add to `NavGraph.kt`
4. Create ViewModel with @HiltViewModel
5. Extract all strings to strings.xml
6. Add analytics events
7. Write UI tests for components
8. **Write integration tests for complete flows**
9. Validate everything works

### Adding a New Feature
1. Create feature package structure
2. Implement domain use cases first
3. Create data layer repositories
4. Build presentation layer last
5. Add proper Hilt modules
6. Write tests for each layer:
   - Unit tests for business logic
   - UI tests for components
   - **Integration tests for complete flows**
7. Run full validation suite

This checklist should be followed by the AI agent for EVERY development task to ensure consistent, high-quality code delivery.

---

**🔴 FINAL REMINDER: This checklist is MANDATORY for the AI agent. Every single step MUST be followed for every development task. The AI agent must:
- ✅ Load all context files before starting
- ✅ Plan before implementing
- ✅ Follow ALL code quality standards
- ✅ Write unit, UI, AND integration tests
- ✅ Successfully build and run all tests
- ✅ Update memory bank documentation
- ✅ Wait for explicit permission before committing

Failure to follow this checklist is a critical error. The AI agent should refer back to this document throughout the development process to ensure compliance. NO EXCEPTIONS. 🔴**