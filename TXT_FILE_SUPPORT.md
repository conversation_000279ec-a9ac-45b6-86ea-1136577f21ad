# TXT File Support Implementation

This document describes the implementation of TXT file support for the Song Details screen, allowing users to view plain text lyrics alongside the existing LRC synchronized lyrics functionality.

## Overview

The implementation adds support for displaying TXT files on the Song Details screen with the following characteristics:
- **Static Display**: TXT files are displayed as scrollable text without synchronization
- **Manual Scrolling**: Users manually scroll through lyrics (no auto-scroll or highlighting)
- **Backward Compatibility**: Existing LRC file functionality remains unchanged
- **Clean Architecture**: Implementation follows project patterns with sealed classes and polymorphic dispatch

## Architecture

### Core Components

#### 1. FileContentState (Domain Layer)
```kotlin
sealed class FileContentState {
    abstract fun isEmpty(): Boolean
    abstract fun extractLrcLines(): Map<Int, String>
    
    data class LrcContent(val lrcLines: Map<Int, String>) : FileContentState()
    data class TxtContent(val textLines: List<String>) : FileContentState()
    data object Empty : FileContentState()
}
```
Represents different types of file content using polymorphic dispatch instead of when expressions.

#### 2. ReadTxtFileUseCase (Domain Layer)
```kotlin
class ReadTxtFileUseCase @Inject constructor() {
    suspend fun readTxtFile(filePath: String): List<String>
}
```
Handles reading plain text files from internal storage.

#### 3. GetFileContentForSongUseCase (Domain Layer)
```kotlin
class GetFileContentForSongUseCase @Inject constructor(
    private val getFileByIdUseCase: GetFileByIdUseCase,
    private val parseLyricsUseCase: ParseLyricsUseCase,
    private val readTxtFileUseCase: ReadTxtFileUseCase
)
```
Unified use case that handles both LRC and TXT files based on FileType.

#### 4. FileProcessor (Domain Layer)
```kotlin
sealed class FileProcessor {
    abstract suspend fun processContent(...): FileContentState
    
    data object LrcProcessor : FileProcessor()
    data object TxtProcessor : FileProcessor()
}
```
Polymorphic processing strategies for different file types.

#### 5. TxtDisplay (UI Layer)
```kotlin
@Composable
fun TxtDisplay(
    textLines: List<String>,
    modifier: Modifier = Modifier
)
```
Composable component for displaying plain text in a scrollable list.

#### 6. FileContentRenderer (UI Layer)
```kotlin
sealed class FileContentRenderer {
    abstract fun render(...): @Composable () -> Unit
    
    data class LrcRenderer(val lrcLines: Map<Int, String>) : FileContentRenderer()
    data class TxtRenderer(val textLines: List<String>) : FileContentRenderer()
    data object EmptyRenderer : FileContentRenderer()
}
```
UI rendering dispatch based on content type using polymorphic methods.

### Updated Components

#### MusicViewModel
- Added `fileContentState: StateFlow<FileContentState>` for new content handling
- Added `loadFileContent(songId: Long)` method
- Maintained backward compatibility with existing `lrcLines` StateFlow
- Updated collectors to use new file content loading

#### SongDetailsScreen
- Updated to collect `fileContentState` alongside existing state
- Replaced direct `LyricsDisplay` usage with `FileContentRenderer`
- Maintained all existing functionality for LRC files

## File Flow

### Navigation Flow
1. User taps file item in Files screen → `navigateToSongDetails(fileWithSong)`
2. Navigation passes `song_id` to SongDetailsScreen
3. SongDetailsScreen calls `musicViewModel.loadFileContent(songId)`
4. ViewModel determines file type and loads appropriate content
5. UI renders content using `FileContentRenderer`

### File Processing Flow
```
Song ID → GetFileByIdUseCase → File Entity
         ↓
File Entity → FileProcessor.fromFileType(fileType) → Specific Processor
         ↓
LrcProcessor → ParseLyricsUseCase → LrcContent
TxtProcessor → ReadTxtFileUseCase → TxtContent
         ↓
FileContentState → FileContentRenderer → UI Component
```

## Key Design Decisions

### 1. Sealed Classes with Polymorphism
- **No when expressions** throughout the codebase
- Each sealed class subtype implements its own behavior
- Follows Open/Closed principle for extensibility

### 2. Backward Compatibility
- Existing `lrcLines` StateFlow maintained
- `loadLrcFile()` method preserved
- All existing LRC functionality unchanged

### 3. Clean Architecture
- Domain logic separated from UI concerns
- Use cases handle business logic
- Repository pattern maintained

### 4. Error Handling
- Graceful handling of non-existent files
- Empty content states properly managed
- Logging for debugging and monitoring

## Testing

### Unit Tests
- `FileContentStateTest`: Tests sealed class behavior
- `ReadTxtFileUseCaseTest`: Tests file reading logic
- `TxtFileIntegrationTest`: End-to-end flow testing

### UI Tests
- `TxtDisplayTest`: Composable component testing
- Integration with existing test patterns

## Internationalization

Added string resource:
```xml
<string name="no_lyrics_available">No lyrics available</string>
```

Fixed existing hardcoded string in `LyricsDisplay` component.

## Files Modified

### Domain Layer
- `app/src/main/java/soly/lyricsgenerator/domain/model/FileContentState.kt` (new)
- `app/src/main/java/soly/lyricsgenerator/domain/model/FileProcessor.kt` (new)
- `app/src/main/java/soly/lyricsgenerator/domain/usecase/lyrics/ReadTxtFileUseCase.kt` (new)
- `app/src/main/java/soly/lyricsgenerator/domain/usecase/lyrics/GetFileContentForSongUseCase.kt` (new)

### UI Layer
- `app/src/main/java/soly/lyricsgenerator/ui/screens/components/TxtDisplay.kt` (new)
- `app/src/main/java/soly/lyricsgenerator/ui/screens/components/FileContentRenderer.kt` (new)
- `app/src/main/java/soly/lyricsgenerator/ui/screens/components/LyricsDisplay.kt` (modified)
- `app/src/main/java/soly/lyricsgenerator/ui/screens/song_details_screen/SongDetailsScreen.kt` (modified)
- `app/src/main/java/soly/lyricsgenerator/ui/viewmodel/MusicViewModel.kt` (modified)

### Resources
- `app/src/main/res/values/strings.xml` (modified)

### Tests
- Unit tests for new components
- Integration tests for complete flow
- UI tests for new composables

## Future Enhancements

### Potential Extensions
1. **Additional File Types**: Easy to add new file types with additional sealed class subtypes
2. **Enhanced TXT Features**: Could add features like line numbering, search within text
3. **Import TXT Files**: Similar to existing LRC import functionality
4. **TXT to LRC Conversion**: Allow users to convert TXT files to LRC format

### Performance Considerations
- File reading operations are performed on IO dispatcher
- Large text files are loaded line by line efficiently
- UI rendering uses lazy loading for performance

## Compliance

This implementation follows all project architectural guidelines:
- ✅ Clean Architecture with proper layer separation
- ✅ Sealed classes with polymorphism (no when expressions)
- ✅ SOLID principles throughout
- ✅ Hilt dependency injection ready
- ✅ No hardcoded strings in UI
- ✅ Backward compatibility maintained
- ✅ Comprehensive testing coverage