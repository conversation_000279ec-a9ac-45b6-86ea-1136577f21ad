# GitHub Copilot Instructions for Soly - Lyrics Generator Android App

This file provides context and guidance for GitHub Copilot when working with this Android project.

## Project Overview

**Soly** is an Android lyrics synchronization application built with:
- **Language**: Kotlin with Jetpack Compose
- **Build System**: Gradle with Android Gradle Plugin 8.8.2
- **Target SDK**: Android API 34
- **Min SDK**: Android API 24 (Android 7.0)
- **Package**: `soly.lyricsgenerator`
- **Architecture**: Clean Architecture with MVVM pattern

## Core Features

The app allows users to:
- Browse and play local music from their device
- Create synchronized lyrics (.lrc) files by timestamping each line with music playback
- Import, view, and manage existing LRC files
- View synchronized lyrics during music playback
- Organize favorites and search through songs/lyrics

## Development Environment

### Prerequisites
- Java JDK 17 or higher
- Android SDK
- Internet access for dependency downloads

### Build Commands
```bash
# Build debug variant
./gradlew assembleDebug

# Build and install on device
./gradlew :app:installDebug

# Run unit tests
./gradlew test

# Run instrumented tests
./gradlew connectedAndroidTest
```

## Critical Development Requirements

### 🚨 MANDATORY: Check Project Structure Before Tasks
**BEFORE executing ANY coding task, you MUST:**
1. **Read and understand** `memory-bank/ANDROID_PROJECT_STRUCTURE.md`
2. **Follow the architectural patterns** defined in that document
3. **Apply the coding standards** specified in the structure guide

This document contains:
- **Clean Architecture + MVVM** implementation guidelines
- **Sealed classes with polymorphism** instead of when expressions (MANDATORY)
- **SOLID principles** application across all layers
- **Hilt dependency injection** patterns
- **Zero tolerance policies** for when expressions and hardcoded strings
- **Code organization** and naming conventions
- **Layer separation** and data flow patterns

**Failure to follow these architectural guidelines will result in code that doesn't align with project standards.**

### 🚨 MANDATORY: Always Validate Your Work
After making changes, you MUST validate:

**If you modified code:**
```bash
# Step 1: Build the project (MANDATORY)
./gradlew assembleDebug

# Step 2: Run ALL unit tests (MANDATORY)
./gradlew test

# Step 3: Run ALL UI/instrumented tests (MANDATORY)
./gradlew connectedAndroidTest
```

**ALL THREE COMMANDS MUST PASS** before your work is considered complete. This ensures:
- Ensures the project builds without errors.
- Verifies all unit tests pass, maintaining code logic integrity.
- Validates all UI tests pass, ensuring UI functionality is intact.
- Confirms no regressions are introduced by the changes.

**If you wrote NEW tests:**
Additionally run the specific test you created to verify it works:
```bash
# For unit tests
./gradlew test --tests "YourTestClass"

# For instrumented tests
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=YourTestClass
```

**If build or tests fail due to missing dependencies:**
- If the build or tests fail due to missing dependencies, install the required tools and retry.

**Never submit unvalidated work** - always verify your changes work correctly with ALL validation steps.

### 🚨 CRITICAL: Storage Permissions Policy

**NEVER ADD `MANAGE_EXTERNAL_STORAGE` PERMISSION TO THIS APP!**

This is a strict requirement due to Google Play Store policies. The app has been specifically designed to work without this permission by using:

1. **Internal Storage** (`context.filesDir`) - All LRC files are saved here
2. **Storage Access Framework (SAF)** - For export functionality where users choose save location
3. **MediaStore API** - For accessing music files with granular permissions

**Why this is critical:**
- Google Play will reject or restrict apps using this permission without valid justification
- All app functionality works perfectly without it
- Adding this permission would violate privacy best practices

**If you think you need this permission:** You don't. Find an alternative using the methods listed above.

### 🚨 CRITICAL: Project Configuration Policy

**NEVER MODIFY GRADLE FILES OR PROJECT SETUP!**

This is a strict requirement for project stability:

**DO NOT TOUCH:**
- `build.gradle.kts` (root or app level)
- `gradle/libs.versions.toml`
- `gradle.properties`
- `settings.gradle.kts`
- `gradle/wrapper/` files
- Any other build configuration files

**Why this is critical:**
- Project setup is already optimized and tested
- Gradle changes can break builds in different environments
- Version configurations are carefully managed
- Build scripts handle environment setup automatically

**If you think you need to change build configuration:** You don't. Work within the existing setup or ask for guidance.

## Project Structure

```
├── app/                                    # Main Android application module
│   ├── src/main/java/soly/lyricsgenerator/ # Kotlin source code
│   │   ├── ui/                            # Jetpack Compose UI components
│   │   │   ├── screens/                   # Screen composables by feature
│   │   │   ├── components/                # Reusable UI components
│   │   │   └── viewmodel/                 # ViewModels for UI state
│   │   ├── domain/                        # Business logic layer
│   │   │   ├── usecase/                   # Use cases
│   │   │   ├── model/                     # Domain entities
│   │   │   ├── repository/                # Repository interfaces
│   │   │   └── service/                   # Background services
│   │   └── data/                          # Data access layer
│   │       ├── repository/                # Repository implementations
│   │       └── database/                  # Room database
│   ├── src/main/res/                      # Android resources
│   │   ├── values-*/                      # Internationalization (10 languages)
│   │   └── layout/                        # XML layouts (minimal, mostly Compose)
│   ├── build.gradle.kts                   # App-level build configuration
│   └── google-services.json               # Firebase configuration (generated)
├── .github/workflows/                      # CI/CD workflows
├── memory-bank/                           # Project documentation and context
├── gradle/                                # Gradle configuration
├── setup-android-build.sh                 # Environment setup script
└── gradlew                                # Gradle wrapper
```

## Architecture Details

### Clean Architecture Layers
1. **Presentation Layer**: Jetpack Compose UI + ViewModels
   - Screens organized by feature (create_screen, files_list_screen, etc.)
   - All UI built with Jetpack Compose
   - MVVM pattern with StateFlow for reactive UI

2. **Domain Layer**: Business logic in UseCases
   - UseCases for file management, lyrics generation, music playback
   - Model classes representing core entities (Song, File, etc.)

3. **Data Layer**: Data access through repositories
   - Room database for local storage
   - File system operations for LRC file management

4. **DI**: Dependency injection with Hilt
   - Modules for database, repositories, and Firebase

### Key Components

1. **Music Player**:
   - Background service for music playback (MusicPlayerService)
   - Uses Android's MediaPlayer
   - Supports favorites filtering and real-time updates

2. **Database**:
   - Room database storing song and file metadata
   - DAOs for Song and File entities
   - Migration support for schema changes

3. **LRC Handling**:
   - Operations for creating, parsing, and saving LRC files
   - Timestamping functionality in CreateScreen

4. **Navigation**:
   - Single Activity architecture with Compose Navigation
   - Navigation routes defined in NavRoutes.kt

## Key Files to Understand

- `build.gradle.kts` (root): Project-level Gradle configuration
- `app/build.gradle.kts`: Android app module configuration  
- `gradle/libs.versions.toml`: Dependency version catalog
- `.github/workflows/android-ci.yml`: CI/CD pipeline configuration
- `CLAUDE.md`: Comprehensive project documentation for AI assistants
- `memory-bank/`: Persistent project knowledge and context

## Build System Details

### Gradle Configuration
- Uses Gradle 8.10.2
- Version catalog in `gradle/libs.versions.toml`
- Supports both online and offline builds
- Includes Firebase integration and ProGuard minification

### Validation Commands
```bash
# Validate code changes - build the app
./gradlew assembleDebug

# Validate specific unit test
./gradlew test --tests "YourTestClass"

# Validate all unit tests
./gradlew test

# Validate specific instrumented test
./gradlew :app:connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=YourTestClass

# Validate all instrumented tests
./gradlew connectedAndroidTest

# Additional commands if needed
./gradlew clean         # Clean build artifacts
./gradlew lint          # Run code linting
./gradlew :app:installDebug  # Install on device
```

## Internationalization

The app supports 10 languages:
- English (default)
- German (de)
- Spanish (es) 
- French (fr)
- Italian (it)
- Polish (pl)
- Portuguese (pt)
- Russian (ru)
- Serbian (sr)
- Turkish (tr)

**Critical Rule**: NO hardcoded strings in UI code. Always use `stringResource(R.string.*)` calls.

## Development Workflow

1. Make code changes
2. **Validate your work (MANDATORY):**
   - Build the project: `./gradlew assembleDebug`
   - Run ALL unit tests: `./gradlew test`
   - Run ALL UI/instrumented tests: `./gradlew connectedAndroidTest`
   - If build fails due to missing tools: Install required dependencies and retry
3. Fix any errors before proceeding - ALL validation steps must pass
4. Only submit working, validated code that passes all tests

## Testing

- Unit tests in `/app/src/test/`
- UI/Instrumentation tests in `/app/src/androidTest/`
- Uses JUnit, Espresso, and Compose UI Testing
- Permission handling with GrantPermissionRule

## Development Guidelines

1. **Logging**: Use Timber with `DEBUG_FLOW` tag for debugging
   ```kotlin
   Timber.d("DEBUG_FLOW: ClassName - action description")
   ```

2. **State Management**: ViewModels expose UI state via `StateFlow`
   ```kotlin
   data class ScreenUiState(
       val isLoading: Boolean = false,
       val data: List<Item> = emptyList()
   )
   val uiState: StateFlow<ScreenUiState> = _uiState.asStateFlow()
   ```

3. **Analytics**: Log Firebase Analytics events for key user actions
   - Screen views: Use `FirebaseAnalytics.Event.SCREEN_VIEW`
   - Custom events: Follow naming convention `feature_action_detail`

4. **File Operations**: Files imported by users are copied to internal storage
   - Use `context.filesDir` for internal file storage
   - Store absolute paths in database, not content URIs

## Firebase Integration

The project includes Firebase Analytics and App Distribution:
- Fake `google-services.json` created for offline builds
- Real Firebase credentials managed via GitHub Secrets
- Distribution configured in `android-ci.yml` workflow

## Environment Support

This project supports:
- **Local development**: Full Android Studio integration
- **Offline environments**: Complete builds without internet after setup
- **CI/CD**: GitHub Actions with Firebase distribution
- **Docker containers**: Minimal Android SDK setup

## Code Style Guidelines

- Use Kotlin for all code
- Follow Android/Kotlin coding conventions
- Leverage Jetpack Compose for UI
- Maintain compatibility with offline builds
- Follow Clean Architecture principles
- Use Hilt for dependency injection

## Git Commit Guidelines

🚨 **ABSOLUTELY CRITICAL RULES** 🚨

### 1. NO AI REFERENCES IN COMMIT MESSAGES
- NO Claude mentions, NO AI footers, NO "Generated with" text
- Use conventional commit format ONLY (feat:, fix:, refactor:, etc.)

## Memory Bank Management

### Important Files to Update
Always examine and update the `/memory-bank` folder:
- `activeContext.md` - Current development focus and active tasks
- `productContext.md` - Product vision and requirements  
- `progress.md` - Development progress and recent changes
- `techContext.md` - Technical implementation details

Update the memory bank when:
1. Implementing significant features or changes
2. Discovering new project patterns
3. Making architectural decisions
4. Completing major tasks
5. Encountering and resolving issues

## Troubleshooting

### Build Failures
1. Check error messages carefully
2. Common fixes:
   ```bash
   chmod +x gradlew
   ./gradlew clean assembleDebug
   ```
3. Ensure you have proper Android SDK setup
4. Check that all dependencies can be downloaded

### Permission Issues
- Never add `MANAGE_EXTERNAL_STORAGE` permission
- Use internal storage and SAF for file operations
- Check MediaStore API usage for music access

## Notes for GitHub Copilot

### 🔴 CRITICAL: Pre-Task Requirements
**MANDATORY FIRST STEP:** Before providing ANY code suggestions or executing coding tasks:
1. **ALWAYS reference** `memory-bank/ANDROID_PROJECT_STRUCTURE.md`
2. **Verify your suggestions align** with the architectural patterns defined there
3. **Apply the specific coding standards** from that document

This ensures:
- ✅ Sealed classes with polymorphism instead of when expressions
- ✅ Proper Clean Architecture + MVVM implementation
- ✅ SOLID principles adherence
- ✅ Correct Hilt dependency injection patterns
- ✅ No hardcoded strings policy compliance
- ✅ Appropriate layer separation and data flow

### General Guidelines
- **Always validate your work**: Run all three test commands (`./gradlew assembleDebug`, `./gradlew test`, and `./gradlew connectedAndroidTest`) after code changes; all must pass.
- **NEVER modify Gradle files or project configuration** - work within existing setup
- Prefer Kotlin syntax and Jetpack Compose patterns
- Follow existing project structure and naming conventions
- Ensure your code compiles and all tests pass before considering it complete
- Never suggest hardcoded strings - always use string resources
- Respect the storage permissions policy - no external storage access
- Update memory bank documentation for significant changes
- Follow Clean Architecture and MVVM patterns
- Use Hilt for dependency injection suggestions
- Consider internationalization in UI suggestions
- **Remember**: Working code is validated code that passes ALL tests