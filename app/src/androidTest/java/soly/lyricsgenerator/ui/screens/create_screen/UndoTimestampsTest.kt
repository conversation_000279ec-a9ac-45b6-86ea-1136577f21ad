package soly.lyricsgenerator.ui.screens.create_screen

import soly.lyricsgenerator.ui.constants.UITestTags

import android.Manifest
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onFirst
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextReplacement
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.compose.ui.test.onAllNodesWithContentDescription
import androidx.compose.ui.test.onAllNodesWithText
import androidx.compose.ui.test.hasClickAction
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.R

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class UndoTimestampsTest {
    companion object {
        private const val NAVIGATION_TIMEOUT_MS = 45000L  // Increased for CI
        private const val SCREEN_LOAD_TIMEOUT_MS = 30000L
        private const val DIALOG_TIMEOUT_MS = 15000L
        
        // Test data constants
        private const val TEST_LYRICS_LINE1 = "Line1"
        private const val TEST_LYRICS_LINE2 = "Line2"
        private const val TEST_LYRICS_SINGLE = "Test Line"
        private const val TIMESTAMP_INITIAL = "[00:00.50]"
        private const val TIMESTAMP_RESET = "[00:00.00]"
        private const val TEST_TAG_BOTTOM_NAV = UITestTags.BOTTOM_NAVIGATION_BAR
        private const val TEST_TAG_SONG_LIST = UITestTags.SONG_LIST
    }

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule(order = 2)
    val permissionsRule: GrantPermissionRule = GrantPermissionRule.grant(
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(TEST_TAG_BOTTOM_NAV))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle()
    }

    private fun navigateToCreateTab() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(TEST_TAG_BOTTOM_NAV)))
        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
    }

    @Test
    fun undoResetsTimestamps() {
        // Navigate to Create tab
        navigateToCreateTab()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // Step 1: Choose song
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        composeTestRule.onNode(hasText(step1Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.song_list)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // Wait for songs to load
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule
                .onAllNodes(hasTestTag(TEST_TAG_SONG_LIST))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Select first song - using hasClickAction to ensure we get clickable items
        composeTestRule.onAllNodes(hasAnyAncestor(hasTestTag(TEST_TAG_SONG_LIST)) and hasClickAction())
            .onFirst()
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Wait for navigation back to CreateScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // Step 2: Add lyrics
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // Choose paste lyrics option
        val pasteLyricsText = composeTestRule.activity.getString(R.string.paste_lyrics_option)
        composeTestRule.onNode(hasText(pasteLyricsText)).performClick()
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // Paste lyrics and save
        val pasteLyricsPlaceholder = composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)
        val lyrics = "$TEST_LYRICS_LINE1\n$TEST_LYRICS_LINE2"
        composeTestRule.onNode(hasText(pasteLyricsPlaceholder)).performTextReplacement(lyrics)
        composeTestRule.waitForIdle()
        
        val saveText = composeTestRule.activity.getString(R.string.save)
        composeTestRule.onNode(hasText(saveText)).performClick()
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // Step 3: Start sync
        val step3Text = composeTestRule.activity.getString(R.string.step_text_start)
        composeTestRule.onNode(hasText(step3Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(TEST_LYRICS_LINE1)).fetchSemanticsNodes().isNotEmpty()
        }

        // Add a timestamp
        val addDesc = composeTestRule.activity.getString(R.string.content_desc_add)
        composeTestRule.onAllNodesWithContentDescription(addDesc).onFirst().performClick()
        composeTestRule.waitForIdle()
        
        // Verify timestamp was added
        composeTestRule.onAllNodesWithText(TIMESTAMP_INITIAL).onFirst().assertIsDisplayed()

        // Click 3-dots menu to open dropdown
        val moreOptionsDesc = composeTestRule.activity.getString(R.string.content_desc_more_options)
        composeTestRule.onNodeWithContentDescription(moreOptionsDesc).performClick()
        composeTestRule.waitForIdle()

        // Click undo option in dropdown
        val undoText = composeTestRule.activity.getString(R.string.undo_sync_confirmation_title)
        composeTestRule.onNode(hasText(undoText)).performClick()
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.undo_sync_confirmation_title)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Confirm undo
        val yesUndoText = composeTestRule.activity.getString(R.string.yes_undo)
        composeTestRule.onNode(hasText(yesUndoText)).performClick()
        composeTestRule.waitForIdle()
        
        // Verify timestamps were reset
        composeTestRule.onAllNodesWithText(TIMESTAMP_RESET).onFirst().assertIsDisplayed()
    }

    @Test
    fun undoDialogCancellation_preservesTimestamps() {
        // Navigate to Create tab and set up lyrics
        navigateToCreateTab()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // Choose song
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        composeTestRule.onNode(hasText(step1Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.song_list)))
                .fetchSemanticsNodes().size >= 1
        }
        // Wait for songs to load
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule
                .onAllNodes(hasTestTag(TEST_TAG_SONG_LIST))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Select first song - using hasClickAction to ensure we get clickable items
        composeTestRule.onAllNodes(hasAnyAncestor(hasTestTag(TEST_TAG_SONG_LIST)) and hasClickAction())
            .onFirst()
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Wait for navigation back to CreateScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // Add lyrics
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                .fetchSemanticsNodes().size == 1
        }
        val pasteLyricsText = composeTestRule.activity.getString(R.string.paste_lyrics_option)
        composeTestRule.onNode(hasText(pasteLyricsText)).performClick()
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)))
                .fetchSemanticsNodes().size >= 1
        }
        val pasteLyricsPlaceholder = composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)
        composeTestRule.onNode(hasText(pasteLyricsPlaceholder)).performTextReplacement(TEST_LYRICS_SINGLE)
        val saveText = composeTestRule.activity.getString(R.string.save)
        composeTestRule.onNode(hasText(saveText)).performClick()
        composeTestRule.waitForIdle()

        // Start sync
        val step3Text = composeTestRule.activity.getString(R.string.step_text_start)
        composeTestRule.onNode(hasText(step3Text)).performClick()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(TEST_LYRICS_SINGLE)).fetchSemanticsNodes().isNotEmpty()
        }

        // Add a timestamp
        val addDesc = composeTestRule.activity.getString(R.string.content_desc_add)
        composeTestRule.onAllNodesWithContentDescription(addDesc).onFirst().performClick()
        composeTestRule.waitForIdle()
        
        // Verify timestamp was added
        composeTestRule.onAllNodesWithText(TIMESTAMP_INITIAL).onFirst().assertIsDisplayed()

        // Click 3-dots menu to open dropdown
        val moreOptionsDesc = composeTestRule.activity.getString(R.string.content_desc_more_options)
        composeTestRule.onNodeWithContentDescription(moreOptionsDesc).performClick()
        composeTestRule.waitForIdle()

        // Click undo option in dropdown
        val undoText = composeTestRule.activity.getString(R.string.undo_sync_confirmation_title)
        composeTestRule.onNode(hasText(undoText)).performClick()
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.undo_sync_confirmation_title)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Cancel the dialog
        val cancelText = composeTestRule.activity.getString(R.string.cancel)
        composeTestRule.onNode(hasText(cancelText)).performClick()
        composeTestRule.waitForIdle()
        
        // Verify timestamps were preserved (not reset)
        composeTestRule.onAllNodesWithText(TIMESTAMP_INITIAL).onFirst().assertIsDisplayed()
    }
}