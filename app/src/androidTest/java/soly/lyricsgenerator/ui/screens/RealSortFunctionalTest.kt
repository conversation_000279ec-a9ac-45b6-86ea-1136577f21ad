package soly.lyricsgenerator.ui.screens

import android.Manifest
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.fake.TestConstants
import soly.lyricsgenerator.ui.constants.UITestTags

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class RealSortFunctionalTest {

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule
    val permissionsRule: GrantPermissionRule = GrantPermissionRule.grant(
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    // Fake data from FakeMusicRepository:
    // 1. Song(id = 1L, title = "Test Song 1", artist = "Test Artist 1", album = "Album A", dateAdded = 1000000L - OLDEST)
    // 2. Song(id = 2L, title = "Test Song 2", artist = "Test Artist 2", album = "Album C", dateAdded = 3000000L - NEWEST) 
    // 3. Song(id = 3L, title = "Another Test Song", artist = "Test Artist 3", album = "Album B", dateAdded = 2000000L - MIDDLE)

    @Before
    fun setup() {
        hiltRule.inject()
        navigateToMusicScreen()
    }

    @Test
    fun sortByTitle_ascending_verifiesCorrectOrder() {
        // Expected Title sort (ASC): TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_2_TITLE
        
        // Sort by Title
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.TITLE).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        // Verify the order by checking which song appears first in the list
        // Get all song items by their test tags
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Test Song 1
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Test Song 2  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Another Test Song
        
        // All songs should be displayed
        song1Node.assertIsDisplayed()
        song2Node.assertIsDisplayed()
        song3Node.assertIsDisplayed()
        
        // Get their bounds to determine order
        val song1Bounds = song1Node.fetchSemanticsNode().boundsInRoot
        val song2Bounds = song2Node.fetchSemanticsNode().boundsInRoot
        val song3Bounds = song3Node.fetchSemanticsNode().boundsInRoot
        
        // For Title ascending, expected order is: song3 (Another Test Song), song1 (Test Song 1), song2 (Test Song 2)
        // Verify song3 comes before song1 (top position = smaller Y coordinate)
        assert(song3Bounds.top < song1Bounds.top) { 
            "Expected '${TestConstants.Songs.SONG_3_TITLE}' (song3) to be above '${TestConstants.Songs.SONG_1_TITLE}' (song1). " +
            "song3.top=${song3Bounds.top}, song1.top=${song1Bounds.top}"
        }
        
        // Verify song1 comes before song2  
        assert(song1Bounds.top < song2Bounds.top) {
            "Expected '${TestConstants.Songs.SONG_1_TITLE}' (song1) to be above '${TestConstants.Songs.SONG_2_TITLE}' (song2). " +
            "song1.top=${song1Bounds.top}, song2.top=${song2Bounds.top}"
        }
        
        // This proves Title sorting is working correctly!
    }

    @Test 
    fun sortByTitle_toggle_verifiesOrderReversed() {
        // First sort by Title ascending
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.TITLE).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        // Get song nodes
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID)))
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID)))
        
        // Sort by Title again to toggle to descending
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.TITLE).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        // Get new order
        val newSong1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val newSong2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val newSong3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // For Title descending, expected order is: TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_3_TITLE
        // Verify song2 comes before song1
        assert(newSong2Y < newSong1Y) {
            "Expected '${TestConstants.Songs.SONG_2_TITLE}' (song2) to be above '${TestConstants.Songs.SONG_1_TITLE}' (song1) in descending order. " +
            "song2.top=$newSong2Y, song1.top=$newSong1Y"
        }
        
        // Verify song1 comes before song3
        assert(newSong1Y < newSong3Y) {
            "Expected '${TestConstants.Songs.SONG_1_TITLE}' (song1) to be above '${TestConstants.Songs.SONG_3_TITLE}' (song3) in descending order. " +
            "song1.top=$newSong1Y, song3.top=$newSong3Y"
        }
        
        // This proves toggle functionality is working!
    }

    @Test
    fun sortByArtist_ascending_verifiesCorrectOrder() {
        // Expected Artist sort (ASC): TestConstants.Songs.SONG_1_ARTIST → TestConstants.Songs.SONG_2_ARTIST → TestConstants.Songs.SONG_3_ARTIST
        // Which corresponds to: TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_3_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ARTIST).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Test Artist 1
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Test Artist 2
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Test Artist 3
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // Verify song1 (Test Artist 1) comes before song2 (Test Artist 2)
        assert(song1Y < song2Y) {
            "Expected '${TestConstants.Songs.SONG_1_ARTIST}' (song1) to be above '${TestConstants.Songs.SONG_2_ARTIST}' (song2). " +
            "song1.top=$song1Y, song2.top=$song2Y"
        }
        
        // Verify song2 (Test Artist 2) comes before song3 (Test Artist 3) 
        assert(song2Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_2_ARTIST}' (song2) to be above '${TestConstants.Songs.SONG_3_ARTIST}' (song3). " +
            "song2.top=$song2Y, song3.top=$song3Y"
        }
        
        // This proves Artist sorting is working correctly!
    }

    @Test
    fun sortByTitle_descending_verifiesCorrectOrder() {
        // Sort by Title twice to get descending order
        // Expected Title sort (DESC): TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_3_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.TITLE).performClick() // First click = ASC
        composeTestRule.waitForIdle()
        Thread.sleep(500)
        
        openSortBottomSheet() 
        composeTestRule.onNodeWithText(TestConstants.SortLabels.TITLE).performClick() // Second click = DESC
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Test Song 1
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Test Song 2  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Another Test Song
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // For Title descending: song2 (Test Song 2) → song1 (Test Song 1) → song3 (Another Test Song)
        assert(song2Y < song1Y) {
            "Expected '${TestConstants.Songs.SONG_2_TITLE}' (song2) to be above '${TestConstants.Songs.SONG_1_TITLE}' (song1) in DESC order. " +
            "song2.top=$song2Y, song1.top=$song1Y"
        }
        
        assert(song1Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_1_TITLE}' (song1) to be above '${TestConstants.Songs.SONG_3_TITLE}' (song3) in DESC order. " +
            "song1.top=$song1Y, song3.top=$song3Y"
        }
    }

    @Test
    fun sortByArtist_descending_verifiesCorrectOrder() {
        // Sort by Artist twice to get descending order
        // Expected Artist sort (DESC): TestConstants.Songs.SONG_3_ARTIST → TestConstants.Songs.SONG_2_ARTIST → TestConstants.Songs.SONG_1_ARTIST
        // Which corresponds to: TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_1_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ARTIST).performClick() // First click = ASC
        composeTestRule.waitForIdle()
        Thread.sleep(500)
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ARTIST).performClick() // Second click = DESC
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Test Artist 1
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Test Artist 2
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Test Artist 3
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // For Artist descending: song3 (Test Artist 3) → song2 (Test Artist 2) → song1 (Test Artist 1)
        assert(song3Y < song2Y) {
            "Expected '${TestConstants.Songs.SONG_3_ARTIST}' (song3) to be above '${TestConstants.Songs.SONG_2_ARTIST}' (song2) in DESC order. " +
            "song3.top=$song3Y, song2.top=$song2Y"
        }
        
        assert(song2Y < song1Y) {
            "Expected '${TestConstants.Songs.SONG_2_ARTIST}' (song2) to be above '${TestConstants.Songs.SONG_1_ARTIST}' (song1) in DESC order. " +
            "song2.top=$song2Y, song1.top=$song1Y"
        }
    }

    @Test
    fun sortByAlbum_ascending_verifiesCorrectOrder() {
        // Expected Album sort (ASC): "Album A" → "Album B" → "Album C"
        // Which corresponds to: TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_2_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ALBUM).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Album A
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Album C  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Album B
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // Verify song1 (Album A) comes before song3 (Album B)
        assert(song1Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_1_ALBUM}' (song1) to be above '${TestConstants.Songs.SONG_3_ALBUM}' (song3). " +
            "song1.top=$song1Y, song3.top=$song3Y"
        }
        
        // Verify song3 (Album B) comes before song2 (Album C)
        assert(song3Y < song2Y) {
            "Expected '${TestConstants.Songs.SONG_3_ALBUM}' (song3) to be above '${TestConstants.Songs.SONG_2_ALBUM}' (song2). " +
            "song3.top=$song3Y, song2.top=$song2Y"
        }
        
        // This proves Album sorting is working correctly!
    }

    @Test
    fun sortByAlbum_descending_verifiesCorrectOrder() {
        // Expected Album sort (DESC): "Album C" → "Album B" → "Album A"
        // Which corresponds to: TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_1_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ALBUM).performClick() // First click = ASC
        composeTestRule.waitForIdle()
        Thread.sleep(500)
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.ALBUM).performClick() // Second click = DESC
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))) // Album A
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))) // Album C  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))) // Album B
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // For Album descending: song2 (Album C) → song3 (Album B) → song1 (Album A)
        assert(song2Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_2_ALBUM}' (song2) to be above '${TestConstants.Songs.SONG_3_ALBUM}' (song3) in DESC order. " +
            "song2.top=$song2Y, song3.top=$song3Y"
        }
        
        assert(song3Y < song1Y) {
            "Expected '${TestConstants.Songs.SONG_3_ALBUM}' (song3) to be above '${TestConstants.Songs.SONG_1_ALBUM}' (song1) in DESC order. " +
            "song3.top=$song3Y, song1.top=$song1Y"
        }
        
        // This proves Album descending sorting is working correctly!
    }

    @Test
    fun sortByRecentlyAdded_ascending_verifiesCorrectOrder() {
        // Expected Recently Added sort (ASC): song1 (1000000L) → song3 (2000000L) → song2 (3000000L)
        // Oldest to newest: TestConstants.Songs.SONG_1_TITLE → TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_2_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.RECENTLY_ADDED).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID)))
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID)))
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // Verify song1 (oldest) comes before song3 (middle)
        assert(song1Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_1_TITLE}' (oldest: ${TestConstants.Songs.SONG_1_DATE_ADDED}) to be above '${TestConstants.Songs.SONG_3_TITLE}' (middle: ${TestConstants.Songs.SONG_3_DATE_ADDED}). " +
            "song1.top=$song1Y, song3.top=$song3Y"
        }
        
        // Verify song3 (middle) comes before song2 (newest)
        assert(song3Y < song2Y) {
            "Expected '${TestConstants.Songs.SONG_3_TITLE}' (middle: ${TestConstants.Songs.SONG_3_DATE_ADDED}) to be above '${TestConstants.Songs.SONG_2_TITLE}' (newest: ${TestConstants.Songs.SONG_2_DATE_ADDED}). " +
            "song3.top=$song3Y, song2.top=$song2Y"
        }
        
        // This proves Recently Added ascending sorting is working correctly!
    }

    @Test
    fun sortByRecentlyAdded_descending_verifiesCorrectOrder() {
        // Expected Recently Added sort (DESC): song2 (3000000L) → song3 (2000000L) → song1 (1000000L)
        // Newest to oldest: TestConstants.Songs.SONG_2_TITLE → TestConstants.Songs.SONG_3_TITLE → TestConstants.Songs.SONG_1_TITLE
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.RECENTLY_ADDED).performClick() // First click = ASC
        composeTestRule.waitForIdle()
        Thread.sleep(500)
        
        openSortBottomSheet()
        composeTestRule.onNodeWithText(TestConstants.SortLabels.RECENTLY_ADDED).performClick() // Second click = DESC
        composeTestRule.waitForIdle()
        Thread.sleep(1000)
        
        val song1Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID)))
        val song2Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))  
        val song3Node = composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID)))
        
        val song1Y = song1Node.fetchSemanticsNode().boundsInRoot.top
        val song2Y = song2Node.fetchSemanticsNode().boundsInRoot.top
        val song3Y = song3Node.fetchSemanticsNode().boundsInRoot.top
        
        // Verify song2 (newest) comes before song3 (middle)
        assert(song2Y < song3Y) {
            "Expected '${TestConstants.Songs.SONG_2_TITLE}' (newest: ${TestConstants.Songs.SONG_2_DATE_ADDED}) to be above '${TestConstants.Songs.SONG_3_TITLE}' (middle: ${TestConstants.Songs.SONG_3_DATE_ADDED}) in DESC order. " +
            "song2.top=$song2Y, song3.top=$song3Y"
        }
        
        // Verify song3 (middle) comes before song1 (oldest)
        assert(song3Y < song1Y) {
            "Expected '${TestConstants.Songs.SONG_3_TITLE}' (middle: ${TestConstants.Songs.SONG_3_DATE_ADDED}) to be above '${TestConstants.Songs.SONG_1_TITLE}' (oldest: ${TestConstants.Songs.SONG_1_DATE_ADDED}) in DESC order. " +
            "song3.top=$song3Y, song1.top=$song1Y"
        }
        
        // This proves Recently Added descending sorting is working correctly!
    }

    @Test
    fun sortingDoesNotLoseSongs() {
        // Verify that sorting operations don't cause songs to disappear
        
        val sortOptions = listOf(
            TestConstants.SortLabels.TITLE, 
            TestConstants.SortLabels.ARTIST, 
            TestConstants.SortLabels.ALBUM, 
            TestConstants.SortLabels.RECENTLY_ADDED
        )
        
        for (sortOption in sortOptions) {
            // Test ascending
            openSortBottomSheet()
            composeTestRule.onNodeWithText(sortOption).performClick()
            composeTestRule.waitForIdle()
            Thread.sleep(500)
            
            // Verify all 3 songs are still present
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertIsDisplayed()
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))).assertIsDisplayed()
            
            // Test descending
            openSortBottomSheet()
            composeTestRule.onNodeWithText(sortOption).performClick()
            composeTestRule.waitForIdle()
            Thread.sleep(500)
            
            // Verify all 3 songs are still present after toggle
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertIsDisplayed()
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
            composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_3_ID))).assertIsDisplayed()
        }
        
        // This proves sorting doesn't break the data integrity!
    }

    private fun navigateToMusicScreen() {
        waitForBottomNav()
        composeTestRule.onNodeWithText(TestConstants.Navigation.MUSIC).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(2000)
    }

    private fun openSortBottomSheet() {
        // Click app bar's 3-dots menu using specific test tag
        composeTestRule.onNodeWithTag(UITestTags.APP_BAR_MORE_OPTIONS).performClick()
        composeTestRule.waitForIdle()
        
        // Click "Sort by" option from dropdown
        composeTestRule.onNodeWithText(TestConstants.ContentDescriptions.SORT_BY).performClick()
        composeTestRule.waitForIdle()
        Thread.sleep(500)
    }

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = 15000L) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle()
    }
}