package soly.lyricsgenerator.ui.screens.create_screen

import android.Manifest
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsEnabled
import androidx.compose.ui.test.assertIsNotEnabled
import androidx.compose.ui.test.assertTextContains
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onFirst
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextReplacement
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.R
import androidx.compose.ui.test.hasClickAction
import androidx.compose.ui.semantics.SemanticsProperties
import androidx.compose.ui.semantics.getOrNull
import dagger.hilt.android.testing.HiltAndroidRule
import soly.lyricsgenerator.ui.constants.UITestTags
import dagger.hilt.android.testing.HiltAndroidTest

/**
 * UI test for 5-second seek controls (forward and backward) in PasteLyricsScreen.
 * Tests the seek button functionality in the media controller component.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class PasteLyricsSeekControlsTest {

    companion object {
        private const val NAVIGATION_TIMEOUT_MS = 15000L
        private const val SCREEN_LOAD_TIMEOUT_MS = 10000L
        private const val DIALOG_TIMEOUT_MS = 3000L
        private const val MEDIA_CONTROLLER_TIMEOUT_MS = 5000L
    }

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule(order = 2)
    val permissionsRule: GrantPermissionRule = GrantPermissionRule.grant(
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle()
    }

    private fun navigateToCreateTab() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))
        
        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
    }

    private fun selectSong() {
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        composeTestRule.onNode(hasText(step1Text)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for navigation to SongPickerScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.song_list)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // Wait for songs to load and select the first one
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule
                .onAllNodes(hasTestTag(UITestTags.SONG_LIST))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click the first song in the list
        composeTestRule.onAllNodes(hasAnyAncestor(hasTestTag(UITestTags.SONG_LIST)) and hasClickAction())
            .onFirst()
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Wait for navigation back to CreateScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }
    }

    private fun navigateToPasteLyricsScreen() {
        // Navigate to create tab
        navigateToCreateTab()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // Select a song first
        selectSong()
        
        // Navigate to Add Lyrics step
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for lyrics input dialog
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // Click "Paste Lyrics" option
        val pasteLyricsText = composeTestRule.activity.getString(R.string.paste_lyrics_option)
        composeTestRule.onNode(hasText(pasteLyricsText)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for navigation to PasteLyricsScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // Add some test lyrics to enable media controls
        val pasteLyricsPlaceholder = composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)
        val testLyrics = """Verse 1: First line
Second line
Chorus: Third line"""
        
        composeTestRule.onNode(hasText(pasteLyricsPlaceholder)).performTextReplacement(testLyrics)
        composeTestRule.waitForIdle()
    }

    @Test
    fun seekControls_displayedCorrectly_whenSongIsSelected() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Verify seek backward button is displayed and enabled
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
            .assertIsEnabled()
        
        // Verify seek forward button is displayed and enabled
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
            .assertIsEnabled()
    }

    @Test
    fun seekBackwardButton_clickable_triggersSeekAction() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click seek backward button - should not crash and should be responsive
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Verify the button is still enabled after click (no error state)
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .assertIsEnabled()
    }

    @Test
    fun seekForwardButton_clickable_triggersSeekAction() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click seek forward button - should not crash and should be responsive
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Verify the button is still enabled after click (no error state)
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .assertIsEnabled()
    }

    @Test
    fun seekControls_bothButtons_canBeClickedSequentially() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click seek backward button multiple times
        repeat(2) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        // Click seek forward button multiple times
        repeat(3) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        // Verify both buttons are still functional
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .assertIsEnabled()
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .assertIsEnabled()
    }

    @Test
    fun seekControls_contentDescriptions_areAccessible() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // The content descriptions should be available for accessibility
        // This is tested implicitly by the button being found with test tags
        // and by the buttons being enabled, which means the content descriptions were properly set
        
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
        
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
    }

    @Test
    fun seekControls_positionDisplayExists_withTestTags() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Verify that position and duration text elements exist with proper test tags
        composeTestRule.onNode(hasTestTag(UITestTags.CURRENT_POSITION_TEXT))
            .assertIsDisplayed()
        
        composeTestRule.onNode(hasTestTag(UITestTags.DURATION_TEXT))
            .assertIsDisplayed()
        
        // Test that seek buttons trigger actions without errors
        repeat(2) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        repeat(2) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        // Verify UI remains stable after seek operations
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
            .assertIsEnabled()
            
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .assertIsDisplayed()
            .assertIsEnabled()
    }

    @Test
    fun seekForward5Seconds_actuallyChangesPositionBy5Seconds() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Start playback
        composeTestRule.onNode(hasTestTag(UITestTags.PLAY_PAUSE_BUTTON)).performClick()
        composeTestRule.waitForIdle()
        
        // Use forward seeks to advance position for testing
        repeat(2) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        // Get initial position
        val initialPosition = getCurrentPositionSeconds()
        
        // Click seek forward button
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
            .performClick()
        
        composeTestRule.waitForIdle()
        Thread.sleep(1000) // Wait for position update
        
        // Get new position
        val newPosition = getCurrentPositionSeconds()
        
        // Verify the position increased by approximately 5 seconds (±2 second tolerance)
        val difference = newPosition - initialPosition
        assert(difference in 3..7) {
            "Expected position to increase by ~5 seconds. Initial: ${initialPosition}s, New: ${newPosition}s, Difference: ${difference}s"
        }
    }

    @Test
    fun seekBackward5Seconds_actuallyChangesPositionBy5Seconds() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Start playback
        composeTestRule.onNode(hasTestTag(UITestTags.PLAY_PAUSE_BUTTON)).performClick()
        composeTestRule.waitForIdle()
        
        // Use forward seeks to advance position to at least 15 seconds for backward testing
        repeat(3) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
        }
        
        // Get initial position
        val initialPosition = getCurrentPositionSeconds()
        
        // Click seek backward button
        composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
            .performClick()
        
        composeTestRule.waitForIdle()
        Thread.sleep(1000) // Wait for position update
        
        // Get new position
        val newPosition = getCurrentPositionSeconds()
        
        // Verify the position decreased by approximately 5 seconds (±2 second tolerance)
        val difference = initialPosition - newPosition
        assert(difference in 3..7) {
            "Expected position to decrease by ~5 seconds. Initial: ${initialPosition}s, New: ${newPosition}s, Difference: ${difference}s"
        }
    }

    @Test
    fun seekControls_multipleSeeks_cumulativeChanges() {
        navigateToPasteLyricsScreen()
        
        // Wait for media controller to appear
        composeTestRule.waitUntil(timeoutMillis = MEDIA_CONTROLLER_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Get initial position (should be close to 0)
        val initialPosition = getCurrentPositionSeconds()
        
        // Click forward 3 times (should be +15 seconds total)
        repeat(3) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
            Thread.sleep(500)
        }
        
        val positionAfterForward = getCurrentPositionSeconds()
        val forwardDifference = positionAfterForward - initialPosition
        
        // Should be approximately 15 seconds forward (±3 second tolerance)
        assert(forwardDifference in 12..18) {
            "Expected ~15 second increase after 3 forward seeks. Initial: ${initialPosition}s, After: ${positionAfterForward}s, Difference: ${forwardDifference}s"
        }
        
        // Click backward 2 times (should be -10 seconds from current position)
        repeat(2) {
            composeTestRule.onNode(hasTestTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON))
                .performClick()
            composeTestRule.waitForIdle()
            Thread.sleep(500)
        }
        
        val finalPosition = getCurrentPositionSeconds()
        val backwardDifference = positionAfterForward - finalPosition
        
        // Should be approximately 10 seconds backward (±3 second tolerance)
        assert(backwardDifference in 7..13) {
            "Expected ~10 second decrease after 2 backward seeks. Before: ${positionAfterForward}s, After: ${finalPosition}s, Difference: ${backwardDifference}s"
        }
    }

    /**
     * Helper function to extract current position in seconds from the UI
     */
    private fun getCurrentPositionSeconds(): Int {
        return try {
            val node = composeTestRule.onNode(hasTestTag(UITestTags.CURRENT_POSITION_TEXT))
            val semanticsNode = node.fetchSemanticsNode()
            val textList = semanticsNode.config.getOrNull(SemanticsProperties.Text)
            val timeText = textList?.firstOrNull()?.text ?: "00:00"
            timeStringToSeconds(timeText)
        } catch (e: Exception) {
            // Fallback: return 0 if we can't read the position
            0
        }
    }

    /**
     * Helper function to convert time string (MM:SS) to seconds
     */
    private fun timeStringToSeconds(timeString: String): Int {
        val parts = timeString.split(":")
        if (parts.size != 2) return 0
        
        val minutes = parts[0].toIntOrNull() ?: 0
        val seconds = parts[1].toIntOrNull() ?: 0
        
        return minutes * 60 + seconds
    }
}