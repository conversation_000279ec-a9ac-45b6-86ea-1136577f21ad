package soly.lyricsgenerator.ui.screens.components

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.ui.theme.LyricsGeneratorTheme

/**
 * UI tests for TxtDisplay component
 */
@RunWith(AndroidJUnit4::class)
class TxtDisplayTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun txtDisplay_withTextLines_displaysAllLines() {
        // Given
        val textLines = listOf(
            "Verse 1: First line",
            "Chorus: Second line", 
            "Verse 2: Third line"
        )

        // When
        composeTestRule.setContent {
            LyricsGeneratorTheme {
                TxtDisplay(textLines = textLines)
            }
        }

        // Then
        textLines.forEach { line ->
            composeTestRule.onNode(hasText(line)).assertIsDisplayed()
        }
    }

    @Test
    fun txtDisplay_withEmptyList_showsNoLyricsMessage() {
        // Given
        val emptyTextLines = emptyList<String>()

        // When
        composeTestRule.setContent {
            LyricsGeneratorTheme {
                TxtDisplay(textLines = emptyTextLines)
            }
        }

        // Then
        composeTestRule.onNode(hasText("No lyrics available")).assertIsDisplayed()
    }

    @Test
    fun txtDisplay_withEmptyStrings_displaysEmptyLines() {
        // Given
        val textLinesWithEmpties = listOf(
            "First line",
            "",
            "Third line"
        )

        // When
        composeTestRule.setContent {
            LyricsGeneratorTheme {
                TxtDisplay(textLines = textLinesWithEmpties)
            }
        }

        // Then
        composeTestRule.onNode(hasText("First line")).assertIsDisplayed()
        composeTestRule.onNode(hasText("Third line")).assertIsDisplayed()
        // Empty string would be displayed but might not be easily testable
    }
}