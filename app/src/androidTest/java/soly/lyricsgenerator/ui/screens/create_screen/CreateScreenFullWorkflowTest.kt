package soly.lyricsgenerator.ui.screens.create_screen

import android.Manifest
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onFirst
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextReplacement
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.R
import androidx.compose.ui.test.hasClickAction
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import soly.lyricsgenerator.ui.constants.UITestTags

/**
 * Comprehensive end-to-end test that verifies the complete lyrics creation workflow:
 * 1. Select a song (Step 1)
 * 2. Add lyrics manually with multi-line text (Step 2)
 * 3. Save the lyrics
 * 4. Start timestamp editing (Step 3)
 * 5. Interact with timestamp editing interface
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class CreateScreenFullWorkflowTest {

    companion object {
        private const val NAVIGATION_TIMEOUT_MS = 15000L
        private const val SCREEN_LOAD_TIMEOUT_MS = 10000L
        private const val DIALOG_TIMEOUT_MS = 3000L
    }

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule(order = 2)
    val permissionsRule: GrantPermissionRule = GrantPermissionRule.grant(
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle()
    }

    private fun navigateToCreateTab() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))
        
        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
    }

    @Test
    fun endToEndLyricsCreation_selectSong_addMultilineText_startTimestampEditing() {
        // Navigate to create tab and wait for it to load
        navigateToCreateTab()
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }

        // ====== STEP 1: Choose Song ======
        
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        composeTestRule.onNode(hasText(step1Text)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for navigation to SongPickerScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.song_list)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // Wait for songs to load and select the first one by its title
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule
                .onAllNodes(hasTestTag(UITestTags.SONG_LIST))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click the first song in the list.
        // We find all clickable nodes within the SongList and click the first one.
        composeTestRule.onAllNodes(hasAnyAncestor(hasTestTag(UITestTags.SONG_LIST)) and hasClickAction())
            .onFirst()
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Wait for navigation back to CreateScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // ====== STEP 2: Add Lyrics ======
        
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for lyrics input dialog
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // Click "Paste Lyrics" option for manual text input
        val pasteLyricsText = composeTestRule.activity.getString(R.string.paste_lyrics_option)
        composeTestRule.onNode(hasText(pasteLyricsText)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for navigation to PasteLyricsScreen
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)))
                .fetchSemanticsNodes().size >= 1
        }
        
        // ====== Multi-line Text Input ======
        
        val pasteLyricsPlaceholder = composeTestRule.activity.getString(R.string.paste_lyrics_placeholder)
        val multiLineLyrics = """Verse 1: This is the first line
This is the second line after enter
And here comes the third line

Chorus: This is the chorus line  
With more lyrics here
Final line of our test song"""
        
        // Input multi-line lyrics
        composeTestRule.onNode(hasText(pasteLyricsPlaceholder)).performTextReplacement(multiLineLyrics)
        composeTestRule.waitForIdle()
        
        // ====== Save Lyrics ======
        
        val saveText = composeTestRule.activity.getString(R.string.save)
        composeTestRule.onNode(hasText(saveText)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for navigation back to CreateScreen with completed steps
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // ====== STEP 3: Start Timestamp Editing ======
        
        val step3Text = composeTestRule.activity.getString(R.string.step_text_start)
        composeTestRule.onNode(hasText(step3Text)).assertIsDisplayed()
        
        // Click START to transition to timestamp editing mode
        composeTestRule.onNode(hasText(step3Text)).performClick()
        composeTestRule.waitForIdle()
        
        // ====== Verify Timestamp Editing Interface ======
        
        // Wait for transition to timestamp editing mode - lyrics should appear
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText("Verse 1: This is the first line"))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Verify that the first line is displayed
        composeTestRule.onAllNodes(hasText("Verse 1: This is the first line")).onFirst().assertIsDisplayed()
    }
}