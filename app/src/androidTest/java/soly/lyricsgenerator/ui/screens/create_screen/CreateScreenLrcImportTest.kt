package soly.lyricsgenerator.ui.screens.create_screen

import android.app.Activity
import android.app.Instrumentation
import android.content.Intent
import android.net.Uri
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.performClick
import androidx.test.espresso.intent.Intents
import androidx.test.espresso.intent.matcher.IntentMatchers
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.constants.UITestTags

/**
 * UI test that verifies the user flow of importing a valid LRC file on the Create screen,
 * specifically focusing on the steps component progression.
 * 
 * This test verifies:
 * - Navigation to Create tab works
 * - Initial steps component state (Step 1: Choose Song, Step 2: Add Lyrics)
 * - Clicking "Add lyrics" step shows import options dialog
 * - UI elements are properly displayed and interactive
 * - Steps component behavior and progression
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class CreateScreenLrcImportTest {

    companion object {
        private const val NAVIGATION_TIMEOUT_MS = 10000L
        private const val SCREEN_LOAD_TIMEOUT_MS = 5000L
        private const val DIALOG_TIMEOUT_MS = 3000L
    }

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule
    val grantPermissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.POST_NOTIFICATIONS,
        android.Manifest.permission.READ_MEDIA_AUDIO
    )

    @Before
    fun setUp() {
        hiltRule.inject()
        navigateToCreateTab()
        
        // Wait for the Create screen to load and steps component to appear
        composeTestRule.waitUntil(timeoutMillis = SCREEN_LOAD_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.steps_title)))
                .fetchSemanticsNodes().size == 1
        }
    }
    
    @After
    fun tearDown() {
        // Clean up Intents if any test initialized them
        try {
            Intents.release()
        } catch (e: Exception) {
            // Intents was not initialized, which is fine
        }
    }

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = NAVIGATION_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle()
    }

    private fun navigateToCreateTab() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))
        
        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
    }

    @Test
    fun navigateToCreate_showsInitialStepsState() {
        // Assert that the steps title is displayed
        val stepsTitle = composeTestRule.activity.getString(R.string.steps_title)
        composeTestRule.onNode(hasText(stepsTitle)).assertIsDisplayed()
        
        // Assert Step 1 text is displayed (Choose a song)
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        composeTestRule.onNode(hasText(step1Text)).assertIsDisplayed()
        
        // Assert Step 2 text is displayed (Add lyrics)
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).assertIsDisplayed()
        
        // Assert Step 3 text is displayed (START)
        val step3Text = composeTestRule.activity.getString(R.string.step_text_start)
        composeTestRule.onNode(hasText(step3Text)).assertIsDisplayed()
    }

    @Test
    fun clickAddLyricsStep_showsImportDialog() {
        // Click on Step 2 (Add lyrics) to trigger the lyrics import dialog
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        composeTestRule.onNode(hasText(step2Text)).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for and verify the import dialog appears
        composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
            composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                .fetchSemanticsNodes().size == 1
        }
        
        // Assert that the import dialog title is displayed
        val dialogTitle = composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)
        composeTestRule.onNode(hasText(dialogTitle)).assertIsDisplayed()
        
        // Assert that import TXT option is displayed
        val importTxtText = composeTestRule.activity.getString(R.string.import_txt_option)
        composeTestRule.onNode(hasText(importTxtText)).assertIsDisplayed()
        
        // Assert that paste lyrics option is displayed
        val pasteLyricsText = composeTestRule.activity.getString(R.string.paste_lyrics_option)
        composeTestRule.onNode(hasText(pasteLyricsText)).assertIsDisplayed()
    }

    @Test
    fun clickAddLyricsStep_thenClickImportTxt_opensFilePicker() {
        // Initialize Espresso Intents only for this test
        Intents.init()
        
        try {
            // Mock the file picker intent to return a dummy text file
            val mockUri = Uri.parse("content://com.android.providers.media.documents/document/text%3A12345")
            val resultData = Intent().apply {
                data = mockUri
            }
            val result = Instrumentation.ActivityResult(Activity.RESULT_OK, resultData)
            
            // Intercept and mock the ACTION_OPEN_DOCUMENT intent (used by file picker)
            Intents.intending(IntentMatchers.hasAction(Intent.ACTION_OPEN_DOCUMENT))
                .respondWith(result)
        
            // Click on Step 2 (Add lyrics) to trigger the lyrics import dialog
            val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
            composeTestRule.onNode(hasText(step2Text)).performClick()
            composeTestRule.waitForIdle()
            
            // Wait for and verify the import dialog appears
            composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
                composeTestRule.onAllNodes(hasText(composeTestRule.activity.getString(R.string.lyrics_input_option_dialog_title)))
                    .fetchSemanticsNodes().size == 1
            }
            
            // Click on Import TXT option to trigger file picker
            val importTxtText = composeTestRule.activity.getString(R.string.import_txt_option)
            composeTestRule.onNode(hasText(importTxtText)).performClick()
            
            // Wait for file picker interaction and subsequent dialog closure
            composeTestRule.waitForIdle()
            
            // Give some time for the mocked file picker to process
            Thread.sleep(1000)
            
            // Verify the dialog closed and we're back to the main Create screen
            val stepsTitle = composeTestRule.activity.getString(R.string.steps_title)
            composeTestRule.waitUntil(timeoutMillis = DIALOG_TIMEOUT_MS) {
                composeTestRule.onAllNodes(hasText(stepsTitle))
                    .fetchSemanticsNodes().size == 1
            }
            
            // Verify the steps component is displayed (indicating successful file picker interaction)
            composeTestRule.onNode(hasText(stepsTitle)).assertIsDisplayed()
        } finally {
            // Clean up Espresso Intents
            Intents.release()
        }
    }

    @Test
    fun stepsComponent_displaysCorrectSequence() {
        // Verify that all three steps are displayed in the correct order
        val step1Text = composeTestRule.activity.getString(R.string.step_text_choose_song)
        val step2Text = composeTestRule.activity.getString(R.string.step_text_add_lyrics)
        val step3Text = composeTestRule.activity.getString(R.string.step_text_start)
        
        // Assert all step texts are displayed initially
        composeTestRule.onNode(hasText(step1Text)).assertIsDisplayed()
        composeTestRule.onNode(hasText(step2Text)).assertIsDisplayed()
        composeTestRule.onNode(hasText(step3Text)).assertIsDisplayed()
        
        // Verify the steps title is displayed
        val stepsTitle = composeTestRule.activity.getString(R.string.steps_title)
        composeTestRule.onNode(hasText(stepsTitle)).assertIsDisplayed()
        
        // Test sequence verification: all steps should be present in the correct order
        // and the entire steps component should be functional
        composeTestRule.waitForIdle()
        
        // Re-verify all components are still present (regression test)
        composeTestRule.onNode(hasText(stepsTitle)).assertIsDisplayed()
        composeTestRule.onNode(hasText(step1Text)).assertIsDisplayed()
        composeTestRule.onNode(hasText(step2Text)).assertIsDisplayed()
        composeTestRule.onNode(hasText(step3Text)).assertIsDisplayed()
    }
}