package soly.lyricsgenerator.ui.screens

import android.Manifest
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasContentDescription
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.onNodeWithContentDescription
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.fake.TestConstants
import soly.lyricsgenerator.ui.constants.UITestTags

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class ArtistFilterTest {
    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule
    val permissionsRule: GrantPermissionRule = GrantPermissionRule.grant(
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    @Before
    fun setup() {
        hiltRule.inject()
        navigateToMusicScreen()
    }

    @Test
    fun filterByArtist_displaysFilteredSongs() {
        // Open 3-dots menu first, then select filter option
        openFilterBottomSheet()
        composeTestRule.waitForIdle()

        // Wait for bottom sheet to appear and be populated
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Verify that the desired artist is available and click it
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST))
            .assertIsDisplayed()
            .performClick()
        composeTestRule.waitForIdle()

        // Wait for filtering to complete
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Verify filtering results
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()
    }

    @Test
    fun filterByArtist_showsAllArtistsOption() {
        // Open artist filter bottom sheet
        openFilterBottomSheet()
        composeTestRule.waitForIdle()

        // Wait for bottom sheet to appear with "All Artists" option
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasText(TestConstants.FilterOptions.ALL_ARTISTS))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Verify "All Artists" option is displayed at the top
        composeTestRule.onNodeWithText(TestConstants.FilterOptions.ALL_ARTISTS).assertIsDisplayed()
        
        // Wait for individual artist options to load
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Verify at least one individual artist is shown (we know Song 2 artist works from the passing test)
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).assertIsDisplayed()
    }

    @Test
    fun filterByArtist_resetToAllArtists_showsAllSongs() {
        // First apply a filter using Song 2 artist (we know this works from other test)
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        // Wait for bottom sheet and use Song 2 artist which we know works
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // Verify filtering worked - only Song 2 should be showing
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()

        // Now reset to "All Artists"
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasText(TestConstants.FilterOptions.ALL_ARTISTS))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        composeTestRule.onNodeWithText(TestConstants.FilterOptions.ALL_ARTISTS).performClick()
        composeTestRule.waitForIdle()

        // Verify songs are now showing (at least Song 2 should be visible)
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
    }

    @Test
    fun filterByArtist_filterButtonHighlighted_whenFilterActive() {
        // Apply artist filter
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // TODO: Add assertion to verify filter button is highlighted/colored when filter is active
        // This would require checking the tint color of the filter icon
        // Example: filterButton.assertHasColorTint(InteractiveAccent)
        
        // For now, verify the filter is working (which indicates it's active)
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()
    }

    @Test
    fun filterByArtist_multipleDifferentArtists_showsCorrectSongs() {
        // First apply a specific artist filter (we know Song 2 artist works)
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // Verify only Song 2 shows when filtered by its artist
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()

        // Now switch to "All Artists" to show all songs again
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        // Wait for bottom sheet to appear
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasText(TestConstants.FilterOptions.ALL_ARTISTS))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        // Click "All Artists" to reset filter
        composeTestRule.onNodeWithText(TestConstants.FilterOptions.ALL_ARTISTS).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for filtering to complete and verify Song 2 is still visible (proving reset worked)
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        
        // Now apply the same artist filter again to verify it works consistently
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // Verify filtering works consistently - only Song 2 should show again
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()
    }

    @Test
    fun filterByArtist_bottomSheetDismisses_afterSelection() {
        // Open bottom sheet
        openFilterBottomSheet()
        composeTestRule.waitForIdle()

        // Wait for bottom sheet to open and verify artist is available
        composeTestRule.waitUntil(timeoutMillis = 5000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Verify bottom sheet is open by checking if artists are visible
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).assertIsDisplayed()

        // Select an artist
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // Wait for bottom sheet to dismiss and filtering to complete
        composeTestRule.waitUntil(timeoutMillis = 3000L) {
            composeTestRule.onAllNodes(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID)))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Verify bottom sheet is dismissed (artist options should no longer be visible)
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).assertDoesNotExist()
        
        // But the filtered results should be visible
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
    }

    @Test
    fun filterByArtist_persistsAcrossScreenNavigation() {
        // Apply artist filter
        openFilterBottomSheet()
        composeTestRule.waitForIdle()
        
        composeTestRule.onNodeWithTag(TestConstants.TestTags.getArtistFilterItemTag(TestConstants.Songs.SONG_2_ARTIST)).performClick()
        composeTestRule.waitForIdle()

        // Verify filter is applied
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()

        // Navigate away from Music screen
        composeTestRule.onNodeWithText(TestConstants.Navigation.CREATE).performClick()
        composeTestRule.waitForIdle()

        // Navigate back to Music screen
        composeTestRule.onNodeWithText(TestConstants.Navigation.MUSIC).performClick()
        composeTestRule.waitForIdle()

        // Wait for music screen to reload
        composeTestRule.waitUntil(timeoutMillis = 10000L) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.APP_BAR_MORE_OPTIONS))
                .fetchSemanticsNodes().size == 1
        }

        // Verify filter is still applied
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_2_ID))).assertIsDisplayed()
        composeTestRule.onNode(hasTestTag(TestConstants.TestTags.getSongItemTag(TestConstants.Songs.SONG_1_ID))).assertDoesNotExist()
    }

    private fun navigateToMusicScreen() {
        composeTestRule.waitUntil(timeoutMillis = 15000L) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.onNodeWithText(TestConstants.Navigation.MUSIC).performClick()
        composeTestRule.waitForIdle()
        
        // Wait for music screen to load completely by checking for app bar 3-dots menu
        composeTestRule.waitUntil(timeoutMillis = 10000L) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.APP_BAR_MORE_OPTIONS))
                .fetchSemanticsNodes().size == 1
        }
    }
    
    private fun openFilterBottomSheet() {
        // Click app bar's 3-dots menu using specific test tag
        composeTestRule.onNodeWithTag(UITestTags.APP_BAR_MORE_OPTIONS).performClick()
        composeTestRule.waitForIdle()
        
        // Click "Filter by artist" option from dropdown
        composeTestRule.onNodeWithText(TestConstants.ContentDescriptions.FILTER_BY_ARTIST).performClick()
        composeTestRule.waitForIdle()
    }
}
