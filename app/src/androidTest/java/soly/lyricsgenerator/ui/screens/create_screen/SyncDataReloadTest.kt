package soly.lyricsgenerator.ui.screens.create_screen

import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import javax.inject.Inject
import kotlinx.coroutines.runBlocking
import soly.lyricsgenerator.domain.database.AppDatabase
import soly.lyricsgenerator.domain.database.dao.FileDao
import soly.lyricsgenerator.domain.database.dao.SongDao
import soly.lyricsgenerator.domain.database.model.File as DatabaseFile
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.Song
import org.junit.Test
import androidx.compose.ui.test.onNodeWithText
import androidx.test.rule.GrantPermissionRule
import soly.lyricsgenerator.R
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.onAllNodesWithContentDescription
import androidx.compose.ui.test.onAllNodesWithText
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsSelected
import soly.lyricsgenerator.ui.constants.UITestTags.BOTTOM_NAVIGATION_BAR
import java.io.File

@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class SyncDataReloadTest {

    companion object {
        // Test file names
        private const val TEST_RTF_FILE_NAME = "Test.rtf"
        private const val TEST_TXT_FILE_NAME = "Test.txt"
        private const val TEST_LRC_FILE_NAME = "Test.lrc"
        
        // Test song data
        private const val TEST_SONG_TITLE = "Test Song"
        private const val TEST_SONG_ARTIST = "Test Artist"
        private const val TEST_SONG_PATH = "/path/to/song.mp3"
        private const val TEST_SONG_DURATION = 180000L
        
        // Test lyrics content
        private const val LYRIC_LINE_ONE = "Line one of the lyrics"
        private const val LYRIC_LINE_TWO = "Line two of the lyrics"
        private const val LYRIC_LINE_THREE = "Line three of the lyrics"
        private const val LYRIC_LINE_FOUR = "Line four of the lyrics"
        private const val LYRIC_LINE_FIVE = "Line five of the lyrics"
        
        // RTF content template
        private const val RTF_CONTENT = """{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
\f0\fs24 $LYRIC_LINE_ONE
\par $LYRIC_LINE_TWO  
\par $LYRIC_LINE_THREE
\par $LYRIC_LINE_FOUR
\par $LYRIC_LINE_FIVE}"""
        
        // TXT content
        private const val TXT_CONTENT = """$LYRIC_LINE_ONE
$LYRIC_LINE_TWO
$LYRIC_LINE_THREE
$LYRIC_LINE_FOUR
$LYRIC_LINE_FIVE"""
        
        // LRC content with timestamps
        private const val LRC_CONTENT = """[00:12.34]$LYRIC_LINE_ONE
[00:24.56]$LYRIC_LINE_TWO
[00:36.78]$LYRIC_LINE_THREE
[00:48.90]$LYRIC_LINE_FOUR
[01:01.23]$LYRIC_LINE_FIVE"""
        
        // Test timing constants
        private const val UI_WAIT_TIME_MS = 1000L
        private const val NAVIGATION_WAIT_TIME_MS = 2000L
    }

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule
    val grantPermissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.POST_NOTIFICATIONS, android.Manifest.permission.READ_MEDIA_AUDIO
    )

    @Inject
    lateinit var appDatabase: AppDatabase

    @Inject
    lateinit var fileDao: FileDao

    @Inject
    lateinit var songDao: SongDao

    @Before
    fun setUp() {
        hiltRule.inject()
        runBlocking {
            appDatabase.clearAllTables()
            songDao.insert(
                Song(
                    id = 1,
                    title = TEST_SONG_TITLE,
                    artist = TEST_SONG_ARTIST,
                    data = TEST_SONG_PATH,
                    duration = TEST_SONG_DURATION,
                    isFavorite = false
                )
            )

            // Create real files in app's internal storage
            val context = composeTestRule.activity.applicationContext
            val filesDir = context.filesDir

            // Create RTF file with sample content
            val rtfFile = File(filesDir, TEST_RTF_FILE_NAME)
            rtfFile.writeText(RTF_CONTENT)

            // Create TXT file with sample content
            val txtFile = File(filesDir, TEST_TXT_FILE_NAME)
            txtFile.writeText(TXT_CONTENT)

            // Create LRC file with sample content
            val lrcFile = File(filesDir, TEST_LRC_FILE_NAME)
            lrcFile.writeText(LRC_CONTENT)

            // Insert database records with real file paths
            fileDao.insert(
                DatabaseFile(
                    id = 1,
                    fileName = TEST_RTF_FILE_NAME,
                    filePath = rtfFile.absolutePath,
                    fileType = FileType.RTF,
                    songId = 1
                )
            )
            fileDao.insert(
                DatabaseFile(
                    id = 2,
                    fileName = TEST_TXT_FILE_NAME,
                    filePath = txtFile.absolutePath,
                    fileType = FileType.TXT,
                    songId = 1
                )
            )
            fileDao.insert(
                DatabaseFile(
                    id = 3,
                    fileName = TEST_LRC_FILE_NAME,
                    filePath = lrcFile.absolutePath,
                    fileType = FileType.LRC,
                    songId = 1
                )
            )
        }
    }

    private fun waitForBottomNav() {
        composeTestRule.waitForIdle()
        Thread.sleep(NAVIGATION_WAIT_TIME_MS) // Simple wait for UI to be ready
    }

    @Test
    fun fileListIsDisplayed() {
        waitForBottomNav()

        // Navigate to Files screen using the same approach as MainActivityBasicUITest
        val filesTabText = composeTestRule.activity.getString(R.string.files)
        val filesTabMatcher =
            hasText(filesTabText).and(hasAnyAncestor(hasTestTag(BOTTOM_NAVIGATION_BAR)))
        composeTestRule.onNode(filesTabMatcher).performClick()
        composeTestRule.waitForIdle()

        // Verify files are displayed
        composeTestRule.onNodeWithText(TEST_RTF_FILE_NAME).assertIsDisplayed()
        composeTestRule.onNodeWithText(TEST_TXT_FILE_NAME).assertIsDisplayed()
        composeTestRule.onNodeWithText(TEST_LRC_FILE_NAME).assertIsDisplayed()

        performSyncAndVerify()

        // Navigate back to Files screen to complete the cycle
        composeTestRule.onNode(filesTabMatcher).performClick()
        composeTestRule.waitForIdle()

        // Verify confirmation dialog appears ("You have unsaved changes")
        val unsavedChangesText = composeTestRule.activity.getString(R.string.unsaved_changes_message)
        composeTestRule.onNodeWithText(unsavedChangesText).assertIsDisplayed()
        
        // Click "Yes, leave" button
        val yesLeaveText = composeTestRule.activity.getString(R.string.yes_leave)
        composeTestRule.onNodeWithText(yesLeaveText).performClick()
        composeTestRule.waitForIdle()
        
        // Verify we're back on Files screen
        composeTestRule.onNode(filesTabMatcher).assertIsSelected()

        // Verify files are still displayed in the list
        composeTestRule.onNodeWithText(TEST_RTF_FILE_NAME).assertIsDisplayed()
        composeTestRule.onNodeWithText(TEST_TXT_FILE_NAME).assertIsDisplayed()
        composeTestRule.onNodeWithText(TEST_LRC_FILE_NAME).assertIsDisplayed()

        performSyncAndVerify()

    }

    private fun performSyncAndVerify() {
        // Click on the three dots menu (More options button) - get the first one (Test.rtf)
        val moreOptionsText = composeTestRule.activity.getString(R.string.content_desc_more_options)
        composeTestRule.onAllNodesWithContentDescription(moreOptionsText)[0].performClick()
        composeTestRule.waitForIdle()

        // Click on "Sync" option
        val syncText = composeTestRule.activity.getString(R.string.sync)
        composeTestRule.onNodeWithText(syncText).performClick()
        composeTestRule.waitForIdle()

        // Verify we navigated to Create screen (sync mode)
        // The sync should load the file content and navigate to Create screen

        // Wait for navigation to complete
        composeTestRule.waitForIdle()
        Thread.sleep(UI_WAIT_TIME_MS) // Give time for sync and navigation

        // Check that we're now on Create screen by verifying Create tab is selected
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher =
            hasText(createTabText).and(hasAnyAncestor(hasTestTag(BOTTOM_NAVIGATION_BAR)))
        composeTestRule.onNode(createTabMatcher).assertIsSelected()

        // Verify that lyrics content is loaded and displayed (this confirms sync worked)
        // The RTF content should be parsed and displayed as individual lyric lines
        // Use onAllNodesWithText to handle multiple occurrences and check that at least one is displayed
        composeTestRule.onAllNodesWithText(LYRIC_LINE_ONE)[0].assertIsDisplayed()
        composeTestRule.onAllNodesWithText(LYRIC_LINE_TWO)[0].assertIsDisplayed()
        composeTestRule.onAllNodesWithText(LYRIC_LINE_THREE)[0].assertIsDisplayed()
    }
} 