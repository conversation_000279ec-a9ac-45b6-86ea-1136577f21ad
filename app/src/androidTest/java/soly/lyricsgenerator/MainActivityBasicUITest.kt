package soly.lyricsgenerator

import androidx.compose.ui.test.assertIsSelected
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.compose.ui.test.performClick
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.ui.constants.UITestTags

/**
 * Basic UI test for MainActivity.
 * This test currently only verifies that the MainActivity can be launched.
 * To make it more meaningful, it should be updated to check for specific
 * Composable elements within MainActivity's content.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class MainActivityBasicUITest {

    @get:Rule(order = 0)
    var hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>() // Assuming MainActivity is your main activity

    @get:Rule
    val grantPermissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.POST_NOTIFICATIONS,
        android.Manifest.permission.READ_MEDIA_AUDIO
    )
    
    @Before
    fun setup() {
        hiltRule.inject()
    }

    private fun waitForBottomNav() {
        composeTestRule.waitUntil(timeoutMillis = 10000) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR))
                .fetchSemanticsNodes().size == 1
        }
        composeTestRule.waitForIdle() // Ensure it's settled after appearing
    }

    @Test
    fun activityLaunches() {
        waitForBottomNav() // Ensure basic UI is ready
        // This test now also implicitly checks if the bottom nav appears.
        // For a more specific check, you could assert a default selected tab.
        val musicTabText = composeTestRule.activity.getString(R.string.music)
        composeTestRule.onNode(hasText(musicTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))).assertIsSelected()
    }

    @Test
    fun navigateToCreateTab_assertIsSelected() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))

        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
        composeTestRule.onNode(createTabMatcher).assertIsSelected()
    }

    @Test
    fun navigateToFilesTab_assertIsSelected() {
        waitForBottomNav()
        val filesTabText = composeTestRule.activity.getString(R.string.files)
        val filesTabMatcher = hasText(filesTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))

        composeTestRule.onNode(filesTabMatcher).performClick()
        composeTestRule.waitForIdle()
        composeTestRule.onNode(filesTabMatcher).assertIsSelected()
    }

    @Test
    fun navigateToMusicTab_afterNavigatingAway_assertIsSelected() {
        waitForBottomNav()
        val createTabText = composeTestRule.activity.getString(R.string.create)
        val musicTabText = composeTestRule.activity.getString(R.string.music)

        val createTabMatcher = hasText(createTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))
        val musicTabMatcher = hasText(musicTabText).and(hasAnyAncestor(hasTestTag(UITestTags.BOTTOM_NAVIGATION_BAR)))

        // First, navigate to a different tab (Create)
        composeTestRule.onNode(createTabMatcher).performClick()
        composeTestRule.waitForIdle()
        composeTestRule.onNode(createTabMatcher).assertIsSelected()

        // Then, navigate back to the Music tab
        composeTestRule.onNode(musicTabMatcher).performClick()
        composeTestRule.waitForIdle()
        composeTestRule.onNode(musicTabMatcher).assertIsSelected()
    }
} 