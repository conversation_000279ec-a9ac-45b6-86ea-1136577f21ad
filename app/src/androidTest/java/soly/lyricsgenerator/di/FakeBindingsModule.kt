package soly.lyricsgenerator.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.data.repository.ContentResolverRepositoryImpl
import soly.lyricsgenerator.data.repository.PreferencesRepositoryImpl
import soly.lyricsgenerator.domain.repository.ContentResolverRepository
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import soly.lyricsgenerator.fake.FakeMusicRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class FakeBindingsModule {

    @Binds
    @Singleton
    abstract fun bindMusicRepository(
        fakeMusicRepository: FakeMusicRepository
    ): IMusicRepository

    @Binds
    @Singleton
    abstract fun bindPreferencesRepository(
        preferencesRepositoryImpl: PreferencesRepositoryImpl
    ): PreferencesRepository

    @Binds
    @Singleton
    abstract fun bindContentResolverRepository(
        contentResolverRepositoryImpl: ContentResolverRepositoryImpl
    ): ContentResolverRepository
} 