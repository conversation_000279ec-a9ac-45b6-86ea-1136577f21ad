package soly.lyricsgenerator.fake

object TestConstants {
    
    object Songs {
        const val SONG_1_TITLE = "Test Song 1"
        const val SONG_1_ARTIST = "Test Artist 1"
        const val SONG_1_ALBUM = "Album A"
        const val SONG_1_PATH = "/fake/path/1"
        const val SONG_1_DURATION = 180000L
        const val SONG_1_ID = 1L
        const val SONG_1_DATE_ADDED = 1000000L // Oldest
        
        const val SONG_2_TITLE = "Test Song 2"
        const val SONG_2_ARTIST = "Test Artist 2"
        const val SONG_2_ALBUM = "Album C"
        const val SONG_2_PATH = "/fake/path/2"
        const val SONG_2_DURATION = 240000L
        const val SONG_2_ID = 2L
        const val SONG_2_DATE_ADDED = 3000000L // Newest
        
        const val SONG_3_TITLE = "Another Test Song"
        const val SONG_3_ARTIST = "Test Artist 3"
        const val SONG_3_ALBUM = "Album B"
        const val SONG_3_PATH = "/fake/path/3"
        const val SONG_3_DURATION = 200000L
        const val SONG_3_ID = 3L
        const val SONG_3_DATE_ADDED = 2000000L // Middle
    }
    
    object SortLabels {
        const val TITLE = "Title"
        const val ARTIST = "Artist"
        const val ALBUM = "Album"
        const val RECENTLY_ADDED = "Recently added"
    }
    
    object TestTags {
        private const val SONG_ITEM_PREFIX = "song_item_"
        private const val ARTIST_FILTER_ITEM_PREFIX = "artist_filter_item_"
        
        const val BOTTOM_NAVIGATION_BAR = "BottomNavigationBar"
        
        fun getSongItemTag(songId: Long): String = "$SONG_ITEM_PREFIX$songId"
        fun getArtistFilterItemTag(artist: String): String = "$ARTIST_FILTER_ITEM_PREFIX$artist"
    }
    
    object ContentDescriptions {
        const val SORT_BY = "Sort by"
        const val FILTER_BY_ARTIST = "Filter by artist"
        const val MORE_OPTIONS = "More options"
    }
    
    object FilterOptions {
        const val ALL_ARTISTS = "All Artists"
    }
    
    object Navigation {
        const val MUSIC = "Music"
        const val CREATE = "Create"
        const val LYRICS = "Lyrics"
    }
}