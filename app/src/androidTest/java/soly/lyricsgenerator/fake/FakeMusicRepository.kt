package soly.lyricsgenerator.fake

import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.repository.IMusicRepository
import javax.inject.Inject

class FakeMusicRepository @Inject constructor() : IMusicRepository {

    override suspend fun getAllSongs(): List<Song> {
        return listOf(
            Song(
                id = TestConstants.Songs.SONG_1_ID, 
                title = TestConstants.Songs.SONG_1_TITLE, 
                artist = TestConstants.Songs.SONG_1_ARTIST, 
                data = TestConstants.Songs.SONG_1_PATH, 
                duration = TestConstants.Songs.SONG_1_DURATION, 
                isFavorite = false,
                album = TestConstants.Songs.SONG_1_ALBUM,
                dateAdded = TestConstants.Songs.SONG_1_DATE_ADDED
            ),
            Song(
                id = TestConstants.Songs.SONG_2_ID, 
                title = TestConstants.Songs.SONG_2_TITLE, 
                artist = TestConstants.Songs.SONG_2_ARTIST, 
                data = TestConstants.Songs.SONG_2_PATH, 
                duration = TestConstants.Songs.SONG_2_DURATION, 
                isFavorite = true,
                album = TestConstants.Songs.SONG_2_ALBUM,
                dateAdded = TestConstants.Songs.SONG_2_DATE_ADDED
            ),
            Song(
                id = TestConstants.Songs.SONG_3_ID, 
                title = TestConstants.Songs.SONG_3_TITLE, 
                artist = TestConstants.Songs.SONG_3_ARTIST, 
                data = TestConstants.Songs.SONG_3_PATH, 
                duration = TestConstants.Songs.SONG_3_DURATION, 
                isFavorite = false,
                album = TestConstants.Songs.SONG_3_ALBUM,
                dateAdded = TestConstants.Songs.SONG_3_DATE_ADDED
            )
        )
    }

    override suspend fun getSongsByArtist(artist: String): List<Song> {
        return getAllSongs().filter { it.artist == artist }
    }
}
