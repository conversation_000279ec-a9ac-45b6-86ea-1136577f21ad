package soly.lyricsgenerator.domain.usecase.audiotag

import android.content.Context
import android.media.MediaMetadataRetriever
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.repository.AudioTagRepositoryImpl
import kotlinx.coroutines.runBlocking

/**
 * Instrumented test for AudioTagger functionality.
 * Tests the basic reading capabilities using MediaMetadataRetriever.
 */
@RunWith(AndroidJUnit4::class)
class AudioTaggerTest {

    private lateinit var context: Context
    private lateinit var repository: AudioTagRepositoryImpl
    private lateinit var readAudioTagsUseCase: ReadAudioTagsUseCase

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        repository = AudioTagRepositoryImpl(context)
        readAudioTagsUseCase = ReadAudioTagsUseCase(repository)
    }

    @Test
    fun testSupportedFormats() {
        val supportedFormats = repository.getSupportedFormats()
        
        // Verify all expected formats are supported
        val expectedFormats = listOf("mp3", "flac", "ogg", "oga", "m4a", "mp4", "aac", "wav", "aiff", "aif", "wma")
        assertTrue("Should support all major audio formats", supportedFormats.containsAll(expectedFormats))
    }

    @Test
    fun testSupportsEmbeddedLyrics() {
        // MP3 should support embedded lyrics
        assertTrue("MP3 should support embedded lyrics", 
            repository.supportsEmbeddedLyrics("test.mp3"))
        
        // FLAC should support embedded lyrics
        assertTrue("FLAC should support embedded lyrics", 
            repository.supportsEmbeddedLyrics("test.flac"))
        
        // WAV should not support embedded lyrics (in our current implementation)
        assertFalse("WAV should not support embedded lyrics", 
            repository.supportsEmbeddedLyrics("test.wav"))
    }

    @Test
    fun testInvalidFileHandling() = runBlocking {
        // Test with non-existent file
        val result = readAudioTagsUseCase("non_existent_file.mp3")
        
        assertTrue("Should return error for non-existent file", 
            result is AudioTagResult.Error)
    }

    @Test
    fun testUnsupportedFormatHandling() = runBlocking {
        // Test with unsupported format
        val result = readAudioTagsUseCase("test.unsupported")
        
        assertTrue("Should return error for unsupported format", 
            result is AudioTagResult.Error)
    }
    
    @Test
    fun testWriteTagsNotImplemented() = runBlocking {
        val tags = AudioTag(title = "Test Title", artist = "Test Artist")
        val writeUseCase = WriteAudioTagsUseCase(repository)
        
        val result = writeUseCase("test.mp3", tags)
        
        assertTrue("Write functionality should return error (not implemented)", 
            result is AudioTagResult.Error)
    }
    
    @Test
    fun testEmbedLyricsNotImplemented() = runBlocking {
        val embedUseCase = EmbedLyricsUseCase(repository)
        
        val result = embedUseCase("test.mp3", "[00:10.00]Test lyrics")
        
        assertTrue("Embed functionality should return error (not implemented)", 
            result is AudioTagResult.Error)
    }
}