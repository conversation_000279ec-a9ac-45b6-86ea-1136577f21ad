package soly.lyricsgenerator.features.shared_audio

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasAnyAncestor
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.onRoot
import androidx.compose.ui.test.printToLog
import androidx.compose.ui.semantics.SemanticsProperties
import androidx.test.core.app.ActivityScenario
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.GrantPermissionRule
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import dagger.hilt.android.testing.UninstallModules
import dagger.hilt.components.SingletonComponent
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.data.repository.ContentResolverRepositoryImpl
import soly.lyricsgenerator.data.repository.PreferencesRepositoryImpl
import soly.lyricsgenerator.di.FakeBindingsModule
import soly.lyricsgenerator.domain.repository.ContentResolverRepository
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.repository.MusicRepository
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import soly.lyricsgenerator.ui.constants.UITestTags
import timber.log.Timber
import java.util.concurrent.CountDownLatch
import javax.inject.Singleton

/**
 * Real UI tests for the shared audio feature that show visible activity on emulator.
 * Uses real repositories by uninstalling the fake module and providing a real one.
 */
@UninstallModules(FakeBindingsModule::class)
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SharedAudioIntegrationTest {

    @Module
    @InstallIn(SingletonComponent::class)
    abstract class TestRepositoryModule {

        @Binds
        @Singleton
        abstract fun bindMusicRepository(
            musicRepository: MusicRepository
        ): IMusicRepository

        @Binds
        @Singleton
        abstract fun bindPreferencesRepository(
            preferencesRepositoryImpl: PreferencesRepositoryImpl
        ): PreferencesRepository

        @Binds
        @Singleton
        abstract fun bindContentResolverRepository(
            contentResolverRepositoryImpl: ContentResolverRepositoryImpl
        ): ContentResolverRepository
    }

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val grantPermissionRule: GrantPermissionRule = GrantPermissionRule.grant(
        android.Manifest.permission.POST_NOTIFICATIONS,
        android.Manifest.permission.READ_MEDIA_AUDIO
    )

    @get:Rule(order = 2)
    val composeTestRule = createEmptyComposeRule()

    private lateinit var context: Context

    @Before
    fun setup() {
        Timber.tag("DEBUG_FLOW").d("SharedAudioIntegrationTest: Setting up UI test environment")
        hiltRule.inject()
        context = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun sharedAudioIntent_withFileUri_opensAppAndDisplaysMediaPlayer() {
        // Check all available audio files on emulator/device
        println("DEBUG_FLOW: === CHECKING AVAILABLE AUDIO FILES ON DEVICE ===")

        val projection = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.MIME_TYPE,
            MediaStore.Audio.Media.TITLE,
            MediaStore.Audio.Media.ARTIST
        )

        val availableAudioFiles = mutableListOf<AudioFileInfo>()

        try {
            val cursor = context.contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                projection,
                null,
                null,
                null
            )

            cursor?.use {
                println("DEBUG_FLOW: Found ${it.count} audio files in MediaStore")

                while (it.moveToNext()) {
                    val id = it.getLong(it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID))
                    val displayName = it.getString(it.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME)) ?: "Unknown"
                    val data = it.getString(it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)) ?: "Unknown"
                    val mimeType = it.getString(it.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE)) ?: "Unknown"
                    val title = it.getString(it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)) ?: "Unknown"
                    val artist = it.getString(it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)) ?: "Unknown"

                    val audioFile = AudioFileInfo(id, displayName, data, mimeType, title, artist)
                    availableAudioFiles.add(audioFile)

                    println("DEBUG_FLOW: Audio file #${availableAudioFiles.size}: ID=$id, Title='$title', Artist='$artist', Name='$displayName', Path='$data', Type='$mimeType'")
                }
            }
        } catch (e: Exception) {
            println("DEBUG_FLOW: Error reading MediaStore audio files: ${e.message}")
            e.printStackTrace()
        }

        println("DEBUG_FLOW: === TOTAL: ${availableAudioFiles.size} audio files found ===")

        // If we have audio files, test shared intent with the first one
        if (availableAudioFiles.isNotEmpty()) {
            val testAudioFile = availableAudioFiles.first()
            println("DEBUG_FLOW: Testing shared intent with: ${testAudioFile.title} by ${testAudioFile.artist}")
            
            testSharedAudioIntent(testAudioFile)
        } else {
            println("DEBUG_FLOW: No audio files found for testing shared intent")
        }
    }

    private fun testSharedAudioIntent(audioFile: AudioFileInfo) {
        println("DEBUG_FLOW: Creating shared intent for audio URI: ${audioFile.mediaStoreUri}")
        
        val mainActivityIntent = Intent(context, MainActivity::class.java).apply {
            action = Intent.ACTION_SEND
            type = audioFile.mimeType
            putExtra(Intent.EXTRA_STREAM, audioFile.mediaStoreUri)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        println("DEBUG_FLOW: Starting MainActivity with ActivityScenario")
        val scenario = ActivityScenario.launch<MainActivity>(mainActivityIntent)

        // Wait until the compact music player is visible
        composeTestRule.waitUntil(timeoutMillis = 15_000) {
            composeTestRule.onAllNodes(hasTestTag(UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER))
                .fetchSemanticsNodes().isNotEmpty()
        }

        // Debug: Check if compact player exists at all
        println("DEBUG_FLOW: Expecting title: '${audioFile.title} - ${audioFile.artist}'")
        println("DEBUG_FLOW: Looking for test tag: '${UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER}'")
        val playerNodes = composeTestRule.onAllNodes(hasTestTag(UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER))
            .fetchSemanticsNodes()
        println("DEBUG_FLOW: Found ${playerNodes.size} compact player nodes")
        
        // Debug: Check all nodes in the tree
        val allNodes = composeTestRule.onAllNodes(hasAnyAncestor(hasTestTag(UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER)))
            .fetchSemanticsNodes()
        println("DEBUG_FLOW: Found ${allNodes.size} nodes with compact player as ancestor")
        
        // Print expected and actual text for debugging
        val playerNodeSelector = hasTestTag(UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER)
        composeTestRule.onAllNodes(hasAnyAncestor(playerNodeSelector))
            .fetchSemanticsNodes()
            .forEach { node ->
                println("DEBUG_FLOW: Checking node: ${node.config}")
                if (node.config.contains(SemanticsProperties.Text)) {
                    val textList = node.config[SemanticsProperties.Text]
                    println("DEBUG_FLOW: Node has ${textList.size} text elements")
                    textList.forEach { text ->
                        println("DEBUG_FLOW: Found UI text in player: '${text}'")
                    }
                } else {
                    println("DEBUG_FLOW: Node has no Text property")
                }
            }

        // Debug: Print the semantics tree to understand what's being detected
        println("DEBUG_FLOW: === PRINTING MERGED SEMANTICS TREE ===")
        composeTestRule.onRoot().printToLog("MERGED_TREE")
        
        println("DEBUG_FLOW: === PRINTING UNMERGED SEMANTICS TREE ===")
        composeTestRule.onRoot(useUnmergedTree = true).printToLog("UNMERGED_TREE")
        
        // Try finding with unmerged tree first (most likely solution)
        println("DEBUG_FLOW: Attempting to find song title with unmerged semantics tree...")
        try {
            composeTestRule.onNodeWithTag("compact_player_song_title", useUnmergedTree = true)
                .assertIsDisplayed()
            println("DEBUG_FLOW: SUCCESS - Found song title with unmerged tree!")
        } catch (e: Exception) {
            println("DEBUG_FLOW: Failed with unmerged tree: ${e.message}")
            
            // Fallback: Try finding by text content
            println("DEBUG_FLOW: Fallback - Attempting to find by text content...")
            val expectedText = "${audioFile.title} - ${audioFile.artist}"
            try {
                composeTestRule.onNodeWithText(expectedText, useUnmergedTree = true)
                    .assertIsDisplayed()
                println("DEBUG_FLOW: SUCCESS - Found song title by text content!")
            } catch (e2: Exception) {
                println("DEBUG_FLOW: Failed with text content: ${e2.message}")
                
                // Final fallback: Try partial text match
                println("DEBUG_FLOW: Final fallback - Attempting partial text match...")
                try {
                    composeTestRule.onNodeWithText(audioFile.title, substring = true, useUnmergedTree = true)
                        .assertIsDisplayed()
                    println("DEBUG_FLOW: SUCCESS - Found song title by partial text match!")
                } catch (e3: Exception) {
                    println("DEBUG_FLOW: All methods failed: ${e3.message}")
                    throw AssertionError("Unable to find song title Text component in any way")
                }
            }
        }
        
        println("DEBUG_FLOW: Song title component is displayed with test tag!")

        println("DEBUG_FLOW: Shared audio intent test completed successfully.")
        scenario.close()
    }

    private data class AudioFileInfo(
        val id: Long,
        val displayName: String,
        val data: String,
        val mimeType: String,
        val title: String,
        val artist: String
    ) {
        val mediaStoreUri: Uri
            get() = android.content.ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id)
    }


}