<resources>
    <string name="app_name">Soly</string>
    <string name="music">Music</string>
    <string name="create">Create</string>
    <string name="files">Lyrics</string>
    <string name="search_songs">Search songs...</string>
    <string name="search_files">Search lyrics...</string>
    <string name="back">Back</string>
    <string name="import_text">Import</string>
    <string name="import_lrc_file">Import LRC File</string>
    <string name="choose_file">Choose File</string>
    <string name="selected_file_is_not_an_lrc_file">Selected file must be an LRC, TXT, or RTF file</string>
    <string name="please_select_valid_lyrics_file">Please select a valid lyrics file (.lrc, .txt, or .rtf)</string>
    <string name="error_linking_file">Error linking file: Could not process file content.</string>
    <string name="failed_to_link_missing_file">Failed to link: Missing file or song.</string>
    <string name="file_linked_successfully">%s file linked successfully!</string>
    <string name="delete">Delete</string>
    <string name="no_files">No saved files found</string>
    <string name="delete_confirmation">Are you sure you want to delete this file?</string>
    <string name="yes">Yes</string>
    <string name="cancel">Cancel</string>
    <string name="retry">Retry</string>
    <string name="refresh">Refresh</string>
    <string name="permission_denied">Permission denied. Some features may not work properly.</string>
    <string name="audio_permission_denied">Audio permissions denied. Songs cannot be loaded from device.</string>
    <string name="no_music_found">No music found on your device</string>
    <string name="permission_required">Permission Required</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="permission_rationale">Access to your music files is needed to display and play songs. Please grant permission in the next dialog.</string>
    <string name="open_settings_message">Please enable storage permissions in Settings to access music files.</string>
    <string name="no_search_results">No songs match your search</string>
    <string name="change_song">Change song</string>
    <string name="change_lyric">Change lyric</string>
    <string name="exit">EXIT</string>
    <string name="save">Save</string>
    <string name="ok">OK</string>
    <string name="enter_file_name">Enter File Name</string>
    <string name="file_name">File Name</string>
    <string name="empty_file_warning">Empty File Warning</string>
    <string name="empty_file_message">Cannot save an empty file. Please add content before saving.</string>
    <string name="import_txt_file">Import TXT File</string>
    <string name="selected_file_is_not_a_txt_file">Selected file is not a TXT file</string>
    <string name="save_before_exit">Save Before Exit</string>
    <string name="save_lyric_prompt">Do you want to save this lyric?</string>
    <string name="save_and_exit">Save and exit</string>
    <string name="keep_syncing">Keep syncing</string>

    <!-- Strings for StepsComponent -->
    <string name="steps_title">Steps</string>
    <string name="step_info_choose_song">Choose a song from your phone in .MP3 format</string>
    <string name="step_info_add_lyrics">Add lyrics in .TXT format or paste it</string>
    <string name="step_info_start_creating">Synchronize lyrics by tapping on a line</string>

    <!-- Strings for step text from FormatUtils -->
    <string name="step_text_choose_song">Choose a song</string>
    <string name="step_text_add_lyrics">Add lyrics</string>
    <string name="step_text_start">START</string>

    <!-- New string resources for paste lyrics feature -->
    <string name="lyrics_input_option_dialog_title">Add Lyrics</string>
    <string name="lyrics_input_option_dialog_message">Would you like to import lyrics in .TXT format or paste it?</string>
    <string name="import_txt_option">Import from .TXT</string>
    <string name="paste_lyrics_option">Paste Lyrics</string>
    <string name="paste_lyrics_hint">Paste your lyrics below</string>
    <string name="paste_lyrics_placeholder">Enter lyrics here...</string>
    <string name="notification_channel_name">Music Player Channel</string>
    <string name="notification_default_title">Music Player</string>
    <string name="notification_default_artist">Unknown Artist</string>
    <string name="notification_loading_title">Music Player</string>
    <string name="notification_loading_text">Starting music service...</string>
    <string name="notification_action_play">Play</string>
    <string name="notification_action_pause">Pause</string>
    <string name="notification_action_stop">Stop</string>
    <string name="play">Play</string>
    <string name="pause">Pause</string>
    <string name="previous">Previous</string>
    <string name="next">Next</string>
    <string name="export_video">Export Video</string>

    <!-- Settings Screen -->
    <string name="settings_title">Settings</string>
    <string name="back_button_desc">Navigate back</string>
    <string name="settings_privacy_policy">Privacy Policy</string>
    <string name="settings_app_version">App Version: %1$s (%2$s)</string>
    <string name="import_lrc">Import LRC</string>
    <string name="settings_contact_us">Contact us</string>

    <!-- Music Screen -->
    <string name="menu_description">Open navigation menu</string>

    <!-- Files Screen Item -->
    <string name="more_options">More options</string>
    <string name="export">Export</string>
    <string name="sync">Sync</string>
    <string name="filter_favorites">Show favorites</string>
    <string name="filter_all_songs">Show all songs</string>
    <string name="filter_by_artist">Filter by artist</string>
    <string name="filter_all_artists">All Artists</string>
    <string name="no_favorite_songs_yet">You don\'t have any liked songs. Your liked songs will appear here.</string>
    <string name="no_song_selected">No song selected</string>
    
    <!-- Hardcoded strings being added for internationalization -->
    <string name="create_lrc_file">Create lrc file</string>
    <string name="pick_txt_file">Pick a txt file</string>
    <string name="picked_txt_file">Picked txt file: %1$s</string>
    <string name="no_file_picked">No file picked</string>
    <string name="picked_song">Picked song: %1$s</string>
    <string name="no_song_picked">No song picked</string>
    <string name="pick_song">Pick a song</string>
    <string name="incomplete_steps">Incomplete Steps</string>
    <string name="incomplete_steps_message">Please choose a song and add lyrics to start synchronizing.</string>
    <string name="yes_leave">Yes, leave</string>
    <string name="unsaved_changes">Unsaved Changes</string>
    <string name="unsaved_changes_message">You have unsaved changes. Are you sure you want to leave?</string>
    <string name="undo_sync_confirmation_title">Undo Sync</string>
    <string name="undo_sync_confirmation_message">Are you sure you want to start syncing again? All timestamps will be lost.</string>
    <string name="yes_undo">Yes, Undo</string>
    <string name="song_list">Song List</string>
    <string name="close">Close</string>
    <string name="number_of_songs">Number of songs: %1$d</string>
    <string name="content_desc_replace">Replace</string>
    <string name="content_desc_completed">Completed</string>
    <string name="content_desc_preview">Preview</string>
    <string name="content_desc_add">Add</string>
    <string name="content_desc_remove">Remove</string>
    <string name="content_desc_play_timestamp">Play from this timestamp</string>
    <string name="content_desc_previous">Previous</string>
    <string name="content_desc_play">Play</string>
    <string name="content_desc_pause">Pause</string>
    <string name="content_desc_next">Next</string>
    <string name="content_desc_forward_5_seconds">Forward 5 seconds</string>
    <string name="content_desc_backward_5_seconds">Backward 5 seconds</string>
    <string name="content_desc_shuffle">Shuffle</string>
    <string name="content_desc_close_search">Close search</string>
    <string name="default_filename">lyrics</string>
    <string name="file_save_error">An error occurred while saving the file: %1$s</string>
    <string name="pasted_lyrics_filename">Pasted Lyrics</string>
    <string name="showing_songs_count">Showing %1$d of %2$d songs</string>
    <string name="no_lyrics_available">No lyrics available</string>
    
    <!-- Song Details Screen snackbar messages -->
    <string name="storage_permissions_required">Storage permissions are required for video export</string>
    <string name="no_song_currently_playing">No song is currently playing</string>
    <string name="no_lyrics_available_for_song">No lyrics available for this song</string>

    <!-- Audio Tag Editor -->
    <string name="tag_editor_title">Edit Audio Tags</string>
    <string name="basic_tags">Basic Tags</string>
    <string name="advanced_tags">Advanced Tags</string>
    <string name="lyrics_section">Lyrics</string>
    
    <string name="tag_title">Title</string>
    <string name="tag_artist">Artist</string>
    <string name="tag_album">Album</string>
    <string name="tag_track">Track</string>
    <string name="tag_genre">Genre</string>
    <string name="tag_year">Year</string>
    <string name="tag_album_artist">Album Artist</string>
    <string name="tag_composer">Composer</string>
    <string name="tag_disc_number">Disc Number</string>
    <string name="tag_comment">Comment</string>
    <string name="tag_lyrics">Lyrics</string>
    <string name="tag_isrc">ISRC</string>
    <string name="tag_musicbrainz_id">MusicBrainz ID</string>
    
    <string name="editing_files">Editing %1$d files</string>
    
    <!-- Content Descriptions for Audio Tag Editor -->
    <string name="content_desc_embed_lyrics">Embed lyrics into audio file</string>
    <string name="embed_lyrics_title">Embed Lyrics</string>
    <string name="embed_lyrics_instructions">Paste your LRC file content below to embed lyrics into the audio file.</string>
    <string name="lrc_content_label">LRC Content</string>
    <string name="embed_as_synced_lyrics">Embed as synchronized lyrics</string>
    <string name="synced_lyrics_info">Synchronized lyrics include timing information and will be embedded with timestamp data preserved.</string>
    <string name="plain_lyrics_info">Plain lyrics will have timestamps removed and be embedded as simple text.</string>
    <string name="embed">Embed</string>
    
    <string name="content_desc_export_file">Export file to accessible location</string>
    <string name="content_desc_clear_tags">Clear all tags</string>
    <string name="content_desc_load_tags">Load current tags from file</string>
    <string name="content_desc_next_file">Next file in batch</string>
    <string name="content_desc_previous_file">Previous file in batch</string>

    <!-- Floating Lyrics Overlay -->
    <string name="floating_lyrics">Floating Lyrics</string>
    <string name="overlay_permission_required">Overlay Permission Required</string>
    <string name="overlay_permission_message">To display floating lyrics over other apps, please grant overlay permission.</string>
    <string name="grant_overlay_permission">Grant Permission</string>
    <string name="overlay_permission_denied">Overlay permission denied. Floating lyrics cannot be displayed.</string>
    <string name="enable_floating_lyrics">Enable Floating Lyrics</string>
    <string name="disable_floating_lyrics">Disable Floating Lyrics</string>
    <string name="overlay_settings">Overlay Settings</string>
    <string name="overlay_transparency">Transparency</string>
    <string name="overlay_font_size">Font Size</string>
    <string name="overlay_font_color">Font Color</string>
    <string name="overlay_background_color">Background Color</string>
    <string name="overlay_position">Position</string>
    <string name="close_overlay">Close Overlay</string>
    <string name="return_to_app">Return to App</string>
    <string name="overlay_notification_title">Floating Lyrics Active</string>
    <string name="overlay_notification_text">Tap to return to Soly</string>
    
    <!-- Overlay Position Options -->
    <string name="overlay_position_top">Top</string>
    <string name="overlay_position_center">Center</string>
    <string name="overlay_position_bottom">Bottom</string>
    
    <!-- Overlay Size Options -->
    <string name="overlay_size_small">Small</string>
    <string name="overlay_size_medium">Medium</string>
    <string name="overlay_size_large">Large</string>
    
    <!-- Additional overlay settings strings -->
    <string name="overlay_size">Size</string>
    <string name="overlay_preview">Preview</string>
    <string name="overlay_sample_text">Sample lyrics line</string>
    <string name="overlay_close_button_desc">Close overlay</string>

    <!-- Content descriptions for overlay -->
    <string name="content_desc_close_overlay">Close floating lyrics</string>
    <string name="content_desc_return_to_app">Return to app</string>
    <string name="content_desc_play_pause_overlay">Play or pause music</string>
    <string name="content_desc_previous_song_overlay">Previous song</string>
    <string name="content_desc_next_song_overlay">Next song</string>

    <!-- Song Details Dialog -->
    <string name="details">Details</string>
    <string name="song_details">Song Details</string>
    <string name="file_path">File path</string>
    <string name="details_file_name">File name</string>
    <string name="file_size">Size</string>
    <string name="last_modified">Last Modified</string>
    <string name="format">Format</string>
    <string name="container_format">Container format</string>
    <string name="audio_codec">Audio codec</string>
    <string name="length">Length</string>
    <string name="bitrate">Bitrate</string>
    <string name="sampling_rate">Sampling rate</string>
    <string name="unknown">Unknown</string>
    <string name="content_desc_more_options">More options</string>
    <string name="content_desc_add_to_favorites">Add to favorites</string>
    <string name="content_desc_remove_from_favorites">Remove from favorites</string>
    <string name="content_desc_loading_favorite_state">Loading favorite state</string>

    <!-- Lyrics Management strings -->
    <string name="lyrics_info_title">About Lyrics Management</string>
    <string name="lyrics_linked_files_info">Linked LRC Files</string>
    <string name="lyrics_linked_files_explanation">These are LRC (lyrics) files found in your app that match this song. They contain synchronized or plain text lyrics.</string>
    <string name="lyrics_sync_button_info">Sync Button</string>
    <string name="lyrics_sync_explanation">Pastes lyrics with timestamps (e.g., [00:12.34]) for synchronized playback in compatible music players.</string>
    <string name="lyrics_plain_button_info">Plain Button</string>
    <string name="lyrics_plain_explanation">Pastes lyrics as plain text without timestamps for broader compatibility across all music players.</string>
    <string name="lyrics_automatic_embedding_info">Automatic Embedding</string>
    <string name="lyrics_automatic_embedding_explanation">When you save tags, lyrics will be automatically embedded into the audio file if the format supports it (MP3, FLAC, OGG, M4A, WMA). The app automatically detects if lyrics are synchronized or plain text.</string>
    <string name="lyrics_manual_editing_info">Manual Editing</string>
    <string name="lyrics_manual_editing_explanation">You can type or edit lyrics directly in the text field. Use the same save button as other tags - no separate steps needed.</string>
    <string name="expand_info">Show info</string>
    <string name="collapse_info">Hide info</string>

    <!-- FAQ Section -->
    <string name="settings_faq">FAQ</string>
    <string name="faq_section_title">Frequently Asked Questions</string>
    <string name="faq_expand_content_desc">Expand FAQ answer</string>
    <string name="faq_collapse_content_desc">Collapse FAQ answer</string>

    <!-- FAQ Questions and Answers -->
    <string name="faq_how_to_create_lyrics_question">How do I create synchronized lyrics?</string>
    <string name="faq_how_to_create_lyrics_answer">1. Go to the Create tab\n2. Select a song from your device\n3. Add lyrics by typing or importing a text file\n4. Tap on each line while the music plays to synchronize it\n5. Save your work as an LRC file</string>

    <string name="faq_what_is_lrc_file_question">What is an LRC file?</string>
    <string name="faq_what_is_lrc_file_answer">LRC (Lyric) files are text files that contain song lyrics with precise timestamps. They allow lyrics to be displayed in sync with music playback. The format is widely supported by music players and karaoke software.</string>

    <string name="faq_how_to_import_lyrics_question">How can I import existing lyrics?</string>
    <string name="faq_how_to_import_lyrics_answer">You can import lyrics in two ways:\n• Import LRC files directly from the Lyrics tab\n• Import plain text files during lyrics creation and then add timestamps\n\nUse the import button (📁) to browse and select files from your device.</string>

    <string name="faq_how_to_save_lyrics_question">Where are my lyrics saved?</string>
    <string name="faq_how_to_save_lyrics_answer">Your synchronized lyrics are saved as LRC files in the app\'s internal storage. You can view, manage, and export them from the Lyrics tab. The files are automatically backed up to your device and can be shared with other apps.</string>

    <string name="faq_how_to_share_lyrics_question">How do I share or export my lyrics?</string>
    <string name="faq_how_to_share_lyrics_answer">From the Lyrics tab, tap on any LRC file and select the export option. You can save the file to your device storage or share it directly with other apps via email, messaging, or cloud storage services.</string>

    <string name="faq_troubleshooting_permissions_question">Why can\'t I access my music files?</string>
    <string name="faq_troubleshooting_permissions_answer">The app needs storage permissions to access your music files. Go to your device Settings > Apps > Soly > Permissions and ensure storage access is enabled. Restart the app after granting permissions.</string>

    <!-- Changelog Strings -->
    <string name="settings_changelog">Changelog</string>
    <string name="changelog_title">What\'s New</string>
    <string name="changelog_description">Discover the latest features and improvements</string>
    <string name="changelog_expand">Expand to see details</string>
    <string name="changelog_collapse">Collapse changelog</string>
    <string name="changelog_version_prefix">Version</string>
    <string name="changelog_loading">Loading changelog...</string>
    <string name="changelog_error">Failed to load changelog</string>
    <string name="changelog_retry">Retry</string>
    <string name="changelog_no_entries">No changelog entries available</string>
    
    <!-- Changelog Categories -->
    <string name="changelog_category_new_features">New Features</string>
    <string name="changelog_category_improvements">Improvements</string>
    <string name="changelog_category_bug_fixes">Bug Fixes</string>
    <string name="changelog_category_performance">Performance</string>

    <!-- Changelog Entry Content -->
    <!-- Version 2.0.49 -->
    <string name="changelog_v2049_date">June 2025</string>
    <string name="changelog_v2049_favorites_system">Favorites System - Mark and filter your favorite songs</string>
    <string name="changelog_v2049_compact_player">Compact Music Player - Mini player at bottom of screen</string>
    <string name="changelog_v2049_enhanced_selection">Enhanced Song Selection - Search in song picker</string>
    <string name="changelog_v2049_better_paste">Better Paste Lyrics - Music controls while typing</string>
    <string name="changelog_v2049_crash_fixes">Fixed crashes and performance issues with large music libraries</string>

    <!-- Version 2.0.50 -->
    <string name="changelog_v2050_date">June 2025</string>
    <string name="changelog_v2050_floating_overlay">View lyrics while using other apps with beautiful floating window</string>
    <string name="changelog_v2050_album_art">See album covers in your music list for easier song recognition</string>
    <string name="changelog_v2050_song_details">Song details in three-dot menu shows complete audio info including format, bitrate, and duration</string>
    <string name="changelog_v2050_favorite_menu">Mark favorites faster with one tap from three-dot menu</string>
    <string name="changelog_v2050_tag_editor">Advanced Tag Editor in three-dot menu for editing song metadata</string>
    <string name="changelog_v2050_scrollbar">Navigate long song lists quickly with new scrollbar</string>
    <string name="changelog_v2050_changelog_faq">New FAQ section in Settings for answers and What\'s New section for updates</string>
    <string name="changelog_v2050_embedded_lyrics">Song Details shows embedded lyrics from audio files as backup when no LRC/TXT linked</string>
    <string name="changelog_v2050_linked_lrc">Song Details prioritizes linked LRC files over embedded lyrics for better sync</string>
    <string name="changelog_v2050_txt_support">Song Details now supports viewing linked TXT files with manual scrolling</string>
    <string name="changelog_v2050_quick_sync">Quick Sync from Lyrics screen - tap Sync in TXT file menu to instantly start timing</string>
    <string name="changelog_v2050_compact_player">Control music playback from any screen</string>
    <string name="changelog_v2050_hamburger_menu">Quick access to Settings with hamburger menu in top bar</string>
    <string name="changelog_v2050_pull_refresh">Pull down to refresh your lyrics list instantly</string>
    <string name="changelog_v2050_wake_lock">Screen stays on during lyrics creation and preview</string>
    <string name="changelog_v2050_gradient_theme">Beautiful gradient backgrounds throughout the app</string>
    <string name="changelog_v2050_navigation_improvements">Improved navigation with better back button handling</string>
    <string name="changelog_v2050_toolbar_improvements">Enhanced toolbars with add buttons and better spacing</string>
    <string name="changelog_v2050_favorites_menu">Add to favorites or remove from favorites directly from three-dot menu</string>
    <string name="changelog_v2050_android_12_fix">Music plays reliably on newer Android versions</string>
    <string name="changelog_v2050_metadata_title_improvement">Song titles now display from metadata tags instead of filename</string>
    <string name="changelog_v2050_performance">Faster loading and smoother scrolling with large music collections</string>
    <string name="changelog_v2051_date">June 2025</string>
    <string name="changelog_v2051_gradient_background">Gradient background added to the Song Picker Screen</string>
    <string name="changelog_v2051_rtf_import">Added support for RTF import on the Lyrics Tab</string>
    <string name="changelog_v2051_forced_updates">Implemented forced and soft updates</string>
    <string name="changelog_v2051_sorting_functionality">Added sorting functionality to the Music Screen</string>
    <string name="changelog_v2051_song_loading_performance">Improved song loading performance across all supported Android versions</string>
    <string name="changelog_v2052_date">July 2025</string>
    <string name="changelog_v2052_undo_button">Added Undo Button to Timestamp Editing Screen</string>
    <string name="changelog_v2052_seek_buttons">Added 5-second seek buttons to Paste Lyrics media player</string>
    <string name="changelog_v2052_rtf_sync">Added RTF sync support</string>
    <string name="changelog_v2052_artist_filtering">Add artist filtering</string>
    <string name="changelog_v2052_share_from_apps">Share from other apps to Soly</string>
    <string name="changelog_v2052_open_with">Open With supported by Soly</string>
    <string name="changelog_v2052_three_dots_menu">Added 3 dots menu in the Music and Create Screen</string>
    <string name="changelog_v2052_wav_tags_fix">Fixed issue with reading tags in Music Screen for wav files</string>
    <string name="changelog_v2052_zero_timestamp_scroll_fix">Fixed scrolling to first zero-timestamp lyric when playing from position 0 on Create Screen</string>

    <!-- Audio transcription strings -->
    <string name="uploading_audio_for_transcription">Uploading audio for transcription...</string>
    <string name="transcription_complete">Transcription complete</string>
    <string name="transcription_failed">Transcription failed</string>
    <string name="failed_to_process_audio_file">Failed to process audio file</string>
    <string name="please_select_audio_file">Please select an audio file</string>
    <string name="please_select_txt_or_rtf_file">Please select a TXT or RTF file</string>

    <!-- File type strings -->
    <string name="file_type_lrc">LRC</string>
    <string name="file_type_rtf">RTF</string>
    <string name="file_type_txt">TXT</string>

    <!-- In-App Update strings -->
    <string name="update_required_title">Update Required</string>
    <string name="update_required_message">To continue using the app, you must update to the latest version.</string>
    <string name="update_now">Update Now</string>
    <string name="update_available_title">Update Available</string>
    <string name="update_available_message">A new version of the app is available. Would you like to install it now?</string>
    <string name="update_later">Later</string>
    <string name="error_play_store">Could not open Play Store.</string>

    <!-- Song Sorting strings -->
    <string name="sort_by">Sort by</string>
    <string name="sort_recently_added">Recently added</string>

    <!-- Share Intent strings -->
    <string name="error_loading_shared_audio">Unable to load shared audio file</string>
    <string name="error_invalid_audio_format">Invalid audio format</string>
    <string name="processing_shared_audio">Processing audio file…</string>
    <string name="shared_audio_validation_failed">The shared file is not a valid audio format</string>
    <string name="shared_audio_no_content">No audio content received</string>
    <string name="shared_audio_processing_error">Failed to process shared audio file</string>

</resources>
