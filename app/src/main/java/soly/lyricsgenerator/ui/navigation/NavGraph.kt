package soly.lyricsgenerator.ui.navigation

import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import soly.lyricsgenerator.ui.screens.create_screen.CreateScreen
import soly.lyricsgenerator.ui.screens.create_screen.PasteLyricsScreen
import soly.lyricsgenerator.ui.screens.create_screen.SongPickerScreen
import soly.lyricsgenerator.ui.screens.files_list_screen.FilesScreen
import soly.lyricsgenerator.ui.screens.settings_screen.SettingsScreen
import soly.lyricsgenerator.ui.screens.song_details_screen.SongDetailsScreen
import soly.lyricsgenerator.ui.screens.songs_list_screen.MusicScreen
import soly.lyricsgenerator.ui.screens.tag_editor.TagEditorScreen
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import soly.lyricsgenerator.ui.viewmodel.FilesViewModel
import timber.log.Timber

@Composable
fun NavGraph(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    musicViewModel: MusicViewModel,
    filesViewModel: FilesViewModel
) {
    // Log navigation controller's back stack information
    LaunchedEffect(navController) {
        navController.currentBackStackEntryFlow.collect { entry ->
            Timber.tag("DEBUG_FLOW").d("NavGraph: Current back stack entry changed to route: ${entry.destination.route}")
        }
    }

    // Define animation duration
    val animationDuration = 300

    // Helper function to determine if the transition is between Music and Settings
    fun isMusicSettingsTransition(initial: NavBackStackEntry?, target: NavBackStackEntry?): Boolean {
        val initialRoute = initial?.destination?.route
        val targetRoute = target?.destination?.route
        return (initialRoute == NavRoutes.Music.route && targetRoute == NavRoutes.Settings.route) ||
                (initialRoute == NavRoutes.Settings.route && targetRoute == NavRoutes.Music.route)
    }

    NavHost(
        navController = navController,
        startDestination = NavRoutes.Music.route,
        modifier = modifier
    ) {
        composable(
            route = NavRoutes.Music.route,
            enterTransition = {
                if (isMusicSettingsTransition(initialState, targetState)) {
                    slideInHorizontally(initialOffsetX = { -it }, animationSpec = tween(animationDuration))
                } else {
                    fadeIn(animationSpec = tween(animationDuration))
                }
            },
            exitTransition = {
                if (isMusicSettingsTransition(initialState, targetState)) {
                    slideOutHorizontally(targetOffsetX = { -it }, animationSpec = tween(animationDuration))
                } else {
                    fadeOut(animationSpec = tween(animationDuration))
                }
            },
            popEnterTransition = {
                // Pop enter for Music is usually coming FROM Settings
                if (targetState.destination.route == NavRoutes.Music.route && initialState.destination.route == NavRoutes.Settings.route) {
                     slideInHorizontally(initialOffsetX = { -it }, animationSpec = tween(animationDuration))
                } else {
                    fadeIn(animationSpec = tween(animationDuration))
                }
            },
            popExitTransition = {
                 // Pop exit from Music is usually going TO Settings (handled by Settings popEnter)
                 // Or exiting the app, or going to other screens
                 if (initialState.destination.route == NavRoutes.Music.route && targetState.destination.route == NavRoutes.Settings.route) {
                     slideOutHorizontally(targetOffsetX = { -it }, animationSpec = tween(animationDuration))
                 } else {
                     fadeOut(animationSpec = tween(animationDuration))
                 }
            }
        ) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing Music screen")
            // Pass the hoisted ViewModel instance to MusicScreen
            MusicScreen(navController = navController, viewModel = musicViewModel)
        }
        composable(
            route = NavRoutes.Create.route,
        ) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing Create screen")
            CreateScreen(navController = navController, musicViewModel = musicViewModel)
        }
        composable(
            route = NavRoutes.CreateWithSync.route,
            arguments = listOf(
                navArgument("songId") {
                    type = NavType.LongType
                },
                navArgument("fileId") {
                    type = NavType.LongType
                }
            )
        ) { backStackEntry ->
            val songId = backStackEntry.arguments?.getLong("songId") ?: 0L
            val fileId = backStackEntry.arguments?.getLong("fileId") ?: 0L
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing Create screen with sync, songId=$songId, fileId=$fileId")
            CreateScreen(
                navController = navController, 
                musicViewModel = musicViewModel,
                syncSongId = songId,
                syncFileId = fileId
            )
        }
        composable(
            route = NavRoutes.Files.route,
        ) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing Files screen")
            // Pass the hoisted ViewModel instance to FilesScreen
            FilesScreen(navController = navController, viewModel = filesViewModel, musicViewModel = musicViewModel)
        }
        composable(route = NavRoutes.SongDetails.route) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing SongDetails screen")
            SongDetailsScreen(navController)
        }
        composable(
            route = "${NavRoutes.SongPickerScreen.route}?isChangingSong={isChangingSong}&linkingMode={linkingMode}",
            arguments = listOf(
                navArgument("isChangingSong") {
                    type = NavType.BoolType
                    defaultValue = false
                },
                navArgument("linkingMode") {
                    type = NavType.BoolType
                    defaultValue = false
                }
            )
        ) { backStackEntry ->
            val isChangingSong = backStackEntry.arguments?.getBoolean("isChangingSong") ?: false
            val linkingMode = backStackEntry.arguments?.getBoolean("linkingMode") ?: false
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing SongPicker screen, isChangingSong=$isChangingSong, linkingMode=$linkingMode")
            SongPickerScreen(
                navController = navController,
                isChangingSong = isChangingSong,
                linkingMode = linkingMode
            )
        }
        composable(NavRoutes.PasteLyricsScreen.route) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing PasteLyrics screen")
            PasteLyricsScreen(navController = navController)
        }
        composable(
            route = NavRoutes.Settings.route,
            enterTransition = {
                // Enter Settings: Slide if coming FROM Music
                if (initialState.destination.route == NavRoutes.Music.route) {
                    slideInHorizontally(initialOffsetX = { it }, animationSpec = tween(animationDuration))
                } else {
                    fadeIn(animationSpec = tween(animationDuration))
                }
            },
            exitTransition = {
                // Exit Settings: Slide if going TO Music (handled by Music enter)
                // Fade otherwise
                 if (targetState.destination.route == NavRoutes.Music.route) {
                     slideOutHorizontally(targetOffsetX = { -it }, animationSpec = tween(animationDuration)) // Corrected direction for Settings exiting towards Music
                 } else {
                     fadeOut(animationSpec = tween(animationDuration))
                 }
            },
            popEnterTransition = {
                // Pop Enter Settings: Should not happen often unless deep-linking or complex stack manipulation
                // Defaulting to fade, slide handled by Music popExit
                 fadeIn(animationSpec = tween(animationDuration))
            },
            popExitTransition = {
                 // Pop Exit Settings: Slide if going back TO Music
                 if (targetState.destination.route == NavRoutes.Music.route) {
                     slideOutHorizontally(targetOffsetX = { it }, animationSpec = tween(animationDuration))
                 } else {
                     fadeOut(animationSpec = tween(animationDuration))
                 }
            }
        ) {
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing Settings screen")
            SettingsScreen(navController = navController)
        }
        
        composable(
            route = NavRoutes.TagEditor.route,
            arguments = listOf(
                navArgument("filePath") {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val filePath = backStackEntry.arguments?.getString("filePath") ?: ""
            // Get song data from the previous backStackEntry (the screen that navigated to TagEditor)
            val songData = navController.previousBackStackEntry?.savedStateHandle?.get<soly.lyricsgenerator.domain.model.Song>(NavigationConstants.KEY_SONG_DATA)
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing TagEditor screen for file: $filePath, song: ${songData?.title}")
            TagEditorScreen(
                filePath = filePath,
                songData = songData
            )
        }
        
        composable(
            route = NavRoutes.TagEditorBatch.route
        ) { backStackEntry ->
            // Get file paths from savedStateHandle or arguments
            val filePaths = navController.currentBackStackEntry?.savedStateHandle?.get<List<String>>(NavigationConstants.KEY_FILE_PATHS) ?: emptyList()
            Timber.tag("DEBUG_FLOW").d("NavGraph: Composing TagEditor screen for batch editing ${filePaths.size} files")
            TagEditorScreen(
                filePath = filePaths.firstOrNull() ?: "",
                batchMode = true,
                filePaths = filePaths
            )
        }
    }
}
