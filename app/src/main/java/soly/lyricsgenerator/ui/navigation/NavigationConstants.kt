package soly.lyricsgenerator.ui.navigation

import android.net.Uri
import androidx.navigation.NavController
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import soly.lyricsgenerator.domain.model.Song

/**
 * Constants for navigation-related functionality including savedStateHandle keys
 */
object NavigationConstants {
    
    /**
     * SavedStateHandle key for passing song ID between screens
     */
    const val KEY_SONG_ID = "song_id"
    
    /**
     * SavedStateHandle key for indicating navigation from compact player
     */
    const val KEY_FROM_COMPACT_PLAYER = "from_compact_player"
    
    /**
     * SavedStateHandle key for file paths in batch editing
     */
    const val KEY_FILE_PATHS = "file_paths"
    
    /**
     * SavedStateHandle key for song data when editing tags
     */
    const val KEY_SONG_DATA = "song_data"
}

/**
 * Extension functions for navigation to TagEditor screens
 */
fun NavController.navigateToTagEditor(filePath: String) {
    val encodedPath = URLEncoder.encode(filePath, StandardCharsets.UTF_8.toString())
    navigate("tag_editor/$encodedPath")
}

fun NavController.navigateToTagEditor(song: Song) {
    val encodedPath = URLEncoder.encode(song.data, StandardCharsets.UTF_8.toString())
    currentBackStackEntry?.savedStateHandle?.set(NavigationConstants.KEY_SONG_DATA, song)
    navigate("tag_editor/$encodedPath")
}

fun NavController.navigateToTagEditorBatch(filePaths: List<String>) {
    currentBackStackEntry?.savedStateHandle?.set(NavigationConstants.KEY_FILE_PATHS, filePaths)
    navigate(NavRoutes.TagEditorBatch.route)
}