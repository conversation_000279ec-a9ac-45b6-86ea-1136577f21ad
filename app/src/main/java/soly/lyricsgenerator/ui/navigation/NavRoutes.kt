package soly.lyricsgenerator.ui.navigation

sealed class NavRoutes(val route: String) {
    object Music : NavRoutes("music")
    object Create : NavRoutes("create")
    object CreateWithSync : NavRoutes("create/{songId}/{fileId}")
    object Files : NavRoutes("files")
    object SongDetails : NavRoutes("song_details")
    object SongPickerScreen : NavRoutes("song_picker")
    object PasteLyricsScreen : NavRoutes("paste_lyrics")
    object Settings : NavRoutes("settings")
    object TagEditor : NavRoutes("tag_editor/{filePath}")
    object TagEditorBatch : NavRoutes("tag_editor_batch")
}
