package soly.lyricsgenerator.ui.screens.components

import androidx.activity.result.ActivityResultLauncher
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.constants.FileConstants
import timber.log.Timber

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BottomSheetFileChooser(
    bottomSheetState: SheetState,
    coroutineScope: CoroutineScope,
    filePickerLauncher: ActivityResultLauncher<Array<String>>
) {
    ModalBottomSheet(
        sheetState = bottomSheetState,
        onDismissRequest = {
            Timber.tag("DEBUG_FLOW").d("BottomSheetFileChooser: Modal dismissed by user")
            coroutineScope.launch {
                bottomSheetState.hide()
            }
        }
    ) {
        Timber.tag("DEBUG_FLOW").d("BottomSheetFileChooser: Modal bottom sheet content rendered")
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.import_lrc_file),
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Button(
                onClick = {
                    Timber.tag("DEBUG_FLOW").d("BottomSheetFileChooser: Choose file button clicked")
                    Timber.tag("DEBUG_FLOW").d("BottomSheetFileChooser: Launching file picker with mime types: ${FileConstants.MimeTypes.SUPPORTED_LYRICS_MIME_TYPES.joinToString()}")
                    filePickerLauncher.launch(FileConstants.MimeTypes.SUPPORTED_LYRICS_MIME_TYPES)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(stringResource(R.string.choose_file))
            }
        }
    }
}