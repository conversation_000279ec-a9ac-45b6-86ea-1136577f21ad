package soly.lyricsgenerator.ui.screens.create_screen

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.fontscaling.MathUtils.lerp

@SuppressLint("RestrictedApi")
@Composable
fun StepLines(
    stepNumber: Int,
    center: Offset,
    isCompleted: Boolean,
    upperLineProgress: Animatable<Float, AnimationVector1D>,
    lowerLineProgress: Animatable<Float, AnimationVector1D>,
) {
    val density = LocalDensity.current
    Canvas(modifier = Modifier.fillMaxSize()) {
        val circleRadius = with(density) { 12.dp.toPx() }
        val bottomLineStart = center.copy(y = circleRadius)
        val bottomLineEnd = if (stepNumber == 3) center.copy(y = circleRadius) else Offset(
            center.x,
            size.height
        )

        drawUpperLine(
            stepNumber,
            center,
            circleRadius,
            upperLineProgress
        )

        drawLowerLine(
            stepNumber,
            bottomLineStart,
            bottomLineEnd,
            isCompleted,
            lowerLineProgress,
        )
    }
}

@SuppressLint("RestrictedApi")
private fun DrawScope.drawUpperLine(
    stepNumber: Int,
    center: Offset,
    circleRadius: Float,
    lineProgress: Animatable<Float, AnimationVector1D>
) {
    if (stepNumber == 1) return
    drawVerticalStepLines(
        center,
        circleRadius,
        lineProgress
    )
}

@SuppressLint("RestrictedApi")
private fun DrawScope.drawVerticalStepLines(
    center: Offset,
    circleRadius: Float,
    lineProgress: Animatable<Float, AnimationVector1D>
) {
    // Siva linija
    drawLine(
        color = Color.Gray,
        start = Offset(center.x, -circleRadius),
        end = Offset(center.x, circleRadius / 2),
        strokeWidth = 3.dp.toPx()
    )

    drawLine(
        color = Color.Green,
        start = Offset(center.x, -circleRadius),
        end = Offset(
            center.x,
            lerp(-circleRadius, circleRadius / 2, lineProgress.value)
        ),
        strokeWidth = 3.dp.toPx()
    )
}

@SuppressLint("RestrictedApi")
private fun DrawScope.drawLowerLine(
    stepNumber: Int,
    bottomLineStart: Offset,
    bottomLineEnd: Offset,
    isCompleted: Boolean,
    lineProgress: Animatable<Float, AnimationVector1D>,
) {
    drawStepLines(
        bottomLineStart,
        bottomLineEnd,
        isCompleted,
        lineProgress,
        stepNumber
    )
}

@SuppressLint("RestrictedApi")
private fun DrawScope.drawStepLines(
    bottomLineStart: Offset,
    bottomLineEnd: Offset,
    isCompleted: Boolean,
    lineProgress: Animatable<Float, AnimationVector1D>,
    stepNumber: Int
) {
    // Siva linija
    drawLine(
        color = Color.Gray,
        start = bottomLineStart,
        end = bottomLineEnd,
        strokeWidth = 3.dp.toPx()
    )
    Log.d(
        "StepIndicator",
        "Drawing green line for step $stepNumber with progress ${lineProgress.value} isCompleted: $isCompleted"
    )
    val animatedEndY = lerp(bottomLineStart.y, bottomLineEnd.y, lineProgress.value)
    drawLine(
        color = Color.Green,
        start = bottomLineStart,
        end = Offset(
            bottomLineEnd.x,
            animatedEndY
        ),
        strokeWidth = 3.dp.toPx()
    )
} 