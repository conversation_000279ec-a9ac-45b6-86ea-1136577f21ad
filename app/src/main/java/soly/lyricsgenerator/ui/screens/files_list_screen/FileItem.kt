package soly.lyricsgenerator.ui.screens.files_list_screen

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.FileUpload
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material.icons.outlined.Edit
import androidx.compose.material.icons.outlined.FileUpload
import androidx.compose.material.icons.outlined.Sync
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.FileWithSong
import soly.lyricsgenerator.ui.screens.create_screen.utils.formatTitleAndArtist
import soly.lyricsgenerator.ui.theme.AccentColor

@Composable
fun FileItem(
    fileWithSong: FileWithSong,
    onItemClick: (FileWithSong) -> Unit,
    onDeleteClick: (Long) -> Unit,
    onSaveClick: (FileWithSong) -> Unit,
    onSyncClick: (FileWithSong) -> Unit = {},
    onTagEditorClick: (FileWithSong) -> Unit = {},
    modifier: Modifier = Modifier
) {
    var showDropdownMenu by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onItemClick(fileWithSong) }
            .background(Color.Transparent)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // File type icon in rounded container
        FileTypeIcon(
            fileType = fileWithSong.file.fileType
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // File and song information
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = fileWithSong.file.fileName,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = Color.White
            )
            
            if (fileWithSong.song.title.isNotBlank()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = formatTitleAndArtist(fileWithSong.song.title, fileWithSong.song.artist, separator = " - "),
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = Color(0xFFB0B0B0) // Light gray for song info
                )
            }
        }
        
        // Three dots menu
        Box {
            IconButton(
                onClick = { showDropdownMenu = true }
            ) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = stringResource(R.string.content_desc_more_options),
                    tint = Color.White
                )
            }
            
            DropdownMenu(
                expanded = showDropdownMenu,
                onDismissRequest = { showDropdownMenu = false }
            ) {
                // Show Sync option for TXT and RTF files
                if (fileWithSong.file.fileType in setOf(FileType.TXT, FileType.RTF)) {
                    DropdownMenuItem(
                        text = { Text(stringResource(R.string.sync)) },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Outlined.Sync,
                                contentDescription = null,
                                tint = AccentColor
                            )
                        },
                        onClick = {
                            showDropdownMenu = false
                            onSyncClick(fileWithSong)
                        }
                    )
                }
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.tag_editor_title)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Edit,
                            contentDescription = null,
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        onTagEditorClick(fileWithSong)
                    }
                )
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.export)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.FileUpload,
                            contentDescription = null,
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        onSaveClick(fileWithSong)
                    }
                )
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.delete)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Delete,
                            contentDescription = null,
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        onDeleteClick(fileWithSong.file.id)
                    }
                )
            }
        }
    }
}

@Composable
fun FileTypeIcon(
    fileType: FileType,
    modifier: Modifier = Modifier
) {
    val icon = fileType.getIcon()
    
    Box(
        modifier = modifier
            .size(48.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(
                color = Color.Gray.copy(alpha = 0.3f)
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(28.dp),
            tint = Color.White
        )
    }
}
