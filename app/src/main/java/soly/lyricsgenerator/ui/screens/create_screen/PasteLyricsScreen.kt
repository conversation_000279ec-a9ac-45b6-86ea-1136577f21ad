package soly.lyricsgenerator.ui.screens.create_screen

import Tuple
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.screens.components.PasteLyricsMediaController
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import timber.log.Timber
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import android.os.Bundle
import soly.lyricsgenerator.domain.constants.PlaybackConstants
import soly.lyricsgenerator.ui.theme.Purple80

@Composable
fun PasteLyricsScreen(navController: NavHostController) {
    val activity = LocalContext.current as ComponentActivity
    val songViewModel: CreateViewModel = hiltViewModel(viewModelStoreOwner = activity)
    val sharedViewModel: SharedViewModel = hiltViewModel(viewModelStoreOwner = activity)
    var lyricsText by remember { mutableStateOf("") }
    val showConfirmationDialog = remember { mutableStateOf(false) }
    
    // Create a unified back navigation handler
    val handleBackNavigation = {
        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: handleBackNavigation called - lyricsText.isNotBlank()=${lyricsText.isNotBlank()}")
        
        if (lyricsText.isNotBlank()) {
            // Log back button pressed analytics
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_BACK_BUTTON_PRESSED) {
                param(AnalyticsConstants.Params.HAS_UNSAVED_CHANGES, lyricsText.isNotBlank().toString())
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
            }
            
            Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Setting showConfirmationDialog to true")
            showConfirmationDialog.value = true
        } else {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_BACK_BUTTON_PRESSED) {
                param(AnalyticsConstants.Params.HAS_NO_UNSAVED_CHANGES, "")
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
            }

            Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: No unsaved changes, navigating up directly")
            navController.navigateUp()
        }
    }
    
    // Expose the navigation handler to SharedViewModel for MainActivity to use
    LaunchedEffect(lyricsText) {
        sharedViewModel.setPasteLyricsBackHandler { handleBackNavigation() }
    }
    
    // Get post-start screen state from SharedViewModel
    val isPostStartScreen by sharedViewModel.isPostStartScreen
    
    // Collect music player state from CreateViewModel
    val currentSong by songViewModel.currentSongFlow.collectAsState()
    val currentPosition by songViewModel.currentPositionFlow.collectAsState()
    val isPlaying by songViewModel.isPlaying.collectAsState()
    
    // Keyboard visibility detection
    val density = LocalDensity.current
    val imeInsets = WindowInsets.ime
    val isKeyboardVisible by remember {
        derivedStateOf {
            imeInsets.getBottom(density) > 0
        }
    }
    
    // Update SharedViewModel with keyboard visibility state
    LaunchedEffect(isKeyboardVisible) {
        sharedViewModel.setKeyboardVisible(isKeyboardVisible)
        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Keyboard visibility changed to: $isKeyboardVisible")
        
        // Log keyboard visibility analytics
        if (isKeyboardVisible) {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_KEYBOARD_SHOWN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
            }
        } else {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_KEYBOARD_HIDDEN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
            }
        }
    }

    // BackHandler to handle back button press with confirmation dialog
    BackHandler(enabled = lyricsText.isNotBlank()) {
        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: BackHandler callback triggered - using unified handler")
        handleBackNavigation()
    }
    
    

    // Log screen view
    LaunchedEffect(Unit) {
        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: LaunchedEffect triggered for screen open log")
        // Log screen opened analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_SCREEN_OPENED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
            param(AnalyticsConstants.Params.SOURCE_SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
        }
        
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.PASTE_LYRICS)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
        }
    }

    // Register broadcast receiver for music updates (similar to CreateScreen)
    DisposableEffect(Unit) {
        songViewModel.registerReceiver()
        onDispose {
            songViewModel.unregisterReceiver()
            sharedViewModel.clearPasteLyricsBackHandler()
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .imePadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Media controller positioned at the top of the Column, above the text field
            if (currentSong != null) {
                PasteLyricsMediaController(
                    currentSong = currentSong,
                    currentPosition = currentPosition,
                    isPlaying = isPlaying,
                    onSeekTo = { position ->
                        // Log seek analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SEEK_SONG) {
                            param(AnalyticsConstants.Params.SEEK_POSITION_MS, position)
                            param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                        }
                        songViewModel.seekTo(activity, position)
                    },
                    onPlayPauseClicked = {
                        if (isPlaying) {
                            // Log pause analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PAUSE_SONG) {
                                param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "unknown")
                            }
                            songViewModel.pauseSong(activity)
                        } else {
                            // Log resume analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_RESUME_SONG) {
                                param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "unknown")
                            }
                            songViewModel.resumeSong(activity)
                        }
                    },
                    onSeekForward5SecondsClicked = { songViewModel.seekForward5Seconds() },
                    onSeekBackward5SecondsClicked = { songViewModel.seekBackward5Seconds() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            TextField(
                value = lyricsText,
                onValueChange = { newText ->
                    // Log text entered analytics
                    if (newText.isNotEmpty() && lyricsText.isEmpty()) {
                        // First time entering text
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_TEXT_ENTERED) {
                            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                            param(AnalyticsConstants.Params.INPUT_LENGTH, newText.length)
                        }
                    } else if (newText.isEmpty() && lyricsText.isNotEmpty()) {
                        // Text cleared
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_TEXT_CLEARED) {
                            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                            param(AnalyticsConstants.Params.PREVIOUS_LENGTH, lyricsText.length)
                        }
                    }
                    lyricsText = newText
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                placeholder = { Text(stringResource(R.string.paste_lyrics_placeholder)) }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = {
                    // Log save clicked analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_SAVE_CLICKED) {
                        param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                        param(AnalyticsConstants.Params.TEXT_LENGTH, lyricsText.length)
                        param(AnalyticsConstants.Params.HAS_CONTENT, lyricsText.isNotBlank().toString())
                    }
                    
                    // Process the lyrics text, split by newlines
                    val lines = lyricsText.split("\n")
                    
                    // Convert to the format needed by the CreateViewModel
                    val lyricsMap = lines.withIndex()
                        .associate { (index, line) -> 
                            index to Tuple(0L, line.trim()) 
                        }
                    
                    // Log existing values
                    Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Before update - lrcKeyValuePairs has ${songViewModel.lrcKeyValuePairs.value.size} items")
                    
                    try {
                        // Log the save event with success
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_SAVE_SUCCESS) {
                            param(AnalyticsConstants.Params.LINE_COUNT, lines.size.toLong())
                            param(AnalyticsConstants.Params.CHARACTER_COUNT, lyricsText.length.toLong())
                            param(AnalyticsConstants.Params.LYRICS_SOURCE, "paste_text")
                        }
                        
                        // Update the viewModel with pasted lyrics
                        songViewModel.lrcKeyValuePairs.value = lyricsMap
                        
                        // Log after update
                        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: After update - lrcKeyValuePairs has ${songViewModel.lrcKeyValuePairs.value.size} items")
                        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: First few items: ${lyricsMap.entries.take(3)}")
                        
                        // Set a placeholder for the file name
                        songViewModel.pickedTextFileName.value = "Pasted Lyrics"
                        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Set pickedTextFileName to: ${songViewModel.pickedTextFileName.value}")
                        
                        Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Processed ${lines.size} lines of pasted lyrics")
                        
                        // Ensure post-start screen state is maintained
                        if (isPostStartScreen) {
                            Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Maintaining post-start screen state (steps hidden)")
                            songViewModel.hideStepsIndicator()
                        }
                        
                        // Navigate back
                        navController.navigateUp()
                        
                    } catch (e: Exception) {
                        // Log save failed analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_SAVE_FAILED) {
                            param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: "Unknown error")
                            param(AnalyticsConstants.Params.ERROR_CATEGORY, "save_processing_error")
                            param(AnalyticsConstants.Params.TEXT_LENGTH, lyricsText.length)
                        }
                        Timber.tag("DEBUG_FLOW").e(e, "PasteLyricsScreen: Failed to save pasted lyrics")
                    }
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Purple80,
                    contentColor = Color.Black // Black text for good contrast with Purple80
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                enabled = lyricsText.isNotBlank()
            ) {
                Text(text = stringResource(R.string.save))
            }
        }
    }
    
    // Confirmation dialog for back navigation when there are unsaved changes
    if (showConfirmationDialog.value) {
        // Log confirmation dialog shown analytics
        LaunchedEffect(Unit) {
            Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Confirmation dialog shown")
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_CONFIRMATION_DIALOG_SHOWN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.PASTE_LYRICS_SCREEN)
                param(AnalyticsConstants.Params.DIALOG_TYPE, AnalyticsConstants.Values.DIALOG_TYPE_UNSAVED_CHANGES)
            }
        }
        
        AlertDialog(
            onDismissRequest = { 
                Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Confirmation dialog dismissed by outside click")
                // Log dialog dismissed analytics
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_CONFIRMATION_DIALOG_CANCELLED) {
                    param(AnalyticsConstants.Params.DISMISS_METHOD, AnalyticsConstants.Values.DISMISS_METHOD_OUTSIDE_CLICK)
                }
                showConfirmationDialog.value = false 
            },
            confirmButton = {
                TextButton(onClick = {
                    Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Confirmation dialog confirmed - navigating up")
                    // Log dialog confirmed analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_CONFIRMATION_DIALOG_CONFIRMED) {
                        param(AnalyticsConstants.Params.BUTTON_TYPE, AnalyticsConstants.Values.BUTTON_TYPE_CONFIRM)
                        param(AnalyticsConstants.Params.ACTION, AnalyticsConstants.Values.ACTION_NAVIGATE_UP)
                    }
                    showConfirmationDialog.value = false
                    navController.navigateUp()
                }) {
                    Text(stringResource(id = R.string.yes_leave))
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    Timber.tag("DEBUG_FLOW").d("PasteLyricsScreen: Confirmation dialog cancelled by cancel button")
                    // Log dialog cancelled analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PASTE_LYRICS_CONFIRMATION_DIALOG_CANCELLED) {
                        param(AnalyticsConstants.Params.BUTTON_TYPE, AnalyticsConstants.Values.BUTTON_TYPE_CANCEL)
                        param(AnalyticsConstants.Params.DISMISS_METHOD, AnalyticsConstants.Values.DISMISS_METHOD_BUTTON_CLICK)
                    }
                    showConfirmationDialog.value = false 
                }) {
                    Text(stringResource(id = R.string.cancel))
                }
            },
            title = {
                Text(stringResource(id = R.string.unsaved_changes))
            },
            text = {
                Text(stringResource(id = R.string.unsaved_changes_message))
            }
        )
    }
}
