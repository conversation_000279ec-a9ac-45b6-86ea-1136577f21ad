package soly.lyricsgenerator.ui.screens.tag_editor

import android.app.Application
import android.content.Context
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.model.WriteInfo
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.usecase.audiotag.EmbedLyricsUseCase
import soly.lyricsgenerator.domain.usecase.audiotag.GetLinkedLrcFilesUseCase
import soly.lyricsgenerator.domain.usecase.audiotag.ReadAudioTagsUseCase
import soly.lyricsgenerator.domain.usecase.audiotag.WriteAudioTagsUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.GetFileContentForSongUseCase
import soly.lyricsgenerator.utils.toFieldKeyMap
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * ViewModel for the Tag Editor screen.
 * Follows Clean Architecture and MVVM patterns with sealed classes and polymorphic dispatch.
 * Uses Hilt for dependency injection.
 */
@HiltViewModel
class TagEditorViewModel @Inject constructor(
    private val context: Application,
    private val readAudioTagsUseCase: ReadAudioTagsUseCase,
    private val embedLyricsUseCase: EmbedLyricsUseCase,
    private val getLinkedLrcFilesUseCase: GetLinkedLrcFilesUseCase,
) : ViewModel() {
    
    init {
        Timber.tag(TAG).d("TagEditorViewModel created: ${this.hashCode()}")
    }

    companion object {
        private const val TAG = "DEBUG_FLOW_TagEditorViewModel"
    }

    // UI State
    private val _uiState = MutableStateFlow<TagEditorUiState>(TagEditorUiState.Loading)
    val uiState: StateFlow<TagEditorUiState> = _uiState.asStateFlow()

    // Batch editing state
    private val _batchState = MutableStateFlow<BatchEditingState>(BatchEditingState.NoBatch)
    val batchState: StateFlow<BatchEditingState> = _batchState.asStateFlow()

    // Snackbar messages
    private val _snackbarMessage = MutableStateFlow<SnackbarMessage?>(null)
    val snackbarMessage: StateFlow<SnackbarMessage?> = _snackbarMessage.asStateFlow()

    /**
     * Initializes the editor with a single file.
     */
    fun initializeSingleFile(filePath: String) {
        Timber.tag(TAG).d("Initializing single file: $filePath")
        
        _batchState.value = BatchEditingState.NoBatch
        loadAudioTags(filePath)
    }
    
    /**
     * Initializes the editor with a single file and pre-populates with song data from MediaStore.
     */
    fun initializeSingleFileWithSong(filePath: String, songData: Song) {
        Timber.tag(TAG).d("Initializing single file with song data: $filePath, song: ${songData.title}")
        
        _batchState.value = BatchEditingState.NoBatch
        loadAudioTagsWithPrePopulation(filePath, songData)
    }

    /**
     * Initializes the editor with multiple files for batch editing.
     */
    fun initializeBatchFiles(filePaths: List<String>) {
        Timber.tag(TAG).d("Initializing batch editing with ${filePaths.size} files")
        
        if (filePaths.isEmpty()) {
            _uiState.value = TagEditorUiState.Error("No files provided for batch editing")
            return
        }

        // Start with the first file
        _batchState.value = BatchEditingState.BatchMode(
            filePaths = filePaths,
            currentIndex = 0,
            commonTags = AudioTag(),
            individualTags = emptyMap()
        )
        
        loadAudioTags(filePaths.first())
    }

    /**
     * Loads audio tags from the specified file.
     */
    private fun loadAudioTags(filePath: String) {
        _uiState.value = TagEditorUiState.Loading
        
        viewModelScope.launch {
            try {
                val result = readAudioTagsUseCase(filePath)
                handleLoadResult(result, filePath)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Exception loading audio tags")
                _uiState.value = TagEditorUiState.Error("Failed to load tags: ${e.message}")
            }
        }
    }
    
    /**
     * Loads audio tags from the specified file with pre-population from song data.
     */
    private fun loadAudioTagsWithPrePopulation(filePath: String, songData: Song) {
        _uiState.value = TagEditorUiState.Loading
        
        viewModelScope.launch {
            try {
                val result = readAudioTagsUseCase(filePath)
                handleLoadResultWithPrePopulation(result, filePath, songData)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Exception loading audio tags")
                _uiState.value = TagEditorUiState.Error("Failed to load tags: ${e.message}")
            }
        }
    }

    /**
     * Handles the result of loading audio tags.
     * Uses polymorphic dispatch with sealed classes.
     */
    private fun handleLoadResult(result: AudioTagResult<AudioTag>, filePath: String) {
        try {
            val tags = result.process()
            val fileName = File(filePath).name
            val supportedFormats = listOf("mp3", "flac", "ogg", "m4a", "wav", "aiff", "wma")
            val supportsEmbeddedLyrics = embedLyricsUseCase.supportsEmbeddedLyrics(filePath)
            
            Timber.tag(TAG).d("Setting state to Loaded for: $fileName")
            Timber.tag(TAG).d("Loaded lyrics: ${if (tags.lyrics?.isNotBlank() == true) "present (${tags.lyrics!!.length} chars)" else "empty/null"}")
            _uiState.value = TagEditorUiState.Loaded(
                currentTags = tags,
                originalTags = tags,
                filePath = filePath,
                fileName = fileName,
                supportedFormats = supportedFormats,
                supportsEmbeddedLyrics = supportsEmbeddedLyrics,
                linkedLrcFiles = emptyList(), // Will be populated when song data is available
                songId = null
            )
            
            Timber.tag(TAG).d("State set to: ${_uiState.value::class.simpleName}")
            Timber.tag(TAG).d("Successfully loaded tags for: $fileName")
        } catch (e: Exception) {
            val errorMessage = result.getResultMessage()
            _uiState.value = TagEditorUiState.Error(errorMessage)
            Timber.tag(TAG).e("Failed to load tags: $errorMessage")
        }
    }
    
    /**
     * Handles the result of loading audio tags with MediaStore data pre-population.
     * Prioritizes MediaStore data for title and artist when file tags are empty.
     */
    private fun handleLoadResultWithPrePopulation(result: AudioTagResult<AudioTag>, filePath: String, songData: Song) {
        viewModelScope.launch {
            try {
                val fileTags = result.process()
                val fileName = File(filePath).name
                val supportedFormats = listOf("mp3", "flac", "ogg", "m4a", "wav", "aiff", "wma")
                val supportsEmbeddedLyrics = embedLyricsUseCase.supportsEmbeddedLyrics(filePath)
                
                // Load linked LRC files for this song
                val linkedLrcFiles = try {
                    getLinkedLrcFilesUseCase(songData)
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to load linked LRC files for song: ${songData.title}")
                    emptyList()
                }
                
                // Pre-populate with MediaStore data when file tags are empty or null
                val prePopulatedTags = fileTags.copy(
                    title = if (fileTags.title.isNullOrBlank()) songData.title else fileTags.title,
                    artist = if (fileTags.artist.isNullOrBlank()) songData.artist else fileTags.artist
                )
                
                Timber.tag(TAG).d("Setting state to Loaded with pre-population for: $fileName")
                Timber.tag(TAG).d("Original file title: '${fileTags.title}', MediaStore title: '${songData.title}', Final title: '${prePopulatedTags.title}'")
                Timber.tag(TAG).d("Original file artist: '${fileTags.artist}', MediaStore artist: '${songData.artist}', Final artist: '${prePopulatedTags.artist}'")
                Timber.tag(TAG).d("Original file lyrics: ${if (fileTags.lyrics?.isNotBlank() == true) "present (${fileTags.lyrics!!.length} chars)" else "empty/null"}")
                Timber.tag(TAG).d("Final lyrics: ${if (prePopulatedTags.lyrics?.isNotBlank() == true) "present (${prePopulatedTags.lyrics!!.length} chars)" else "empty/null"}")
                Timber.tag(TAG).d("Found ${linkedLrcFiles.size} linked LRC files")
                
                _uiState.value = TagEditorUiState.Loaded(
                    currentTags = prePopulatedTags,
                    originalTags = fileTags, // Keep original file tags for comparison
                    filePath = filePath,
                    fileName = fileName,
                    supportedFormats = supportedFormats,
                    supportsEmbeddedLyrics = supportsEmbeddedLyrics,
                    linkedLrcFiles = linkedLrcFiles,
                    songId = songData.id
                )
                
                Timber.tag(TAG).d("State set to: ${_uiState.value::class.simpleName}")
                Timber.tag(TAG).d("Successfully loaded tags with pre-population for: $fileName")
            } catch (e: Exception) {
                val errorMessage = result.getResultMessage()
                _uiState.value = TagEditorUiState.Error(errorMessage)
                Timber.tag(TAG).e("Failed to load tags: $errorMessage")
            }
        }
    }

    /**
     * Updates a specific tag field using polymorphic dispatch.
     */
    fun updateTag(field: String, value: String) {
        Timber.tag(TAG).d("updateTag called on ViewModel: ${this.hashCode()}, field: $field, value: $value")
        
        // Log tag update analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_FIELD_EDITED) {
            param(AnalyticsConstants.Params.TAG_FIELD, field)
            param(AnalyticsConstants.Params.INPUT_LENGTH, value.length)
            param(AnalyticsConstants.Params.TAG_OPERATION, "update")
        }
        
        val currentState = _uiState.value
        Timber.tag(TAG).d("Current state in updateTag: ${currentState::class.simpleName}")
        if (currentState !is TagEditorUiState.Loaded) {
            Timber.tag(TAG).w("Cannot update tag - state is not Loaded: $currentState")
            return
        }

        val updatedTags = updateTagField(currentState.currentTags, field, value)
        val isModified = updatedTags != currentState.originalTags
        
        _uiState.value = currentState.copy(
            currentTags = updatedTags,
            isModified = isModified,
            validationErrors = validateTags(updatedTags)
        )
        
        Timber.tag(TAG).d("Updated tag $field to: $value on ViewModel: ${this.hashCode()}")
    }

    /**
     * Updates a specific field in the AudioTag using reflection-like approach.
     */
    private fun updateTagField(tags: AudioTag, field: String, value: String): AudioTag {
        // Don't trim during editing - preserve user input including spaces
        // Empty string check prevents saving blank fields
        val processedValue = if (value.isEmpty()) null else value
        
        return when (field) {
            "title" -> tags.copy(title = processedValue)
            "artist" -> tags.copy(artist = processedValue)
            "album" -> tags.copy(album = processedValue)
            "track" -> tags.copy(track = processedValue)
            "genre" -> tags.copy(genre = processedValue)
            "year" -> tags.copy(year = processedValue)
            "albumArtist" -> tags.copy(albumArtist = processedValue)
            "composer" -> tags.copy(composer = processedValue)
            "discNumber" -> tags.copy(discNumber = processedValue)
            "comment" -> tags.copy(comment = processedValue)
            "lyrics" -> tags.copy(lyrics = processedValue)
            "isrc" -> tags.copy(isrc = processedValue)
            "musicBrainzId" -> tags.copy(musicBrainzId = processedValue)
            else -> tags
        }
    }

    /**
     * Validates tag fields and returns validation errors.
     */
    private fun validateTags(tags: AudioTag): Map<String, String> {
        val errors = mutableMapOf<String, String>()
        
        // Validate year format
        tags.year?.let { year ->
            if (year.isNotEmpty() && !year.matches(Regex("\\d{4}"))) {
                errors["year"] = "Invalid year format"
            }
        }
        
        // Validate track number
        tags.track?.let { track ->
            if (track.isNotEmpty() && !track.matches(Regex("\\d+"))) {
                errors["track"] = "Invalid track number"
            }
        }
        
        // Validate disc number
        tags.discNumber?.let { disc ->
            if (disc.isNotEmpty() && !disc.matches(Regex("\\d+"))) {
                errors["discNumber"] = "Invalid disc number"
            }
        }
        
        return errors
    }

    /**
     * Saves tags directly to the original file location.
     * For Android R+, this will prepare cache files and return them for MediaStore permission request.
     * For older Android versions, this will write directly if permissions allow.
     */
    fun saveTags(context: Context, onCacheFilesReady: ((List<File>) -> Unit)? = null) {
        // Log save initiated analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_SAVE_CLICKED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.TAG_EDITOR_SCREEN)
        }
        
        val currentState = _uiState.value
        if (currentState !is TagEditorUiState.Loaded) {
            _snackbarMessage.value = SnackbarMessage.Error(text = "No file loaded")
            // Log save failed analytics event
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_SAVE_FAILED) {
                param(AnalyticsConstants.Params.ERROR_MESSAGE, "No file loaded")
                param(AnalyticsConstants.Params.ERROR_CATEGORY, "state_error")
            }
            return
        }

        if (!currentState.isModified) {
            _snackbarMessage.value = SnackbarMessage.Warning(text = "No changes to save")
            // Log save failed analytics event  
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_SAVE_FAILED) {
                param(AnalyticsConstants.Params.ERROR_MESSAGE, "No changes to save")
                param(AnalyticsConstants.Params.ERROR_CATEGORY, "no_changes")
            }
            return
        }

        _uiState.value = TagEditorUiState.Saving(currentState.currentTags, "Saving tags...")
        
        viewModelScope.launch {
            try {
                // Trim tags before saving
                val trimmedTags = trimTagsForExport(currentState.currentTags)
                
                // First, embed lyrics if supported and lyrics exist
                if (currentState.supportsEmbeddedLyrics && !trimmedTags.lyrics.isNullOrBlank()) {
                    try {
                        // Determine if lyrics are synchronized (contain timestamps)
                        val isSync = trimmedTags.lyrics!!.contains(Regex("\\[\\d{2}:\\d{2}\\.\\d{2}]"))
                        
                        Timber.tag(TAG).d("Embedding lyrics (sync=$isSync) before saving tags")
                        val embedResult = embedLyricsUseCase(
                            currentState.filePath,
                            trimmedTags.lyrics!!,
                            isSync
                        )
                        
                        // Process embed result but continue with tag saving regardless
                        try {
                            embedResult.process()
                            Timber.tag(TAG).d("Lyrics embedded successfully")
                        } catch (e: Exception) {
                            Timber.tag(TAG).w(e, "Lyrics embedding failed, continuing with tag save")
                        }
                    } catch (e: Exception) {
                        Timber.tag(TAG).w(e, "Exception during lyrics embedding, continuing with tag save")
                    }
                }
                
                // Create WriteInfo with tag values
                val writeInfo = WriteInfo(
                    paths = listOf(currentState.filePath),
                    values = trimmedTags.toFieldKeyMap(),
                    artworkInfo = null // TODO: Add artwork support later
                )
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // For Android R+, create cache files and request permission
                    val result = soly.lyricsgenerator.utils.AudioTagger.writeTagsToFilesR(context, writeInfo)
                    
                    if (result.isSuccess && !result.cacheFiles.isNullOrEmpty()) {
                        // Return cache files to UI for MediaStore permission request
                        onCacheFilesReady?.invoke(result.cacheFiles)
                        _uiState.value = currentState.copy(
                            originalTags = trimmedTags,
                            isModified = false
                        )
                        _snackbarMessage.value = SnackbarMessage.Warning(text = "Permission required to save changes")
                    } else {
                        _uiState.value = currentState
                        _snackbarMessage.value = SnackbarMessage.Error(text = "Failed to prepare files for saving")
                    }
                } else {
                    // For older Android versions, write directly
                    val result = soly.lyricsgenerator.utils.AudioTagger.writeTagsToFiles(context, writeInfo)
                    
                    when (result) {
                        is AudioTagResult.Success -> {
                            _uiState.value = currentState.copy(
                                originalTags = trimmedTags,
                                isModified = false
                            )
                            _snackbarMessage.value = SnackbarMessage.Success(text = result.getResultMessage())
                            
                            // Trigger song data refresh in other screens
                            triggerSongDataRefresh()
                        }
                        is AudioTagResult.Error -> {
                            _uiState.value = currentState
                            _snackbarMessage.value = SnackbarMessage.Error(text = result.getResultMessage())
                        }
                        else -> {
                            _uiState.value = currentState
                            _snackbarMessage.value = SnackbarMessage.Error(text = "Unexpected result")
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Exception saving tags")
                _uiState.value = currentState
                _snackbarMessage.value = SnackbarMessage.Error(text = "Failed to save tags: ${e.message}")
            }
        }
    }
    
    /**
     * Persists tag changes from cache files to original locations after MediaStore permission is granted.
     * This is called after the user grants permission on Android R+.
     */
    suspend fun persistTagChanges(
        context: Context,
        filePaths: List<String>,
        uris: List<Uri>,
        cacheFiles: List<File>
    ) {
        val currentState = _uiState.value
        if (currentState !is TagEditorUiState.Loaded) return
        
        try {
            soly.lyricsgenerator.utils.AudioTagger.persistChanges(
                context = context,
                paths = filePaths,
                destUris = uris,
                cacheFiles = cacheFiles
            )
            
            _snackbarMessage.value = SnackbarMessage.Success(text = "Tags saved successfully")
            
            // Trigger song data refresh in other screens
            triggerSongDataRefresh()
            
            // Clean up cache files
            cacheFiles.forEach { it.delete() }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to persist tag changes")
            _snackbarMessage.value = SnackbarMessage.Error(text = "Failed to save changes: ${e.message}")
        }
    }

    /**
     * Handles the result of embedding lyrics.
     */
    private fun handleEmbedResult(result: AudioTagResult<AudioTag>, previousState: TagEditorUiState.Loaded) {
        try {
            result.process()
            val successMessage = result.getResultMessage()
            
            _uiState.value = previousState
            _snackbarMessage.value = SnackbarMessage.Success(
                text = successMessage.ifEmpty { "Lyrics embedded successfully" }
            )
            
            Timber.tag(TAG).d("Successfully embedded lyrics")
        } catch (e: Exception) {
            _uiState.value = previousState
            _snackbarMessage.value = SnackbarMessage.Error(text = result.getResultMessage())
            Timber.tag(TAG).e("Failed to embed lyrics: ${result.getResultMessage()}")
        }
    }

    /**
     * Reloads the current tags from the file.
     */
    fun loadCurrentTags() {
        val currentState = _uiState.value
        if (currentState !is TagEditorUiState.Loaded) return

        loadAudioTags(currentState.filePath)
    }

    /**
     * Navigates to the next/previous file in batch mode.
     */
    fun navigateBatch(direction: Int) {
        val batchState = _batchState.value
        if (batchState !is BatchEditingState.BatchMode) return

        val newIndex = (batchState.currentIndex + direction)
            .coerceIn(0, batchState.filePaths.size - 1)
        
        if (newIndex != batchState.currentIndex) {
            _batchState.value = batchState.copy(currentIndex = newIndex)
            loadAudioTags(batchState.filePaths[newIndex])
        }
    }



    /**
     * Dismisses the current snackbar message.
     */
    fun dismissSnackbar() {
        _snackbarMessage.value = null
    }
    
    /**
     * Trims leading and trailing whitespace from all tag fields for export.
     * Preserves internal spacing between words.
     */
    private fun trimTagsForExport(tags: AudioTag): AudioTag {
        return tags.copy(
            title = tags.title?.trim()?.takeIf { it.isNotEmpty() },
            artist = tags.artist?.trim()?.takeIf { it.isNotEmpty() },
            album = tags.album?.trim()?.takeIf { it.isNotEmpty() },
            track = tags.track?.trim()?.takeIf { it.isNotEmpty() },
            genre = tags.genre?.trim()?.takeIf { it.isNotEmpty() },
            year = tags.year?.trim()?.takeIf { it.isNotEmpty() },
            albumArtist = tags.albumArtist?.trim()?.takeIf { it.isNotEmpty() },
            composer = tags.composer?.trim()?.takeIf { it.isNotEmpty() },
            discNumber = tags.discNumber?.trim()?.takeIf { it.isNotEmpty() },
            comment = tags.comment?.trim()?.takeIf { it.isNotEmpty() },
            lyrics = tags.lyrics?.trim()?.takeIf { it.isNotEmpty() },
            isrc = tags.isrc?.trim()?.takeIf { it.isNotEmpty() },
            musicBrainzId = tags.musicBrainzId?.trim()?.takeIf { it.isNotEmpty() }
        )
    }

    /**
     * Triggers a refresh of song data in Music and Song Details screens
     * after tags have been successfully saved.
     * Also scans the modified file to update MediaStore.
     */
    private fun triggerSongDataRefresh() {
        Timber.tag(TAG).d("Triggering song data refresh after successful tag save")
        
        // Scan the modified file to update MediaStore
        val currentState = _uiState.value
        if (currentState is TagEditorUiState.Loaded) {
            val filePath = currentState.filePath
            Timber.tag(TAG).d("Scanning file to update MediaStore: $filePath")
            
            MediaScannerConnection.scanFile(
                context,
                arrayOf(filePath),
                null
            ) { path, uri ->
                Timber.tag(TAG).d("MediaStore scan completed for: $path, URI: $uri")
                
                // Add a small delay to ensure MediaStore has fully processed the update
                viewModelScope.launch {
                    kotlinx.coroutines.delay(500) // 500ms delay
                    
                    // After MediaStore scan completes and delay, trigger song reload
                    MusicPlayerService.startService(
                        context,
                        MusicPlayerService.ACTION_LOAD_SONGS
                    )
                }
            }
        } else {
            // Fallback - just reload songs without scan
            MusicPlayerService.startService(
                context,
                MusicPlayerService.ACTION_LOAD_SONGS
            )
        }
    }
}