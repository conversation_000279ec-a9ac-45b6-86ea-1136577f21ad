package soly.lyricsgenerator.ui.screens.create_screen.utils

import androidx.annotation.StringRes
import soly.lyricsgenerator.R

fun formatTimestamp(timestamp: Long): String {
    val minutes = (timestamp / 60000).toString().padStart(2, '0')
    val seconds = ((timestamp % 60000) / 1000).toString().padStart(2, '0')
    val milliseconds = ((timestamp % 1000) / 10).toString().padStart(2, '0')
    return "[$minutes:$seconds.$milliseconds]"
}

/**
 * Formats a song title and artist for display.
 * If artist is null, empty, or "unknown", returns only the title.
 * @param title The song title (non-null)
 * @param artist The artist name (nullable)
 * @param separator The separator string, default is "by"
 */
fun formatTitleAndArtist(title: String, artist: String?, separator: String = "by"): String {
    val artistNormalized = artist?.trim()?.lowercase()
    return if (
        artistNormalized.isNullOrEmpty() ||
        artistNormalized == "unknown" ||
        artistNormalized == "<unknown>"
    ) {
        title
    } else {
        "$title $separator ${artist.trim()}"
    }
}

@StringRes
fun getStepText(stepNumber: Int): Int {
    return when (stepNumber) {
        1 -> R.string.step_text_choose_song
        2 -> R.string.step_text_add_lyrics
        else -> R.string.step_text_start
    }
}
