package soly.lyricsgenerator.ui.screens.songs_list_screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.outlined.Edit
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.google.firebase.analytics.FirebaseAnalytics
import kotlinx.coroutines.launch
import my.nanihadesuka.compose.LazyColumnScrollbar
import my.nanihadesuka.compose.ScrollbarSettings
import soly.lyricsgenerator.MainActivity
import soly.lyricsgenerator.R
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.navigation.navigateToTagEditor
import soly.lyricsgenerator.ui.screens.components.CompactMusicPlayerController
import soly.lyricsgenerator.ui.screens.components.SongDetailsBottomSheet
import soly.lyricsgenerator.ui.screens.components.SortBottomSheet
import soly.lyricsgenerator.ui.screens.components.ArtistFilterBottomSheet
import soly.lyricsgenerator.ui.screens.song_details_screen.FavoriteState
import soly.lyricsgenerator.ui.theme.AccentColor
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.ui.constants.UITestTags
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import soly.lyricsgenerator.utils.AlbumArtUtils

@Composable
fun EmptyStateView(
    message: String,
    icon: ImageVector? = null,
    showButton: Boolean,
    buttonText: String = "",
    onButtonClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth()
        ) {
            icon?.let {
                Icon(
                    imageVector = it,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            if (showButton) {
                Spacer(modifier = Modifier.height(24.dp))
                
                Button(onClick = onButtonClick) {
                    Text(text = buttonText)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MusicScreen(
    navController: NavHostController,
    viewModel: MusicViewModel
) {
    val uiState by viewModel.getUIState().collectAsState()
    val listState = rememberLazyListState()
    val enteredScreen by viewModel.enteredScreen.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val hasAudioPermission by viewModel.hasAudioPermission.collectAsState()
    val showFavoritesOnly by viewModel.showFavoritesOnly.collectAsState()

    // Track filtered songs state
    val filteredSongsState by viewModel.filteredSongsFlow.collectAsState()
    
    // Sort bottom sheet state
    val showSortBottomSheet by viewModel.showSortBottomSheet.collectAsState()
    val sortType by viewModel.sortType.collectAsState()
    val sortOrder by viewModel.sortOrder.collectAsState()
    val sortBottomSheetState = rememberModalBottomSheetState()

    val showArtistFilterBottomSheet by viewModel.showArtistFilterBottomSheet.collectAsState()
    val artistFilter by viewModel.artistFilter.collectAsState()
    val filterBottomSheetState = rememberModalBottomSheetState()
    val coroutineScope = rememberCoroutineScope()

    var isShowingInitialLoadProgress by remember { mutableStateOf(false) }






    // Auto-scroll to currently playing song
    LaunchedEffect(filteredSongsState.songs, uiState.currentlyPlayingSong) {
        val currentFilteredSongs = filteredSongsState.songs
        if (enteredScreen && currentFilteredSongs.isNotEmpty() && uiState.currentlyPlayingSong != null) {
            val displayIndex = currentFilteredSongs.indexOfFirst { it.id == uiState.currentlyPlayingSong?.id }
            if (displayIndex != -1) {
                listState.animateScrollToItem(displayIndex)
            }
        }
    }

    // Handle navigation events
    LaunchedEffect(viewModel.navigationEvent) {
        viewModel.navigationEvent.collect { route ->
            navController.navigate(route.route)
        }
    }

    // Track entry/exit from screen
    DisposableEffect(key1 = Unit) {
        viewModel.onEnterScreen()
        
        onDispose {
            viewModel.onExitScreen()
        }
    }

    // Handle back stack changes
    LaunchedEffect(navController) {
        navController.currentBackStackEntryFlow.collect { backStackEntry ->
            val currentRoute = backStackEntry.destination.route
            
            if (currentRoute != NavRoutes.Music.route && currentRoute != NavRoutes.SongDetails.route) {
                viewModel.onExitScreen()
            }
        }
    }

    // Request current state from service when screen becomes active
    LaunchedEffect(Unit) {
        if (hasAudioPermission && uiState.songs.isEmpty()) {
            isShowingInitialLoadProgress = true

            // Start timeout for the progress bar
            launch { 
                kotlinx.coroutines.delay(3000L)
                if (isShowingInitialLoadProgress && uiState.songs.isEmpty() && hasAudioPermission) {
                    isShowingInitialLoadProgress = false
                }
            }
        }
        viewModel.loadSongsIfEmpty()
        
        // ADDITIONAL FIX: Force reload to ensure sorting is applied when screen enters
        // This handles case where songs are cached but sorting preferences might not be applied
        if (hasAudioPermission && uiState.songs.isNotEmpty()) {
            viewModel.forceReloadSongs()
        }
    }

    // If songs load, or permission is lost, or loading is complete, hide the progress bar.
    LaunchedEffect(uiState.songs, hasAudioPermission, uiState.isLoading) {
        if (uiState.songs.isNotEmpty() || !hasAudioPermission || !uiState.isLoading) {
            if (isShowingInitialLoadProgress) {
                isShowingInitialLoadProgress = false
            }
        }
    }

    // Log screen view
    LaunchedEffect(Unit) {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.MUSIC)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.MUSIC_SCREEN)
        }
    }


    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = AppGradientBrush)
    ) {
        // Check if we need to show empty state
        if (!hasAudioPermission) {
            // No permission granted
            val context = LocalContext.current
            EmptyStateView(
                message = stringResource(R.string.permission_required),
                icon = Icons.Default.Warning,
                showButton = true,
                buttonText = stringResource(R.string.grant_permission),
                onButtonClick = {
                    // Request permissions through MainActivity
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_GRANT_PERMISSION_CLICK) { // Log permission button click
                        param(AnalyticsConstants.Params.PERMISSION_TYPE, AnalyticsConstants.Params.AUDIO)
                    }
                    
                    (context as? MainActivity)?.requestAudioPermissions()
                }
            )
        } else if ((isShowingInitialLoadProgress || uiState.isLoading) && uiState.songs.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (searchQuery.isNotEmpty() && filteredSongsState.songs.isEmpty() && uiState.songs.isNotEmpty()) {
            // Search active but no results found
            EmptyStateView(
                message = stringResource(R.string.no_search_results),
                icon = Icons.Default.Warning,
                showButton = false
            )
        } else if (showFavoritesOnly && filteredSongsState.songs.isEmpty() && uiState.songs.isNotEmpty()) {
            // Favorites filter active but no favorite songs found
            EmptyStateView(
                message = stringResource(R.string.no_favorite_songs_yet),
                showButton = false
            )
        } else if (uiState.songs.isEmpty()) {
            // Permission granted but no songs found
            EmptyStateView(
                message = stringResource(R.string.no_music_found),
                icon = Icons.Default.Warning,
                showButton = false
            )
        } else {
            // LazyColumnScrollbar with proper drag navigation
            LazyColumnScrollbar(
                state = listState,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), // Take remaining space, leaving room for compact player
                settings = ScrollbarSettings(
                    thumbUnselectedColor = Color.White.copy(alpha = 0.7F),
                    thumbSelectedColor = Color.White,
                    thumbThickness = 6.dp
                )
            ) {
                // LazyColumn for the songs list when songs are available
                LazyColumn(
                    state = listState, 
                    modifier = Modifier.fillMaxSize()
                ) {
                    val songsToDisplay = filteredSongsState.songs
                    
                    items(
                        items = songsToDisplay,
                        key = { song -> song.id }
                    ) { song ->
                        // Removed expensive indexOf operation for better performance with large lists

                        SongItem(
                            song = song,
                            currentlyPlayingSong = uiState.currentlyPlayingSong,
                            navController = navController,
                            viewModel = viewModel,
                            onClick = { 
                                viewModel.playSong(song)
                            }
                        )
                    }
                }
            }
        }

        // Compact music player at the bottom
        CompactMusicPlayerController(
            currentSong = uiState.currentlyPlayingSong,
            currentPosition = uiState.currentPosition,
            isPlaying = uiState.isPlaying,
            isVisible = uiState.currentlyPlayingSong != null,
            onPlayPauseClicked = {
                if (uiState.isPlaying) {
                    viewModel.pausePlaying()
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PAUSE_SONG) {
                        param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                        param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: "Unknown")
                        param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.COMPACT_PLAYER)
                    }
                } else {
                    if (uiState.currentPosition > 0) {
                        viewModel.resumePlaying()
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_RESUME_SONG) {
                            param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: "Unknown")
                            param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.COMPACT_PLAYER)
                        }
                    } else {
                        uiState.currentlyPlayingSong?.let { song ->
                            viewModel.playSong(song)
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PLAY_SONG) {
                                param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                                param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                                param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.COMPACT_PLAYER)
                            }
                        }
                    }
                }
            },
            onNextClicked = {
                viewModel.playNextSong()
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_NEXT_SONG_CLICK) {
                    param(AnalyticsConstants.Params.CURRENT_SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.COMPACT_PLAYER)
                }
            },
            onPreviousClicked = {
                viewModel.playPreviousSong()
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PREVIOUS_SONG_CLICK) {
                    param(AnalyticsConstants.Params.CURRENT_SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.COMPACT_PLAYER)
                }
            },
            onPlayerClicked = {
                // Navigate to song details screen without restarting playback
                // Don't set song_id since the song is already playing and SongDetailsScreen should detect it
                navController.navigate(NavRoutes.SongDetails.route)
                uiState.currentlyPlayingSong?.let { song ->
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_COMPACT_PLAYER_EXPAND) {
                        param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                        param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                    }
                }
            }
        )
    }
    
    // Sort Bottom Sheet
    if (showSortBottomSheet) {
        SortBottomSheet(
            bottomSheetState = sortBottomSheetState,
            coroutineScope = coroutineScope,
            sortType = sortType,
            sortOrder = sortOrder,
            onSortChanged = viewModel::onSortChanged,
            onDismiss = viewModel::hideSortBottomSheet
        )
    }

    if (showArtistFilterBottomSheet) {
        val artists = listOf(stringResource(R.string.filter_all_artists)) +
            uiState.songs.map { it.artist }.distinct().sorted()
        ArtistFilterBottomSheet(
            bottomSheetState = filterBottomSheetState,
            coroutineScope = coroutineScope,
            artists = artists,
            selectedArtist = artistFilter,
            onArtistSelected = {
                viewModel.setArtistFilter(it)
                viewModel.hideArtistFilterBottomSheet()
            },
            onDismiss = viewModel::hideArtistFilterBottomSheet
        )
    }
}

@Composable
fun AlbumArtOrIcon(
    song: Song,
    isCurrentSong: Boolean,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var albumArt by remember(song.id) { mutableStateOf(null as android.graphics.Bitmap?) }

    LaunchedEffect(song.id) {
        albumArt = AlbumArtUtils.getAlbumArt(context, song)
    }

    albumArt?.let { art ->
        Box(
            modifier = modifier
                .size(48.dp)
                .clip(RoundedCornerShape(4.dp))
        ) {
            Image(
                bitmap = art.asImageBitmap(),
                contentDescription = "Album art for ${song.title}",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }
    } ?: run {
        Box(
            modifier = modifier
                .size(48.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(
                    color = if (isCurrentSong) 
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                    else 
                        Color.Gray.copy(alpha = 0.3f)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.MusicNote,
                contentDescription = null,
                modifier = Modifier.size(28.dp),
                tint = if (isCurrentSong) MaterialTheme.colorScheme.primary else Color.White
            )
        }
    }
}

@Composable
fun SongItem(
    song: Song,
    currentlyPlayingSong: Song?,
    navController: NavHostController,
    viewModel: MusicViewModel,
    onClick: () -> Unit
) {
    val isCurrentSong = song.id == currentlyPlayingSong?.id
    var showDropdownMenu by remember { mutableStateOf(false) }
    var showDetailsBottomSheet by remember { mutableStateOf(false) }
    
    // Use the song's own favorite status instead of global state
    val favoriteState = if (song.isFavorite) FavoriteState.Favorite else FavoriteState.NotFavorite

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { 
                onClick()
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PLAY_SONG) {
                    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                    param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.SONG_ITEM_CLICK)
                }
             }
            .background(Color.Transparent)
            .padding(16.dp)
            .testTag("song_item_${song.id}"),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Album art or music icon
        AlbumArtOrIcon(
            song = song,
            isCurrentSong = isCurrentSong
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // Title and artist
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = song.title,
                style = MaterialTheme.typography.titleMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = if (isCurrentSong) MaterialTheme.colorScheme.primary else Color.White
            )
            
            if (song.artist.isNotBlank()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = song.artist,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = Color(0xFFB0B0B0) // Light gray for artist
                )
            }
        }
        
        // Three dots menu
        Box {
            IconButton(
                onClick = { showDropdownMenu = true },
                modifier = Modifier.testTag(UITestTags.SONG_ITEM_MORE_OPTIONS)
            ) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = stringResource(R.string.content_desc_more_options),
                    tint = Color.White
                )
            }
            
            DropdownMenu(
                expanded = showDropdownMenu,
                onDismissRequest = { showDropdownMenu = false }
            ) {
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.details)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Info,
                            contentDescription = null,
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        showDetailsBottomSheet = true
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_SONG_DETAILS_CLICK) {
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                        }
                    }
                )
                DropdownMenuItem(
                    text = { Text(stringResource(R.string.tag_editor_title)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Outlined.Edit,
                            contentDescription = null,
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        navController.navigateToTagEditor(song)
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_TAG_EDITOR_CLICK) {
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                            param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.MUSIC_SCREEN)
                        }
                    }
                )
                DropdownMenuItem(
                    text = { 
                        Text(
                            text = favoriteState.getMenuText()
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = favoriteState.getIcon(),
                            contentDescription = favoriteState.getContentDescription(),
                            tint = AccentColor
                        )
                    },
                    onClick = {
                        showDropdownMenu = false
                        viewModel.toggleFavorite(song.id)
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_FAVORITE_TOGGLED) {
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                            param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.MUSIC_SCREEN)
                        }
                    }
                )
            }
        }
    }
    
    if (showDetailsBottomSheet) {
        SongDetailsBottomSheet(
            song = song,
            onDismiss = { showDetailsBottomSheet = false }
        )
    }
}
