package soly.lyricsgenerator.ui.screens.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.SkipNext
import androidx.compose.material.icons.filled.SkipPrevious
import androidx.compose.material.icons.filled.Forward5
import androidx.compose.material.icons.filled.Replay5
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.res.stringResource
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.domain.model.Song
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.material.icons.filled.Shuffle
import androidx.compose.ui.graphics.Color
import soly.lyricsgenerator.ui.screens.song_details_screen.FavoriteState
import soly.lyricsgenerator.R
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.ui.theme.InteractiveAccent

@Composable
fun MusicPlayerControllerNew(
    currentSong: Song?,
    currentPosition: Long,
    isPlaying: Boolean,
    onSeekTo: (Long) -> Unit,
    onPlayPauseClicked: () -> Unit,
    onNextClicked: () -> Unit,
    onPreviousClicked: () -> Unit,
    currentLyricLine: String?,
    modifier: Modifier = Modifier,
    displayTitleInsteadOfLyrics: Boolean = false,
    showPreviousButton: Boolean = true,
    showNextButton: Boolean = true,
    showShuffleButton: Boolean = false,
    isShuffleEnabled: Boolean = false,
    onShuffleClicked: () -> Unit = {},
    showFavoriteButton: Boolean = false,
    favoriteState: FavoriteState? = null,
    onFavoriteClicked: () -> Unit = {},
    onSeekForward5SecondsClicked: () -> Unit = {},
    onSeekBackward5SecondsClicked: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 16.dp),
    ) {
        Text(
            modifier = Modifier.padding(start = 8.dp, end = 8.dp),
            text = if (displayTitleInsteadOfLyrics || currentLyricLine.isNullOrBlank()) {
                buildString {
                    append(currentSong?.title ?: "...")
                    currentSong?.artist?.takeIf { it.isNotBlank() }?.let {
                        append(" - ")
                        append(it)
                    }
                }
            } else {
                currentLyricLine
            },
            style = MaterialTheme.typography.titleLarge,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        Slider(
            value = currentPosition.toFloat(),
            onValueChange = { newPosition ->
                // Log seek analytics
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_SEEK_STARTED) {
                    param(AnalyticsConstants.Params.SEEK_POSITION_MS, newPosition.toLong())
                    param(AnalyticsConstants.Params.CURRENT_TIME_MS, currentPosition)
                    param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                    param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller")
                }
                onSeekTo(newPosition.toLong())
            },
            valueRange = 0f..(currentSong?.duration ?: 0L).toFloat(),
            modifier = Modifier.fillMaxWidth(),
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.tertiary,
                activeTrackColor = MaterialTheme.colorScheme.tertiary
            )
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            Text(text = formatTime(currentPosition))

            if (showPreviousButton || showNextButton || showShuffleButton) {
                Text(text = formatTime(currentSong?.duration ?: 0L))
            } else {
                // Row with seek buttons and play/pause in the center
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Backward 5 seconds button
                    IconButton(
                        onClick = onSeekBackward5SecondsClicked,
                        enabled = currentSong != null
                    ) {
                        Icon(
                            imageVector = Icons.Default.Replay5,
                            contentDescription = stringResource(R.string.content_desc_backward_5_seconds),
                            modifier = Modifier.size(32.dp),
                            tint = if (currentSong != null) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    }
                    
                    PlayPauseButton(
                        isPlaying = isPlaying, 
                        onClick = {
                            // Log play/pause analytics (simplified controls)
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_PLAY_CLICKED) {
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.IS_PLAYING, isPlaying.toString())
                                param(AnalyticsConstants.Params.ACTION, if (isPlaying) "pause" else "play")
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller_simple")
                            }
                            onPlayPauseClicked()
                        }
                    )
                    
                    // Forward 5 seconds button
                    IconButton(
                        onClick = onSeekForward5SecondsClicked,
                        enabled = currentSong != null
                    ) {
                        Icon(
                            imageVector = Icons.Default.Forward5,
                            contentDescription = stringResource(R.string.content_desc_forward_5_seconds),
                            modifier = Modifier.size(32.dp),
                            tint = if (currentSong != null) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    }
                }
                
                Text(text = formatTime(currentSong?.duration ?: 0L))
            }
        }

        if (showShuffleButton || showPreviousButton || showNextButton || showFavoriteButton) {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                if (showShuffleButton) {
                    IconButton(
                        onClick = { 
                            // Log shuffle toggle analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_SHUFFLE_TOGGLED) {
                                param(AnalyticsConstants.Params.SHUFFLE_ENABLED, (!isShuffleEnabled).toString())
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller")
                            }
                            onShuffleClicked() 
                        },
                        modifier = Modifier.align(Alignment.CenterStart).padding(start = 8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Shuffle,
                            contentDescription = stringResource(R.string.content_desc_shuffle),
                            tint = if (isShuffleEnabled) InteractiveAccent else Color.White,
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }

                if (showFavoriteButton && favoriteState != null) {
                    IconButton(
                        onClick = {
                            // Log favorite toggle analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_FAVORITE_TOGGLED) {
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.IS_FAVORITE, (favoriteState is FavoriteState.NotFavorite).toString())
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller")
                                param(AnalyticsConstants.Params.SOURCE, "player_controller")
                            }
                            onFavoriteClicked()
                        },
                        enabled = favoriteState !is FavoriteState.Loading,
                        modifier = Modifier.align(Alignment.CenterEnd).padding(end = 8.dp)
                    ) {
                        Icon(
                            imageVector = favoriteState.getIcon(),
                            contentDescription = favoriteState.getContentDescription(),
                            tint = favoriteState.getTint(),
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }

                Row(
                    modifier = Modifier.align(Alignment.Center),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    if (showPreviousButton) {
                        IconButton(onClick = { 
                            // Log previous clicked analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_PREVIOUS_CLICKED) {
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller")
                                param(AnalyticsConstants.Params.SOURCE, "player_controller_previous")
                            }
                            onPreviousClicked() 
                        }) {
                            Icon(
                                imageVector = Icons.Default.SkipPrevious,
                                contentDescription = stringResource(R.string.content_desc_previous),
                                modifier = Modifier.size(48.dp)
                            )
                        }
                    }

                    PlayPauseButton(
                        isPlaying = isPlaying, 
                        onClick = {
                            // Log play/pause analytics (full controls)
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_PLAY_CLICKED) {
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.IS_PLAYING, isPlaying.toString())
                                param(AnalyticsConstants.Params.ACTION, if (isPlaying) "pause" else "play")
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller_full")
                            }
                            onPlayPauseClicked()
                        }
                    )

                    if (showNextButton) {
                        IconButton(onClick = { 
                            // Log next clicked analytics
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.PLAYER_CONTROLLER_NEXT_CLICKED) {
                                param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                param(AnalyticsConstants.Params.COMPONENT_TYPE, "music_player_controller")
                                param(AnalyticsConstants.Params.SOURCE, "player_controller_next")
                            }
                            onNextClicked() 
                        }) {
                            Icon(
                                imageVector = Icons.Default.SkipNext,
                                contentDescription = stringResource(R.string.content_desc_next),
                                modifier = Modifier.size(48.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PlayPauseButton(isPlaying: Boolean, onClick: () -> Unit) {
    IconButton(onClick = onClick) {
        Icon(
            modifier = Modifier.size(48.dp),
            imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
            contentDescription = if (isPlaying) stringResource(R.string.content_desc_pause) else stringResource(R.string.content_desc_play)
        )
    }
}

fun formatTime(milliseconds: Long): String {
    val minutes = (milliseconds / 1000) / 60
    val seconds = (milliseconds / 1000) % 60
    return String.format("%02d:%02d", minutes, seconds)
}

@Preview(showBackground = true)
@Composable
fun MusicPlayerControllerPreview() {
    val sampleSong = Song(
        id = 1L,
        title = "Sample Song Title",
        artist = "Sample Artist",
        data = "/path/to/sample.mp3",
        duration = 185000L
    )

    MusicPlayerControllerNew(
        currentSong = sampleSong,
        currentPosition = 60000L,
        isPlaying = true,
        onSeekTo = {},
        onPlayPauseClicked = {},
        onNextClicked = {},
        onPreviousClicked = {},
        currentLyricLine = "This is the current lyric line, if available, otherwise it's just a placeholder...",
        displayTitleInsteadOfLyrics = false
    )
}

@Preview(showBackground = true)
@Composable
fun MusicPlayerControllerHiddenButtonsPreview() {
    val sampleSong = Song(
        id = 1L,
        title = "Sample Song Title",
        artist = "Sample Artist",
        data = "/path/to/sample.mp3",
        duration = 185000L
    )

    MusicPlayerControllerNew(
        currentSong = sampleSong,
        currentPosition = 60000L,
        isPlaying = true,
        onSeekTo = {},
        onPlayPauseClicked = {},
        onNextClicked = {},
        onPreviousClicked = {},
        currentLyricLine = "This is the current lyric line, if available, otherwise it's just a placeholder.",
        displayTitleInsteadOfLyrics = true,
        showPreviousButton = false,
        showNextButton = false
    )
}

@Preview(showBackground = true)
@Composable
fun MusicPlayerControllerWithShufflePreview() {
    val sampleSong = Song(
        id = 1L,
        title = "Sample Song With Shuffle",
        artist = "Shuffle Artist",
        data = "/path/to/sample.mp3",
        duration = 185000L
    )

    MusicPlayerControllerNew(
        currentSong = sampleSong,
        currentPosition = 90000L,
        isPlaying = false,
        onSeekTo = {},
        onPlayPauseClicked = {},
        onNextClicked = {},
        onPreviousClicked = {},
        currentLyricLine = null,
        displayTitleInsteadOfLyrics = true,
        showShuffleButton = true,
        isShuffleEnabled = true,
        onShuffleClicked = {}
    )
}
