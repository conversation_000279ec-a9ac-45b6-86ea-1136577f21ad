package soly.lyricsgenerator.ui.screens.create_screen

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.background
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.MutableStateFlow
import soly.lyricsgenerator.ui.screens.components.MusicPlayerControllerNew
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import timber.log.Timber
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.material3.HorizontalDivider

@Composable
fun CreateScreenContent(
    context: Context,
    songViewModel: CreateViewModel,
    sharedViewModel: SharedViewModel? = null,
    showStartButtonState: <PERSON><PERSON><PERSON>,
    paddingValues: PaddingValues,
    currentPosition: Long,
    musicControllerHeightPx: Int,
    currentPositionFlow: MutableStateFlow<Long>,
    musicControllerHeight: MutableStateFlow<Int>,
    onChangeSong: (() -> Unit)? = null,
    onUndoClicked: () -> Unit
) {
    val showPreview by songViewModel.showPreview.collectAsState()
    val selectedSong by songViewModel.selectedSong.collectAsState()
    val songDuration = selectedSong?.duration ?: 0L

    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
    ) {
        MainContentColumn(
            context = context,
            songViewModel = songViewModel,
            sharedViewModel = sharedViewModel,
            showStartButtonState = showStartButtonState,
            showPreview = showPreview,
            currentPosition = currentPosition,
            musicControllerHeightPx = musicControllerHeightPx,
            songDuration = songDuration,
            onChangeSong = onChangeSong,
            onUndoClicked = onUndoClicked,
            modifier = Modifier.fillMaxSize()
        )

        val isPlaying by songViewModel.isPlaying.collectAsState()
        val currentLyricLine by songViewModel.currentLyricLine.collectAsState()

        val onSeekTo: (Long) -> Unit = { position ->
            songViewModel.seekTo(context, position)
            if (!isPlaying) {
                songViewModel.resumeSong(context)
            }
        }
        val onPlayPauseClicked: () -> Unit = {
            if (isPlaying) {
                Timber.tag("DEBUG_FLOW").d("CreateScreenContent: Pausing current song")
                songViewModel.pauseSong(context)
            } else {
                val currentSong = songViewModel.selectedSong.value
                if (currentSong != null) {
                    // Get current position to determine if this is a new song or a paused one
                    val currentSongPosition = songViewModel.currentPositionFlow.value
                    if (currentSongPosition > 0) {
                        // If position is greater than 0, song was playing before and should be resumed
                        Timber.tag("DEBUG_FLOW").d("CreateScreenContent: Resuming paused song: ${currentSong.title}")
                        songViewModel.resumeSong(context)
                    } else {
                        // If position is 0, this is either a new song or a song that hasn't started playing
                        Timber.tag("DEBUG_FLOW").d("CreateScreenContent: Starting playback of song: ${currentSong.title}")
                        songViewModel.playSong(context)
                    }
                }
            }
        }

        if (songViewModel.isSongAndFileSelected())
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
            ) {

                MusicPlayerControllerNew(
                    currentSong = selectedSong,
                    isPlaying = isPlaying,
                    currentPosition = currentPositionFlow.collectAsState().value,
                    onSeekTo = onSeekTo,
                    onPlayPauseClicked = onPlayPauseClicked,
                    onNextClicked = { /* TODO: Implement if needed for Create Screen */ },
                    onPreviousClicked = { /* TODO: Implement if needed for Create Screen */ },
                    onSeekForward5SecondsClicked = { songViewModel.seekForward5Seconds() },
                    onSeekBackward5SecondsClicked = { songViewModel.seekBackward5Seconds() },
                    currentLyricLine = currentLyricLine,
                    displayTitleInsteadOfLyrics = false,
                    showPreviousButton = false,
                    showNextButton = false,
                    showShuffleButton = false,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surfaceColorAtElevation(3.dp))
                        .padding(bottom = 16.dp, start = 4.dp, end = 4.dp)
                        .onGloballyPositioned { coordinates ->
                            musicControllerHeight.value = coordinates.size.height
                            songViewModel.musicControllerHeight.value = coordinates.size.height
                        }
                )

                HorizontalDivider(
                    modifier = Modifier.fillMaxWidth(),
                    thickness = 1.dp,
                    color = MaterialTheme.colorScheme.outlineVariant
                )
            }
    }
}
