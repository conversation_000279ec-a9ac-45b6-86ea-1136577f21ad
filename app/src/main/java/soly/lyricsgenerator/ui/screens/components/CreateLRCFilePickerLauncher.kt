package soly.lyricsgenerator.ui.screens.components

import android.content.Context
import android.provider.OpenableColumns
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.lrc.flip
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.constants.FileConstants
import java.io.File
import android.net.Uri
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import timber.log.Timber

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun createLRCFilePickerLauncher(
    context: Context,
    coroutineScope: CoroutineScope,
    bottomSheetState: SheetState,
    songFileName: String? = null,
    onLrcLinesParsed: (Map<Int, String>, String, String) -> Unit
): ActivityResultLauncher<Array<String>> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri ->
        Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: File picker result received")
        uri?.let {
            Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Selected file URI: $uri")
            val contentResolver = context.contentResolver

            val mimeType = contentResolver.getType(uri)
            val uriString = uri.toString().lowercase()
            Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: File mime type: $mimeType, URI string: $uriString")
            if (mimeType == FileConstants.MimeTypes.TEXT_PLAIN || 
                mimeType == FileConstants.MimeTypes.APPLICATION_LRC || 
                mimeType == FileConstants.MimeTypes.APPLICATION_RTF || 
                mimeType == FileConstants.MimeTypes.TEXT_RTF ||
                uriString.endsWith(FileConstants.Extensions.LRC) || 
                uriString.endsWith(FileConstants.Extensions.RTF) || 
                uriString.endsWith(FileConstants.Extensions.TXT)) {
                Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: File type validation passed")
                try {
                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Opening input stream for file")
                    val inputStream = contentResolver.openInputStream(uri)
                    val fileContent = inputStream?.bufferedReader().use { it?.readText() }
                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: File content read, length: ${fileContent?.length ?: 0} characters")

                    // Use song file name if provided, otherwise fall back to timestamp
                    val lrcFileName = if (!songFileName.isNullOrEmpty()) {
                        // Extract the base name without extension and add .lrc
                        val baseName = songFileName.substringBeforeLast('.')
                        "$baseName.lrc"
                    } else {
                        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(
                            Date()
                        )
                        "temp_$timestamp.lrc"
                    }
                    
                    val lrcFile = File(context.cacheDir, lrcFileName)
                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Creating file: ${lrcFile.absolutePath} with name: $lrcFileName")
                    lrcFile.writeText(fileContent ?: "")

                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Parsing LRC file with flip.Parse")
                    val lrc = flip.Parse(lrcFile)
                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: LRC parsed successfully, lines count: ${lrc.Lines.size}")
                    
                    Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Calling onLrcLinesParsed callback")
                    onLrcLinesParsed(lrc.Lines, lrcFileName, lrcFile.absolutePath)

                    coroutineScope.launch {
                        Timber.tag("DEBUG_FLOW").d("CreateLRCFilePickerLauncher: Hiding bottom sheet")
                        bottomSheetState.hide()
                    }
                } catch (e: IOException) {
                    Timber.tag("DEBUG_FLOW").e(e, "CreateLRCFilePickerLauncher: IOException during file processing")
                    e.printStackTrace()
                }
            } else {
                Timber.tag("DEBUG_FLOW").w("CreateLRCFilePickerLauncher: File type validation failed - unsupported file type")
                Toast.makeText(
                    context,
                    context.getString(R.string.selected_file_is_not_an_lrc_file),
                    Toast.LENGTH_SHORT
                ).show()
            }
        } ?: run {
            Timber.tag("DEBUG_FLOW").w("CreateLRCFilePickerLauncher: File picker returned null URI")
        }
    }
}

fun getFileNameFromUri(context: Context, uri: Uri): String {
    val contentResolver = context.contentResolver
    val cursor = contentResolver.query(uri, null, null, null, null)
    return cursor?.use {
        if (it.moveToFirst()) {
            val displayNameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
            if (displayNameIndex != -1) {
                it.getString(displayNameIndex)
            } else {
                "Unknown"
            }
        } else {
            "Unknown"
        }
    } ?: "Unknown"
}

/**
 * File type detection utility using sealed classes with polymorphism
 * Following Clean Architecture guidelines - no when expressions
 */
sealed class FileExtensionMatcher {
    abstract fun matches(fileName: String): Boolean
    abstract fun getFileType(): FileType

    data object LrcMatcher : FileExtensionMatcher() {
        override fun matches(fileName: String): Boolean {
            return fileName.lowercase().endsWith(FileConstants.Extensions.LRC)
        }

        override fun getFileType(): FileType = FileType.LRC
    }

    data object TxtMatcher : FileExtensionMatcher() {
        override fun matches(fileName: String): Boolean {
            return fileName.lowercase().endsWith(FileConstants.Extensions.TXT)
        }

        override fun getFileType(): FileType = FileType.TXT
    }

    data object RtfMatcher : FileExtensionMatcher() {
        override fun matches(fileName: String): Boolean {
            return fileName.lowercase().endsWith(FileConstants.Extensions.RTF)
        }

        override fun getFileType(): FileType = FileType.RTF
    }
}

/**
 * Determines FileType based on file extension using polymorphic dispatch
 * @param fileName The name of the file including extension
 * @return FileType.LRC for .lrc files, FileType.TXT for .txt files,
 *         FileType.RTF for .rtf files, defaults to FileType.LRC for unknown extensions
 */
fun getFileTypeFromExtension(fileName: String): FileType {
    val matchers = listOf(
        FileExtensionMatcher.LrcMatcher,
        FileExtensionMatcher.TxtMatcher,
        FileExtensionMatcher.RtfMatcher
    )
    
    return matchers.firstOrNull { matcher ->
        matcher.matches(fileName)
    }?.getFileType() ?: FileType.LRC // Default to LRC for backward compatibility
}