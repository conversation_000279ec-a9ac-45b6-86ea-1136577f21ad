package soly.lyricsgenerator.ui.screens.create_screen

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material3.Icon
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R

@Composable
fun TxtFilePicker(
    pickedTextFileName: State<String?>,
    onPickTxtFile: () -> Unit
) {
    if (pickedTextFileName.value == null) {
        Button(
            onClick = { onPickTxtFile() },
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentWidth(Alignment.CenterHorizontally)
        ) {
            Text(stringResource(R.string.pick_txt_file))
        }
    } else {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 8.dp)
        ) {
            Text(
                text = stringResource(
                    R.string.picked_txt_file,
                    pickedTextFileName.value ?: stringResource(R.string.no_file_picked)
                ),
                color = Color.Gray,
                modifier = Modifier.weight(1f)
            )
            Button(
                onClick = { onPickTxtFile() },
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = stringResource(R.string.content_desc_replace)
                )
            }
        }
    }
}