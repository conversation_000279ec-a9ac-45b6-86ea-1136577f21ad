package soly.lyricsgenerator.ui.screens.create_screen

import soly.lyricsgenerator.ui.viewmodel.CreateViewModel

class TimestampUpdater(private val songViewModel: CreateViewModel) {

    fun calculateDecrementedTimestamp(index: Int, timestamp: Long, decrement: Long): Long {
        return if (timestamp == 0L && index > 0) {
            songViewModel.lrcKeyValuePairs.value[index - 1]?.first ?: 0L
        } else {
            timestamp - decrement
        }
    }

    fun updateDecrementedTimestamp(index: Int, decrement: Long) {
        val newTimestamp = calculateDecrementedTimestamp(index, songViewModel.lrcKeyValuePairs.value[index]?.first ?: 0L, decrement)
        val previousTimestamp = if (index > 0) songViewModel.lrcKeyValuePairs.value[index - 1]?.first ?: 0 else 0
        if (newTimestamp >= 0 && newTimestamp >= previousTimestamp) {
            songViewModel.updateLrcLineTimestamp(index, newTimestamp)
        }
    }

    fun calculateIncrementTimestamp(index: Int, timestamp: Long, increment: Long): Long {
        return if (timestamp == 0L && index > 0) {
            songViewModel.lrcKeyValuePairs.value[index - 1]?.first ?: 0L
        } else {
            timestamp + increment
        }
    }

    fun updateLrcTimestamp(index: Int, increment: Long) {
        val timestamp = songViewModel.lrcKeyValuePairs.value[index]?.first ?: 0L
        val newTimestamp = calculateIncrementTimestamp(index, timestamp, increment)
        val nextTimestamp = songViewModel.lrcKeyValuePairs.value[index + 1]?.first ?: Long.MAX_VALUE
        if (newTimestamp <= nextTimestamp || nextTimestamp == 0L) {
            songViewModel.updateLrcLineTimestamp(index, newTimestamp)
        }
    }
}