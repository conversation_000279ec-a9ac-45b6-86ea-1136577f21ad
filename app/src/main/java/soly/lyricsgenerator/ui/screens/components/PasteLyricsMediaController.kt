package soly.lyricsgenerator.ui.screens.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Forward5
import androidx.compose.material.icons.filled.Replay5
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.graphics.Color
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.constants.UITestTags
import timber.log.Timber
import kotlinx.coroutines.delay

/**
 * A simplified media controller for PasteLyricsScreen with song title, progress bar,
 * current/duration time below progress bar, and play/pause button only.
 * Based on MusicPlayerControllerNew but simplified for paste lyrics use case.
 */
@Composable
fun PasteLyricsMediaController(
    currentSong: Song?,
    currentPosition: Long,
    isPlaying: Boolean,
    onSeekTo: (Long) -> Unit,
    onPlayPauseClicked: () -> Unit,
    onSeekForward5SecondsClicked: () -> Unit = {},
    onSeekBackward5SecondsClicked: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // Track if user is dragging the slider
    var isDragging by remember { mutableStateOf(false) }
    var dragPosition by remember { mutableStateOf(0L) }
    var shouldDebounceReset by remember { mutableStateOf(false) }
    
    // Debounced drag state reset to prevent race condition with external position updates
    LaunchedEffect(shouldDebounceReset) {
        if (shouldDebounceReset) {
            Timber.tag("DEBUG_FLOW").d("PasteLyricsMediaController: Starting debounced drag reset (150ms delay)")
            delay(150) // Allow time for seek operation to propagate through service
            isDragging = false
            shouldDebounceReset = false
            Timber.tag("DEBUG_FLOW").d("PasteLyricsMediaController: Debounced drag reset completed")
        }
    }
    
    // Use drag position when dragging, otherwise use current position
    val displayPosition = if (isDragging) dragPosition else currentPosition
    
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(4.dp)
    ) {
        // Song title
        Text(
            text = currentSong?.title ?: stringResource(R.string.no_song_selected),
            style = MaterialTheme.typography.titleMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.padding(bottom = 1.dp)
        )

        // Progress bar
        Slider(
            value = displayPosition.toFloat(),
            onValueChange = { value ->
                if (!isDragging) {
                    Timber.tag("DEBUG_FLOW").d("PasteLyricsMediaController: User started dragging slider")
                }
                isDragging = true
                shouldDebounceReset = false // Cancel any pending reset
                dragPosition = value.toLong()
            },
            onValueChangeFinished = {
                Timber.tag("DEBUG_FLOW").d("PasteLyricsMediaController: User finished dragging, seeking to: ${dragPosition}ms")
                onSeekTo(dragPosition)
                shouldDebounceReset = true // Trigger debounced reset instead of immediate reset
            },
            valueRange = 0f..(currentSong?.duration ?: 0L).toFloat(),
            modifier = Modifier.fillMaxWidth(),
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.tertiary,
                activeTrackColor = MaterialTheme.colorScheme.tertiary
            )
        )

        // Time display and controls below progress bar
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 1.dp)
        ) {
            Text(
                text = formatTime(displayPosition),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.testTag(UITestTags.CURRENT_POSITION_TEXT)
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                IconButton(
                    onClick = onSeekBackward5SecondsClicked,
                    enabled = currentSong != null,
                    modifier = Modifier.testTag(UITestTags.SEEK_BACKWARD_5_SECONDS_BUTTON)
                ) {
                    Icon(
                        imageVector = Icons.Default.Replay5,
                        contentDescription = stringResource(R.string.content_desc_backward_5_seconds),
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }

                IconButton(
                    onClick = onPlayPauseClicked,
                    modifier = Modifier.testTag(UITestTags.PLAY_PAUSE_BUTTON)
                ) {
                    Icon(
                        imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                        contentDescription = if (isPlaying) stringResource(R.string.pause) else stringResource(R.string.play),
                        modifier = Modifier.size(32.dp),
                        tint = Color.White
                    )
                }

                IconButton(
                    onClick = onSeekForward5SecondsClicked,
                    enabled = currentSong != null,
                    modifier = Modifier.testTag(UITestTags.SEEK_FORWARD_5_SECONDS_BUTTON)
                ) {
                    Icon(
                        imageVector = Icons.Default.Forward5,
                        contentDescription = stringResource(R.string.content_desc_forward_5_seconds),
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            }

            Text(
                text = formatTime(currentSong?.duration ?: 0L),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.testTag(UITestTags.DURATION_TEXT)
            )
        }
    }
}


@Preview(showBackground = true)
@Composable
fun PasteLyricsMediaControllerPreview() {
    val sampleSong = Song(
        id = 1L,
        title = "Sample Song Title",
        artist = "Sample Artist",
        data = "/path/to/song.mp3",
        duration = 240000L, // 4 minutes
        isFavorite = false
    )
    
    PasteLyricsMediaController(
        currentSong = sampleSong,
        currentPosition = 120000L, // 2 minutes
        isPlaying = false,
        onSeekTo = { },
        onPlayPauseClicked = { },
        onSeekForward5SecondsClicked = {},
        onSeekBackward5SecondsClicked = {}
    )
}

@Preview(showBackground = true)
@Composable
fun PasteLyricsMediaControllerPlayingPreview() {
    val sampleSong = Song(
        id = 2L,
        title = "Very Long Song Title That Should Be Truncated With Ellipsis",
        artist = "Sample Artist",
        data = "/path/to/song.mp3",
        duration = 180000L, // 3 minutes
        isFavorite = true
    )
    
    PasteLyricsMediaController(
        currentSong = sampleSong,
        currentPosition = 45000L, // 45 seconds
        isPlaying = true,
        onSeekTo = { },
        onPlayPauseClicked = { },
        onSeekForward5SecondsClicked = {},
        onSeekBackward5SecondsClicked = {}
    )
}

@Preview(showBackground = true)
@Composable
fun PasteLyricsMediaControllerNoSongPreview() {
    PasteLyricsMediaController(
        currentSong = null,
        currentPosition = 0L,
        isPlaying = false,
        onSeekTo = { },
        onPlayPauseClicked = { },
        onSeekForward5SecondsClicked = {},
        onSeekBackward5SecondsClicked = {}
    )
}
