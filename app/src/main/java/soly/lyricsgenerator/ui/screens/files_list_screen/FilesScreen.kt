package soly.lyricsgenerator.ui.screens.files_list_screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.background
import androidx.compose.material3.*
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.navigation.NavigationConstants
import soly.lyricsgenerator.ui.navigation.navigateToTagEditor
import soly.lyricsgenerator.domain.model.FileWithSong
import soly.lyricsgenerator.ui.viewmodel.FilesUiState
import soly.lyricsgenerator.ui.viewmodel.FilesViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import soly.lyricsgenerator.domain.service.MusicPlayerService
import java.io.IOException
import soly.lyricsgenerator.ui.screens.components.CompactMusicPlayerController
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.domain.constants.FileConstants
import timber.log.Timber

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilesScreen(
    navController: NavController,
    viewModel: FilesViewModel,
    musicViewModel: MusicViewModel
) {
    val uiState by viewModel.uiState.collectAsState()
    var fileToDelete by remember { mutableStateOf<Long?>(null) }
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    var fileToSave by remember { mutableStateOf<FileWithSong?>(null) }
    val snackbarMessage by viewModel.snackbarMessage.collectAsState()

    // Observe MusicViewModel state for compact player
    val musicUiState by musicViewModel.getUIState().collectAsState()
    val showCompactPlayer = musicUiState.currentlyPlayingSong != null

    // Handle navigation events from the music player
    LaunchedEffect(musicViewModel.navigationEvent) {
        musicViewModel.navigationEvent.collect { route ->
            // If navigating to SongDetails, pass the current song ID and a flag
            if (route == NavRoutes.SongDetails) {
                musicViewModel.getUIState().value.currentlyPlayingSong?.let { song ->
                    navController.currentBackStackEntry?.savedStateHandle?.apply {
                        set(NavigationConstants.KEY_SONG_ID, song.id)
                        set(NavigationConstants.KEY_FROM_COMPACT_PLAYER, true)
                    }
                }
            }
            navController.navigate(route.route)
        }
    }
    
    // Log screen view analytics event
    LaunchedEffect(Unit) {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.FILES)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.FILES + "Screen.kt")
        }
    }

    val isRefreshing = uiState is FilesUiState.Loading
    val swipeRefreshState = rememberSwipeRefreshState(isRefreshing)
    

    // ActivityResultLauncher for picking an LRC file
    val pickLrcFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument(),
        onResult = { uri ->
            if (uri != null) {
                // Validate file type before processing
                val contentResolver = context.contentResolver
                val mimeType = contentResolver.getType(uri)
                val uriString = uri.toString().lowercase()
                
                val isValidMimeType = mimeType == FileConstants.MimeTypes.TEXT_PLAIN ||
                                     mimeType == FileConstants.MimeTypes.APPLICATION_LRC || 
                                     mimeType == FileConstants.MimeTypes.APPLICATION_RTF || 
                                     mimeType == FileConstants.MimeTypes.TEXT_RTF
                val isValidExtension = uriString.endsWith(FileConstants.Extensions.LRC) || 
                                      uriString.endsWith(FileConstants.Extensions.RTF) || 
                                      uriString.endsWith(FileConstants.Extensions.TXT)
                
                if (isValidMimeType || isValidExtension) {
                    
                    // Log analytics event for file selection
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_SELECTED) {
                        param(AnalyticsConstants.Params.FILE_URI, uri.toString())
                    }
                    
                    viewModel.onLrcFileSelected(uri)
                    
                    // Navigate to SongPickerScreen with linkingMode = true
                    navController.navigate("${NavRoutes.SongPickerScreen.route}?linkingMode=true")
                } else {
                    // Invalid file type selected

                    Toast.makeText(
                        context,
                        context.getString(R.string.please_select_valid_lyrics_file),
                        Toast.LENGTH_LONG
                    ).show()
                    
                    // Log analytics event for invalid file selection
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_SELECTION_CANCELLED) {
                        param(AnalyticsConstants.Params.REASON, AnalyticsConstants.Params.INVALID_FILE_TYPE)
                        param(AnalyticsConstants.Params.MIME_TYPE, mimeType ?: AnalyticsConstants.Params.UNKNOWN)
                        param("uri", uri.toString())
                    }
                }
            } else {
                // Log analytics event for file selection cancellation
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_SELECTION_CANCELLED) {}
                
                viewModel.onLrcFileSelected(null) // Notify ViewModel if selection was cancelled
            }
        }
    )
    
    // ActivityResultLauncher for saving the file
    val saveFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("*/*"),
        onResult = { uri ->
            uri?.let { targetUri ->
                fileToSave?.let { file ->
                    scope.launch {
                        val content = viewModel.getFileContent(file.file.id)
                        if (content != null) {
                            try {
                                context.contentResolver.openOutputStream(targetUri)?.use { outputStream ->
                                    outputStream.write(content.toByteArray())
                                }
                                // Log save event
                                viewModel.logFileSavedEvent(file.file.id, file.file.fileName)
                                // Optionally show a success message (e.g., Snackbar)
                            } catch (e: IOException) {
                                // Optionally show an error message
                            }
                        } else {
                            // Optionally show an error message
                        }
                        fileToSave = null // Reset after attempt
                    }
                }
            }
        }
    )
    
    // Log screen open event
    LaunchedEffect(Unit) {
        viewModel.logFilesScreenOpened()
    }

    // Get shared ViewModel from the Activity
    val activity = LocalContext.current as ComponentActivity
    val sharedViewModel: SharedViewModel = hiltViewModel(activity)

    // Collect songIdForLinking from SharedViewModel
    LaunchedEffect(Unit) {
        sharedViewModel.songIdForLinking.collect { songId ->
            if (songId != null) {
                viewModel.onSongSelectedForLinking(songId)
                sharedViewModel.setSongIdForLinking(null) // Reset after processing
            }
        }
    }

    // Show Toast when message is available
    LaunchedEffect(snackbarMessage) {
        snackbarMessage?.let { message ->
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
            viewModel.onSnackbarShown() // Notify ViewModel that message was shown
        }
    }

    // Listen for refresh events from SharedViewModel
    LaunchedEffect(Unit) {
        sharedViewModel.refreshFilesEvent.collect {
            viewModel.onRefreshEvent()
        }
    }

    // Listen for silent refresh events from SharedViewModel
    LaunchedEffect(Unit) {
        sharedViewModel.refreshFilesSilentlyEvent.collect {
            Timber.tag("DEBUG_FLOW").d("FilesScreen: Received refreshFilesSilently event, calling onSilentRefreshEvent()")
            viewModel.onSilentRefreshEvent()
        }
    }

    // Listen for import events from SharedViewModel
    LaunchedEffect(Unit) {
        sharedViewModel.importLrcFileEvent.collect {
            viewModel.logFabClickedEvent()
            pickLrcFileLauncher.launch(FileConstants.MimeTypes.SUPPORTED_LYRICS_MIME_TYPES)
        }
    }
    
    // Notify MusicPlayerService of entry to Files screen for playback continuity
    LaunchedEffect(Unit) {
        // Get the previous route to determine if we're coming from a tab that had music
        val previousRoute = navController.previousBackStackEntry?.destination?.route
        val isFromCreateScreen = previousRoute == NavRoutes.Create.route || previousRoute?.startsWith("create/") == true
        val syncInitialized = sharedViewModel.isSyncInitialized.value
        
        // Check if sync was initialized regardless of previous route detection issues
        if (syncInitialized) {
            viewModel.onSilentRefreshEvent()
        }
        
        // Always call MusicPlayerService for screen transition if coming from relevant screens
        if (previousRoute == NavRoutes.Music.route || isFromCreateScreen) {
            MusicPlayerService.startService(
                context = context,
                action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                sourceScreen = previousRoute,
                destinationScreen = NavRoutes.Files.route
            )
        }
    }
    
    // Confirmation dialog
    fileToDelete?.let { fileId ->
        AlertDialog(
            onDismissRequest = { fileToDelete = null },
            title = { Text(stringResource(R.string.delete)) },
            text = { Text(stringResource(R.string.delete_confirmation)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteFile(fileId)
                        fileToDelete = null
                    }
                ) {
                    Text(stringResource(R.string.yes))
                }
            },
            dismissButton = {
                TextButton(onClick = { fileToDelete = null }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    
    @Suppress("UnusedMaterial3ScaffoldPaddingParameter")
    Scaffold { _ ->
        SwipeRefresh(state = swipeRefreshState, onRefresh = { viewModel.onRefreshEvent() }) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(brush = AppGradientBrush)
            ) {

            when (val state = uiState) {
                is FilesUiState.Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                is FilesUiState.Empty -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_files),
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                is FilesUiState.Success -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(
                            start = 8.dp,
                            end = 8.dp,
                            top = 8.dp,
                            bottom = if (showCompactPlayer) 80.dp else 8.dp
                        )
                    ) {
                        items(state.filesWithSongs) { fileWithSong ->
                            FileItem(
                                fileWithSong = fileWithSong,
                                onItemClick = { 
                                    viewModel.logFileItemClicked(fileWithSong.file.id, fileWithSong.file.fileName) // Log click using fileWithSong
                                    navigateToSongDetails(navController, fileWithSong) // Pass fileWithSong
                                },
                                onDeleteClick = { fileId -> // This lambda now matches FileItem's expectation
                                    // We still log using the fileWithSong from the outer scope
                                    viewModel.logDeleteOptionClicked(fileWithSong.file.id, fileWithSong.file.fileName)
                                    fileToDelete = fileId // Use the fileId passed to the lambda
                                },
                                onSaveClick = { fileWithSongParam ->
                                    viewModel.logSaveOptionClicked(fileWithSongParam.file.id, fileWithSongParam.file.fileName) // Log click
                                    fileToSave = fileWithSongParam // Set the file to be saved
                                    saveFileLauncher.launch(fileWithSongParam.file.fileName) // Launch SAF with suggested filename
                                },
                                onSyncClick = { fileWithSongParam ->
                                    // Navigate to Create screen with sync parameters
                                    navController.navigate("create/${fileWithSongParam.song.id}/${fileWithSongParam.file.id}")
                                },
                                onTagEditorClick = { fileWithSongParam ->
                                    // Navigate to tag editor with the song data
                                    navController.navigateToTagEditor(fileWithSongParam.song)
                                }
                            )
                        }
                    }
                }
                
                is FilesUiState.Error -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = state.message,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Button(onClick = { 
                            viewModel.logRetryLoadFilesClicked() // Log click
                            viewModel.loadFiles() 
                        }) {
                            Text(stringResource(R.string.retry))
                        }
                    }
                }
            }
            // CompactMusicPlayerController at the bottom
            if (showCompactPlayer) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                ) {
                    CompactMusicPlayerController(
                        currentSong = musicUiState.currentlyPlayingSong,
                        currentPosition = musicUiState.currentPosition,
                        isPlaying = musicUiState.isPlaying,
                        isVisible = true,
                        onPlayPauseClicked = {
                            if (musicUiState.isPlaying) {
                                musicViewModel.pausePlaying()
                            } else {
                                musicViewModel.resumePlaying()
                            }
                        },
                        onNextClicked = {
                            musicViewModel.playNextSong()
                        },
                        onPreviousClicked = {
                            musicViewModel.playPreviousSong()
                        },
                        onPlayerClicked = {
                            musicViewModel.onInfoClicked()
                        }
                    )
                }
            }


    }

}
}
}



private fun navigateToSongDetails(navController: NavController, fileWithSong: FileWithSong) {
    // Navigate to the song details screen with the song ID
    navController.currentBackStackEntry?.savedStateHandle?.set(NavigationConstants.KEY_SONG_ID, fileWithSong.song.id)
    navController.navigate(NavRoutes.SongDetails.route)
}

