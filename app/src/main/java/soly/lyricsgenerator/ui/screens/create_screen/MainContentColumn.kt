package soly.lyricsgenerator.ui.screens.create_screen

import android.content.Context
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import timber.log.Timber

@Composable
fun MainContentColumn(
    context: Context,
    songViewModel: CreateViewModel,
    sharedViewModel: SharedViewModel? = null,
    showStartButtonState: Boolean,
    showPreview: Boolean,
    currentPosition: Long,
    musicControllerHeightPx: Int,
    songDuration: Long,
    onChangeSong: (() -> Unit)? = null,
    onUndoClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(bottom = 8.dp)
    ) {
        // Use isPostStartScreen from SharedViewModel if available, otherwise fallback to showStepsIndicator
        val isPostStartScreen = if (sharedViewModel != null) {
            sharedViewModel.isPostStartScreen.value
        } else {
            !songViewModel.showStepsIndicator.collectAsState().value
        }

        // Show TopAppBar when in Post-Start screen mode
        if (isPostStartScreen) {
            Timber.tag("DEBUG_FLOW").d("MainContentColumn: Showing TopAppBar because in Post-Start screen mode")
            TopAppBarCreateScreen(
                context = context,
                createViewModel = songViewModel,
                onChangeSong = onChangeSong,
                onUndoClicked = onUndoClicked
            )

            // Always show LyricsContent when in Post-Start screen mode
            Timber.tag("DEBUG_FLOW").d("MainContentColumn: Showing LyricsContent because in Post-Start screen mode")
            LyricsContent(
                context = context,
                showPreview = showPreview,
                songViewModel = songViewModel,
                currentPosition = currentPosition,
                musicControllerHeightPx = musicControllerHeightPx,
                songDuration = songDuration
            )
        } else if (!showStartButtonState) {
            // This is the original condition - show lyrics when start button is hidden
            // but not in Post-Start screen mode (should not happen often)
            Timber.tag("DEBUG_FLOW").d("MainContentColumn: Showing LyricsContent because start button is hidden")
            LyricsContent(
                context = context,
                showPreview = showPreview,
                songViewModel = songViewModel,
                currentPosition = currentPosition,
                musicControllerHeightPx = musicControllerHeightPx,
                songDuration = songDuration
            )
        }
    }
}