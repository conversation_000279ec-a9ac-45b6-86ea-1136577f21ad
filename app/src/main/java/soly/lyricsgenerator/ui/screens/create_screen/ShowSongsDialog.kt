package soly.lyricsgenerator.ui.screens.create_screen

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.res.stringResource
import kotlinx.coroutines.flow.MutableStateFlow
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.R

@Composable
fun ShowSongsDialog(
    context: Context,
    songs: List<Song>,
    showDialog: MutableStateFlow<Boolean>,
    songViewModel: CreateViewModel
) {
    if (songs.isNotEmpty()) {
        AlertDialog(
            onDismissRequest = {},
            title = {
                Text(text = stringResource(R.string.song_list))
            },
            text = {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp)
                ) {
                    items(songs) { song ->
                        Text(
                            text = song.title,
                            fontSize = 18.sp,
                            modifier = Modifier.clickable {
                                songViewModel.selectedSong.value = song
                                showDialog.value = false
                            }
                        )
                    }
                }
            },
            confirmButton = {
                Button(onClick = { showDialog.value = false }) {
                    Text(stringResource(R.string.close))
                }
            }
        )
    }
}