package soly.lyricsgenerator.ui.screens.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.QuestionAnswer
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.FaqItem

/**
 * Modern FAQ component with animations and collapsible cards
 * Following project's architecture with sealed classes and localized strings
 */
@Composable
fun FaqSection(
    faqItems: List<FaqItem>,
    onFaqItemExpanded: (String) -> Unit,
    onFaqItemCollapsed: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var isSectionExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // FAQ Header - Clickable to expand/collapse
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        isSectionExpanded = !isSectionExpanded
                    }
                    .padding(16.dp)
                    .semantics {
                        contentDescription = if (isSectionExpanded) {
                            "Collapse FAQ section"
                        } else {
                            "Expand FAQ section"
                        }
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.faq_section_title),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White
                )
                
                Icon(
                    imageVector = if (isSectionExpanded) {
                        Icons.Default.ExpandMore // Down arrow (v)
                    } else {
                        Icons.Default.ExpandLess // Right arrow (>)
                    },
                    contentDescription = if (isSectionExpanded) {
                        stringResource(R.string.faq_collapse_content_desc)
                    } else {
                        stringResource(R.string.faq_expand_content_desc)
                    },
                    tint = Color.White,
                    modifier = Modifier
                        .size(20.dp)
                        .rotate(if (isSectionExpanded) 0f else 90f)
                )
            }

            // FAQ Items with Animation
            AnimatedVisibility(
                visible = isSectionExpanded,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(
                    animationSpec = tween(durationMillis = 300)
                ),
                exit = shrinkVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeOut(
                    animationSpec = tween(durationMillis = 300)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp, bottom = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    faqItems.forEach { faqItem ->
                        FaqItemCard(
                            faqItem = faqItem,
                            onExpanded = { onFaqItemExpanded(faqItem.id) },
                            onCollapsed = { onFaqItemCollapsed(faqItem.id) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun FaqItemCard(
    faqItem: FaqItem,
    onExpanded: () -> Unit,
    onCollapsed: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.08f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // Question Row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        isExpanded = !isExpanded
                        if (isExpanded) {
                            onExpanded()
                        } else {
                            onCollapsed()
                        }
                    }
                    .padding(16.dp)
                    .semantics {
                        contentDescription = if (isExpanded) {
                            "Collapse FAQ answer"
                        } else {
                            "Expand FAQ answer"
                        }
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = faqItem.icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = faqItem.getQuestion().invoke(),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White,
                    modifier = Modifier.weight(1f)
                )
                
                IconButton(
                    onClick = {
                        isExpanded = !isExpanded
                        if (isExpanded) {
                            onExpanded()
                        } else {
                            onCollapsed()
                        }
                    },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = if (isExpanded) {
                            Icons.Default.ExpandMore // Down arrow (v)
                        } else {
                            Icons.Default.ExpandLess // Right arrow (>)
                        },
                        contentDescription = if (isExpanded) {
                            stringResource(R.string.faq_collapse_content_desc)
                        } else {
                            stringResource(R.string.faq_expand_content_desc)
                        },
                        tint = Color.White.copy(alpha = 0.8f),
                        modifier = Modifier
                            .size(20.dp)
                            .rotate(if (isExpanded) 0f else 90f)
                    )
                }
            }
            
            // Answer Section with Animation
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = tween(durationMillis = 300)
                ) + fadeIn(
                    animationSpec = tween(durationMillis = 300)
                ),
                exit = shrinkVertically(
                    animationSpec = tween(durationMillis = 300)
                ) + fadeOut(
                    animationSpec = tween(durationMillis = 300)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 48.dp, end = 16.dp, bottom = 16.dp)
                ) {
                    Text(
                        text = faqItem.getAnswer().invoke(),
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.9f),
                        lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
                    )
                }
            }
        }
    }
}