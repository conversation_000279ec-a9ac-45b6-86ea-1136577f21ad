package soly.lyricsgenerator.ui.screens.create_screen

import android.annotation.SuppressLint
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@SuppressLint("RestrictedApi")
@Composable
fun StepIndicator(
    stepNumber: Int,
    isCompleted: <PERSON><PERSON><PERSON>,
    buttonHalfHeight: Dp,
    modifier: Modifier = Modifier,
    drawUpperLineAnimated: Boolean = false,
    upperLineProgress: Animatable<Float, AnimationVector1D>,
    lowerLineProgress: Animatable<Float, AnimationVector1D>
) {
    val smileyAlpha = remember { Animatable(0f) }

    LaunchedEffect(isCompleted, drawUpperLineAnimated) {
        if (isCompleted) {
            smileyAlpha.animateTo(
                targetValue = 1f,
                animationSpec = androidx.compose.animation.core.tween(500)
            )
        }
    }

    Box(
        modifier = modifier
            .width(24.dp)
            .height(88.dp)
            .padding(top = (buttonHalfHeight - (if (isCompleted && stepNumber == 3) 6.dp else 0.dp)).coerceAtLeast(0.dp)),
    ) {
        StepLines(
            stepNumber = stepNumber,
            center = with(LocalDensity.current) { Offset(12.dp.toPx(), 44.dp.toPx()) },
            isCompleted = isCompleted,
            upperLineProgress = upperLineProgress,
            lowerLineProgress = lowerLineProgress,
        )

        StepCircleIndicator(
            isCompleted = isCompleted,
            stepNumber = stepNumber,
            smileyAlpha = smileyAlpha,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@SuppressLint("RestrictedApi")
@Composable
private fun StepCircleIndicator(
    isCompleted: Boolean,
    stepNumber: Int,
    smileyAlpha: Animatable<Float, AnimationVector1D>,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        if (isCompleted && stepNumber != 3) {
            CompletedStepIndicator()
        } else {
            Canvas(modifier = Modifier.size(if (isCompleted) 36.dp else 24.dp)) {
                if (isCompleted) {
                    drawSmileyFace(center, smileyAlpha)
                } else {
                    drawStepCircle(center, stepNumber)
                }
            }
        }
    }
}

@SuppressLint("RestrictedApi")
private fun DrawScope.drawStepCircle(
    center: Offset,
    stepNumber: Int
) {
    drawCircle(
        color = Color.Black,
        radius = 12.dp.toPx()
    )

    // Draw circle with thicker stroke
    drawCircle(
        color = Color.Gray,
        radius = 12.dp.toPx(),
        style = Stroke(width = 3.dp.toPx())
    )

    // Draw number inside circle
    drawContext.canvas.nativeCanvas.apply {
        val textPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.GRAY
            textSize = 12.dp.toPx()
            textAlign = android.graphics.Paint.Align.CENTER
        }
        val textY = center.y - (textPaint.descent() + textPaint.ascent()) / 2
        drawText(
            stepNumber.toString(),
            center.x,
            textY,
            textPaint
        )
    }
}

private fun DrawScope.drawSmileyFace(
    center: Offset,
    smileyAlpha: Animatable<Float, AnimationVector1D>
) {
    // Draw smiley face with animation
    drawCircle(
        color = Color.Yellow,
        radius = 18.dp.toPx(),
        alpha = smileyAlpha.value
    )

    // Draw eyes and mouth with animation
    val eyeRadius = 3.dp.toPx()
    val eyeOffsetX = 6.dp.toPx()
    val eyeOffsetY = 6.dp.toPx()
    val mouthRadius = 8.dp.toPx()
    val mouthOffsetY = 10.dp.toPx()

    drawCircle(
        color = Color.Black,
        radius = eyeRadius,
        center = Offset(center.x - eyeOffsetX, center.y - eyeOffsetY),
        alpha = smileyAlpha.value
    )
    drawCircle(
        color = Color.Black,
        radius = eyeRadius,
        center = Offset(center.x + eyeOffsetX, center.y - eyeOffsetY),
        alpha = smileyAlpha.value
    )

    drawArc(
        color = Color.Black,
        startAngle = 0f,
        sweepAngle = 180f,
        useCenter = false,
        topLeft = Offset(center.x - mouthRadius, center.y),
        size = Size(mouthRadius * 2, mouthOffsetY),
        alpha = smileyAlpha.value
    )
} 