package soly.lyricsgenerator.ui.screens.song_details_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Shuffle
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import soly.lyricsgenerator.ui.navigation.NavigationConstants
import soly.lyricsgenerator.domain.service.MusicPlayerService
import androidx.compose.ui.res.stringResource
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import soly.lyricsgenerator.ui.screens.components.BottomSheetFileChooser
import soly.lyricsgenerator.ui.screens.components.FileContentRenderer
import soly.lyricsgenerator.ui.screens.components.LyricsDisplay
import soly.lyricsgenerator.ui.screens.components.createLRCFilePickerLauncher
import soly.lyricsgenerator.ui.screens.components.createRenderer
import soly.lyricsgenerator.ui.screens.song_details_screen.components.TopAppBarSongDetails
import soly.lyricsgenerator.ui.viewmodel.LyricsViewModel
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import soly.lyricsgenerator.utils.LrcToSrtConverter
// import soly.lyricsgenerator.utils.VideoExporter
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import soly.lyricsgenerator.ui.screens.components.MusicPlayerControllerNew
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.features.overlay.data.services.OverlayService
import soly.lyricsgenerator.features.overlay.presentation.components.OverlayPermissionDialog
import timber.log.Timber
import android.media.MediaScannerConnection
import androidx.activity.ComponentActivity

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SongDetailsScreen(navController: NavHostController) {
    val musicViewModel: MusicViewModel = hiltViewModel()
    val songDetailsViewModel: SongDetailsViewModel = hiltViewModel()
    val uiState by musicViewModel.getUIState().collectAsState()
    
    // Get SharedViewModel from Activity for cross-screen communication
    val activity = LocalContext.current as ComponentActivity
    val sharedViewModel: SharedViewModel = hiltViewModel(activity)
    val bottomSheetState = rememberModalBottomSheetState()
    val coroutineScope = rememberCoroutineScope()
    val lrcLines by musicViewModel.lrcLines.collectAsState()
    val fileContentState by musicViewModel.fileContentState.collectAsState()
    val favoriteState by musicViewModel.favoriteState.collectAsState()
    
    // Create persistent state to cache the received lyrics
    val (cachedLines, setCachedLines) = remember { mutableStateOf<Map<Int, String>>(emptyMap()) }
    // Track which song ID the cached lyrics belong to
    val (cachedSongId, setCachedSongId) = remember { mutableStateOf<Long?>(null) }

    // Flag to track if we've successfully handled the song
    var songHandled by remember { mutableStateOf(false) }
    
    // Track if we're navigating to TagEditor to hide TopAppBar immediately
    var isNavigatingToTagEditor by remember { mutableStateOf(false) }
    
    // Add state for showing export status
    val snackbarHostState = remember { SnackbarHostState() }
    var isExporting by remember { mutableStateOf(false) }
    
    // Debouncing for favorite button
    var lastFavoriteClickTime by remember { mutableStateOf(0L) }
    
    // Overlay permission dialog state
    var showOverlayPermissionDialog by remember { mutableStateOf(false) }

    // Get the song_id and navigation source from the saved state handle
    val songId = navController.previousBackStackEntry?.savedStateHandle?.get<Long>(NavigationConstants.KEY_SONG_ID)
    val fromCompactPlayer = navController.previousBackStackEntry?.savedStateHandle?.get<Boolean>(NavigationConstants.KEY_FROM_COMPACT_PLAYER) ?: false

    val appContext = LocalContext.current

    val isShuffleEnabled by musicViewModel.shuffleEnabled.collectAsState()
    
    // Define permissions needed
    val permissionsToRequest = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.POST_NOTIFICATIONS
        )
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
    } else {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }
    
    // For requesting permissions
    val requestPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: All permissions granted, proceeding with export")
            // Proceed with export if all permissions granted
            // performVideoExport(uiState.currentlyPlayingSong, lrcLines, appContext, snackbarHostState, coroutineScope, isExporting) { isExporting = it }
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PERMISSION_RESULT) {
                param(AnalyticsConstants.Params.PERMISSION_TYPE, AnalyticsConstants.Params.MEDIA_EXPORT)
                param(AnalyticsConstants.Params.RESULT, AnalyticsConstants.Params.GRANTED)
            }
        } else {
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Some permissions denied")
            coroutineScope.launch {
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PERMISSION_RESULT) {
                    param(AnalyticsConstants.Params.PERMISSION_TYPE, AnalyticsConstants.Params.MEDIA_EXPORT)
                    param(AnalyticsConstants.Params.RESULT, AnalyticsConstants.Params.DENIED)
                }
                snackbarHostState.showSnackbar(appContext.getString(R.string.storage_permissions_required))
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SNACKBAR_SHOWN) {
                    param(AnalyticsConstants.Params.MESSAGE, AnalyticsConstants.ErrorMessages.STORAGE_PERMISSIONS_REQUIRED)
                }
            }
        }
    }
    
    // For requesting MANAGE_EXTERNAL_STORAGE permission (Android 11+)
    val manageStorageLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()) {
                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: MANAGE_EXTERNAL_STORAGE granted")
                // performVideoExport(uiState.currentlyPlayingSong, lrcLines, appContext, snackbarHostState, coroutineScope, isExporting) { isExporting = it }
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PERMISSION_RESULT) {
                    param(AnalyticsConstants.Params.PERMISSION_TYPE, AnalyticsConstants.Params.MANAGE_EXTERNAL_STORAGE)
                    param(AnalyticsConstants.Params.RESULT, AnalyticsConstants.Params.GRANTED)
                }
            } else {
                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: MANAGE_EXTERNAL_STORAGE denied")
                coroutineScope.launch {
                    snackbarHostState.showSnackbar(appContext.getString(R.string.storage_permissions_required))
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SNACKBAR_SHOWN) {
                        param(AnalyticsConstants.Params.MESSAGE, AnalyticsConstants.ErrorMessages.STORAGE_PERMISSIONS_REQUIRED)
                    }
                }
            }
        }
    }

    fun toggleShuffle() {
        musicViewModel.toggleShuffle()
    }
    
    // Function to handle video export with permission checks
    fun exportVideoWithLyrics() {
        // Check if we have song and lyrics
        val currentSong = uiState.currentlyPlayingSong
        
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_EXPORT_VIDEO_CLICK) { // Log export click
            param(AnalyticsConstants.Params.SONG_ID, currentSong?.id.toString())
            param(AnalyticsConstants.Params.SONG_TITLE, currentSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
            param(AnalyticsConstants.Params.HAS_LYRICS, lrcLines.isNotEmpty().toString())
        }
        
        if (currentSong == null) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar(appContext.getString(R.string.no_song_currently_playing))
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SNACKBAR_SHOWN) {
                    param(AnalyticsConstants.Params.MESSAGE, AnalyticsConstants.ErrorMessages.NO_SONG_PLAYING)
                }
            }
            return
        }
        
        if (lrcLines.isEmpty()) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar(appContext.getString(R.string.no_lyrics_available_for_song))
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SNACKBAR_SHOWN) {
                    param(AnalyticsConstants.Params.MESSAGE, AnalyticsConstants.ErrorMessages.NO_LYRICS_AVAILABLE)
                }
            }
            return
        }
        
        // Check and request permissions before proceeding
        when {
            // For Android 11 and above, ideally request MANAGE_EXTERNAL_STORAGE
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && Environment.isExternalStorageManager() -> {
                // performVideoExport(currentSong, lrcLines, appContext, snackbarHostState, coroutineScope, isExporting) { isExporting = it }
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // For Android 11+, request MANAGE_EXTERNAL_STORAGE (requires special intent)
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                        data = Uri.parse("package:${appContext.packageName}")
                    }
                    manageStorageLauncher.launch(intent)
                } catch (e: Exception) {
                    // If the specific intent fails, try the general storage settings
                    val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                    manageStorageLauncher.launch(intent)
                }
            }
            // For Android 10 and below, check regular permissions
            permissionsToRequest.all { permission ->
                ContextCompat.checkSelfPermission(appContext, permission) == PackageManager.PERMISSION_GRANTED
            } -> {
                // performVideoExport(currentSong, lrcLines, appContext, snackbarHostState, coroutineScope, isExporting) { isExporting = it }
            }
            else -> {
                // Request regular permissions
                requestPermissionLauncher.launch(permissionsToRequest)
            }
        }
    }

    val fileLRCPickerLauncher = createLRCFilePickerLauncher(
        context = appContext,
        coroutineScope = coroutineScope,
        bottomSheetState = bottomSheetState,
        songFileName = uiState.currentlyPlayingSong?.data?.substringAfterLast('/'),
        onLrcLinesParsed = { parsedLines, fileName, filePath ->
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: onLrcLinesParsed callback triggered")
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Parsed lines count: ${parsedLines.size}, fileName: $fileName, filePath: $filePath")
            
            // Log some sample lines
            parsedLines.entries.take(3).sortedBy { it.key }.forEach { (timeMs, line) ->
                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Sample line - Time: ${timeMs}ms, Text: \"$line\"")
            }
            
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Calling musicViewModel.updateFileContentWithLrcLines()")
            musicViewModel.updateFileContentWithLrcLines(parsedLines)
            
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Calling musicViewModel.saveFile()")
            musicViewModel.saveFile(
                filePath = filePath,
                fileName = fileName
            )
            
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Triggering files list refresh")
            sharedViewModel.refreshFilesSilently()
            
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: LRC file import completed successfully")
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_LRC_FILE_SAVED) { // Log LRC save
                param(AnalyticsConstants.Params.FILE_NAME, fileName)
                param(AnalyticsConstants.Params.LINE_COUNT, parsedLines.size.toLong())
            }
        }
    )

    // Cache lyrics when they change
    LaunchedEffect(lrcLines, uiState.currentlyPlayingSong) {
        if (lrcLines.isNotEmpty() && uiState.currentlyPlayingSong != null) {
            // Cache the non-empty lyrics lines along with the song ID
            setCachedLines(lrcLines)
            setCachedSongId(uiState.currentlyPlayingSong?.id)
        }
    }
    
    // Refresh song data when returning from TagEditor
    LaunchedEffect(navController.currentBackStackEntry?.destination?.route) {
        val currentRoute = navController.currentBackStackEntry?.destination?.route
        val previousRoute = navController.previousBackStackEntry?.destination?.route
        
        // If we're on SongDetails and came from TagEditor, refresh song data
        if (currentRoute == "song_details" && previousRoute?.startsWith("tag_editor") == true) {
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Returned from TagEditor, refreshing song data")
            
            // Get current song to scan its file for MediaStore updates
            uiState.currentlyPlayingSong?.let { song ->
                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Scanning file to update MediaStore: ${song.data}")
                
                MediaScannerConnection.scanFile(
                    appContext,
                    arrayOf(song.data),
                    null
                ) { path, uri ->
                    Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: MediaStore scan completed for: $path, URI: $uri")
                    
                    // After MediaStore scan completes, trigger song reload
                    MusicPlayerService.startService(
                        appContext,
                        MusicPlayerService.ACTION_LOAD_SONGS
                    )
                }
            } ?: run {
                // Fallback - just reload songs without scan
                MusicPlayerService.startService(
                    appContext,
                    MusicPlayerService.ACTION_LOAD_SONGS
                )
                // Give a small delay to ensure the service processes the reload
                delay(100)
            }
        }
    }

    LaunchedEffect(Unit) {
        // Make sure we load songs if they're not already loaded
        musicViewModel.loadSongsIfEmpty()
        
        // Function to find and play song with retry mechanism
        suspend fun findAndPlaySong(maxRetries: Int = 5) {
            var retryCount = 0
            
            while (!songHandled && retryCount < maxRetries) {
                // If we have a song ID from navigation, find and play that song
                songId?.let { id ->
                    // Find the song from available songs
                    val songs = uiState.songs
                    
                    if (songs.isNotEmpty()) {
                        val songToPlay = songs.find { it.id == id }
                        
                        // Play the song if found
                        songToPlay?.let { song ->
                            // Check if this song is already playing or if we came from compact player
                            if (uiState.currentlyPlayingSong?.id == song.id || fromCompactPlayer) {
                                // Just load content without restarting playback
                                musicViewModel.loadFileContent(id)
                            } else {
                                musicViewModel.playSong(song)
                                musicViewModel.loadFileContent(id)
                            }
                            
                            songHandled = true
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SONG_PLAY_SUCCESS) {
                                param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                                param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                                param(AnalyticsConstants.Params.SOURCE, if (songId != null) AnalyticsConstants.Params.NAVIGATION else AnalyticsConstants.Params.CURRENT_PLAYING)
                            }
                            
                            // Clear the compact player flag AFTER we've used it for playback decision
                            if (fromCompactPlayer) {
                                navController.previousBackStackEntry?.savedStateHandle?.remove<Boolean>(NavigationConstants.KEY_FROM_COMPACT_PLAYER)
                            }
                            
                            return
                        }
                    } else {
                        musicViewModel.loadSongsIfEmpty()
                    }
                } ?: run {
                    // Otherwise, use the currently playing song (if any)
                    uiState.currentlyPlayingSong?.let { song ->
                        musicViewModel.loadFileContent(song.id)
                        
                        // Don't change playback state - preserve current playing/paused state
                        // This maintains the exact state from when user navigated from compact player
                        
                        songHandled = true
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SONG_PLAY_SUCCESS) {
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                            param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.CURRENT_PLAYING_PRESERVE_STATE)
                        }
                        
                        // Clear the compact player flag AFTER we've used it for playback decision
                        if (fromCompactPlayer) {
                            navController.previousBackStackEntry?.savedStateHandle?.remove<Boolean>(NavigationConstants.KEY_FROM_COMPACT_PLAYER)
                        }
                        
                        return
                    }
                }
                
                // Wait before retry
                retryCount++
                if (!songHandled && retryCount < maxRetries) {
                    // Increase delay for each retry
                    delay(500L * retryCount)
                }
            }
            
            if (!songHandled) {
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SONG_PLAY_FAILURE) {
                    param(AnalyticsConstants.Params.SONG_ID, songId.toString())
                    param(AnalyticsConstants.Params.REASON, AnalyticsConstants.Params.NOT_FOUND_OR_TIMEOUT)
                    param(AnalyticsConstants.Params.RETRIES, maxRetries.toLong())
                }
            }
        }
        
        // Start the find and play process
        findAndPlaySong()
    }

    // Log screen view
    LaunchedEffect(Unit) {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.SONG_DETAILS)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.SONG_DETAILS_SCREEN)
        }
    }

    // Load favorite status when song changes
    LaunchedEffect(uiState.currentlyPlayingSong?.id) {
        uiState.currentlyPlayingSong?.let { song ->
            musicViewModel.loadFavoriteStatus(song.id)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = AppGradientBrush)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Only show TopAppBar if we're not navigating to TagEditor
            if (!isNavigatingToTagEditor) {
                TopAppBarSongDetails(
                    navController = navController,
                    bottomSheetState = bottomSheetState,
                    coroutineScope = coroutineScope,
                    isExporting = isExporting,
                    hasLyrics = uiState.currentlyPlayingSong != null && !fileContentState.isEmpty(),
                    currentSong = uiState.currentlyPlayingSong,
                    onNavigatingToTagEditor = { isNavigatingToTagEditor = true },
                    onFloatingLyricsClick = {
                        // Handle floating lyrics click with permission check
                        uiState.currentlyPlayingSong?.let { song ->
                            // Check if overlay permission is granted
                            val permissionResult = songDetailsViewModel.checkOverlayPermission()
                            
                            when (permissionResult) {
                                is soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionResult.Granted -> {
                                    // Permission granted, proceed with overlay
                                    // Start overlay service
                                    OverlayService.showOverlay(appContext)
                                    
                                    // Log analytics
                                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_FLOATING_LYRICS_CLICK) {
                                        param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                                        param(AnalyticsConstants.Params.SONG_TITLE, song.title)
                                    }
                                }
                                is soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionResult.Denied -> {
                                    // Permission not granted, show dialog
                                    showOverlayPermissionDialog = true
                                }
                            }
                        }
                    }
                )
            }

            // Only use cached lines if they're for the current song
            // and we somehow lost the lyrics data temporarily 
            val currentSongId = uiState.currentlyPlayingSong?.id
            val displayLines = if (lrcLines.isEmpty() && cachedLines.isNotEmpty() && 
                currentSongId != null && currentSongId == cachedSongId) {
                cachedLines
            } else {
                // If the song has changed or we truly don't have lyrics, use what's in lrcLines
                lrcLines
            }
            
            // Create renderer based on file content state using visitor pattern polymorphic dispatch
            val contentRenderer = fileContentState.createRenderer()
            val lyricsViewModel: LyricsViewModel = hiltViewModel()
            
            // Render the appropriate content (LRC or TXT)
            contentRenderer.render(
                modifier = Modifier.weight(1f),
                currentTimeMs = uiState.currentPosition.toInt(),
                lyricsViewModel = lyricsViewModel
            ).invoke()

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                MusicPlayerControllerNew(
                    currentSong = uiState.currentlyPlayingSong,
                    currentPosition = uiState.currentPosition,
                    isPlaying = uiState.isPlaying,
                    onSeekTo = { position -> 
                        musicViewModel.seekTo(position)
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_SEEK_SONG) { // Log seek
                            param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                            param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                            param(AnalyticsConstants.Params.SEEK_POSITION_MS, position)
                        }
                    },
                    onPlayPauseClicked = {
                        if (uiState.isPlaying) {
                            musicViewModel.pausePlaying()
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PAUSE_SONG) { // Log pause
                                param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                                param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                            }
                        } else {
                            if (musicViewModel.isStopped.value && uiState.currentlyPlayingSong != null) {
                                uiState.currentlyPlayingSong?.let { song ->
                                    musicViewModel.playSong(song)
                                }
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PLAY_SONG) { // Log play from stop
                                    param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                                    param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Params.DETAILS_CONTROLLER)
                                }
                            } else {
                                musicViewModel.resumePlaying()
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_RESUME_SONG) { // Log resume
                                    param(AnalyticsConstants.Params.SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                                    param(AnalyticsConstants.Params.SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                                }
                            }
                        }
                    },
                    onNextClicked = { 
                        musicViewModel.playNextSong()
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_NEXT_SONG_CLICK) { // Log next
                            param(AnalyticsConstants.Params.CURRENT_SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                            param(AnalyticsConstants.Params.CURRENT_SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                        }
                    },
                    onPreviousClicked = { 
                        musicViewModel.playPreviousSong()
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_PREVIOUS_SONG_CLICK) { // Log previous
                            param(AnalyticsConstants.Params.CURRENT_SONG_ID, uiState.currentlyPlayingSong?.id.toString())
                            param(AnalyticsConstants.Params.CURRENT_SONG_TITLE, uiState.currentlyPlayingSong?.title ?: AnalyticsConstants.Params.UNKNOWN)
                        }
                    },
                    currentLyricLine = null, // Pass null as we don't have/need the specific line here
                    displayTitleInsteadOfLyrics = true, // Tell the controller to display the title
                    showShuffleButton = true, // Show the shuffle button
                    isShuffleEnabled = isShuffleEnabled, // Pass the shuffle state
                    onShuffleClicked = { 
                        toggleShuffle() 
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.DETAILS_TOGGLE_SHUFFLE_CLICK) { // Log shuffle toggle
                            param(AnalyticsConstants.Params.SHUFFLE_ENABLED, (!isShuffleEnabled).toString())
                        }
                    }, // Pass the toggle function
                    showFavoriteButton = true, // Show the favorite button
                    favoriteState = favoriteState, // Pass the favorite state
                    onFavoriteClicked = {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastFavoriteClickTime > 500) { // 500ms debounce
                            lastFavoriteClickTime = currentTime
                            uiState.currentlyPlayingSong?.let { song ->
                                musicViewModel.toggleFavorite(song.id)
                            }
                        }
                    },
                    modifier = Modifier.fillMaxWidth() // Controller takes full width now
                )
            }
        }

        // Show SnackBar for export status
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )

        if (bottomSheetState.isVisible) {
            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Bottom sheet is visible, rendering BottomSheetFileChooser")
            BottomSheetFileChooser(
                bottomSheetState = bottomSheetState,
                coroutineScope = coroutineScope,
                filePickerLauncher = fileLRCPickerLauncher
            )
        }
        
        // Overlay permission dialog
        if (showOverlayPermissionDialog) {
            OverlayPermissionDialog(
                onPermissionGranted = {
                    showOverlayPermissionDialog = false
                    // Permission granted, try again
                    uiState.currentlyPlayingSong?.let { song ->
                        OverlayService.showOverlay(appContext)
                    }
                },
                onPermissionDenied = {
                    showOverlayPermissionDialog = false
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar(appContext.getString(R.string.overlay_permission_denied))
                    }
                },
                onDismissRequest = {
                    showOverlayPermissionDialog = false
                }
            )
        }
    }
}

// Helper function to perform video export (moved outside the Composable to avoid duplicates)
//private fun performVideoExport(
//    currentSong: soly.lyricsgenerator.domain.model.Song?,
//    lrcLines: Map<Int, String>,
//    context: android.content.Context,
//    snackbarHostState: SnackbarHostState,
//    coroutineScope: kotlinx.coroutines.CoroutineScope,
//    isExportingCurrent: Boolean,
//    setExporting: (Boolean) -> Unit
//) {
//    if (currentSong == null || isExportingCurrent) return
//    
//    setExporting(true)
//    
//    coroutineScope.launch {
//        try {
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Starting video export for ${currentSong.title}")
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Song URI: ${Uri.parse(currentSong.data)}")
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: LRC lines count: ${lrcLines.size}")
//            
//            // Log a few of the LRC lines
//            lrcLines.entries.take(5).sortedBy { it.key }.forEach { (timeMs, line) ->
//                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: LRC sample - Time: ${timeMs}ms, Text: \"$line\"")
//            }
//            
//            // Convert LRC to SRT
//            val lrcToSrtConverter = LrcToSrtConverter()
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Converting LRC to SRT format")
//            val srtContent = lrcToSrtConverter.convertLrcToSrt(lrcLines)
//            
//            // Log SRT conversion result
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: SRT conversion complete, content length: ${srtContent.length}")
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: SRT content sample: ${srtContent.take(200)}")
//            
//            if (srtContent.isBlank()) {
//                Timber.tag("DEBUG_FLOW").e("SongDetailsScreen: Generated SRT content is empty/blank")
//                snackbarHostState.showSnackbar("Cannot create video: generated subtitle content is empty")
//                setExporting(false)
//                return@launch
//            }
//            
//            // Use VideoExporter to handle the FFmpeg operations
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Creating VideoExporter instance")
//            val videoExporter = VideoExporter(context)
//            
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Calling exportVideoWithLyrics")
//            val result = videoExporter.exportVideoWithLyrics(
//                songUri = Uri.parse(currentSong.data),
//                songTitle = currentSong.title,
//                srtContent = srtContent
//            )
//            
//            Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Video export result: $result")
//            
//            if (result.isSuccess) {
//                val outputPath = result.getOrNull()
//                Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Video exported successfully to $outputPath")
//                
//                // Check if the output file exists and has a reasonable size
//                outputPath?.let { path ->
//                    val outputFile = java.io.File(path)
//                    if (outputFile.exists()) {
//                        Timber.tag("DEBUG_FLOW").d("SongDetailsScreen: Output file exists, size: ${outputFile.length()} bytes")
//                    } else {
//                        Timber.tag("DEBUG_FLOW").e("SongDetailsScreen: Output file does not exist at $path")
//                    }
//                }
//                
//                snackbarHostState.showSnackbar("Video exported successfully to ${result.getOrNull()}")
//            } else {
//                val exception = result.exceptionOrNull()
//                Timber.tag("DEBUG_FLOW").e(exception, "SongDetailsScreen: Export failed with exception")
//                snackbarHostState.showSnackbar("Export failed: ${result.exceptionOrNull()?.message}")
//            }
//        } catch (e: Exception) {
//            Timber.tag("DEBUG_FLOW").e(e, "SongDetailsScreen: Error during video export")
//            snackbarHostState.showSnackbar("Export failed: ${e.message}")
//        } finally {
//            setExporting(false)
//        }
//    }
//}
