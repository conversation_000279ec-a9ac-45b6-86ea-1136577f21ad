package soly.lyricsgenerator.ui.screens.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.delay
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.viewmodel.LyricsViewModel

import kotlin.math.roundToInt
import android.graphics.Paint
import android.graphics.Typeface
import kotlin.math.ceil

/**
 * Display mode configuration for SharedLyricsDisplay
 */
enum class LyricsDisplayMode {
    FULL_SCREEN,    // Full scrollable list with auto-scroll and highlighting
    OVERLAY,        // Compact view showing current + context lines
    COMPACT         // Single line display
}

/**
 * Configuration for SharedLyricsDisplay appearance and behavior
 */
data class LyricsDisplayConfig(
    val mode: LyricsDisplayMode = LyricsDisplayMode.FULL_SCREEN,
    val maxVisibleLines: Int = 5, // For overlay mode
    val fontSize: TextUnit = 16.sp,
    val lineHeight: TextUnit = 24.sp,
    val activeColor: Color = Color.White,
    val inactiveColor: Color = Color.White,
    val activeAlpha: Float = 1f,
    val inactiveAlpha: Float = 0.4f,
    val activeWeight: FontWeight = FontWeight.Bold,
    val inactiveWeight: FontWeight = FontWeight.Normal,
    val horizontalPadding: Dp = 16.dp,
    val verticalSpacing: Dp = 16.dp,
    val enableAutoScroll: Boolean = true,
    val showPlaceholder: Boolean = true
)

/**
 * Safely animates scrolling to a specific item index with a given offset.
 */
private suspend fun LazyListState.safeAnimateScrollToItem(
    index: Int,
    offset: Int
) {
    try {
        this.animateScrollToItem(index, offset)
    } catch (e: Exception) {
        // Ignored
    }
}

/**
 * Shared lyrics display component that can be used in both song details and floating overlay
 * 
 * @param lrcLines Map of timestamp (ms) to lyric text
 * @param currentTimeMs Current playback position in milliseconds
 * @param config Display configuration
 * @param modifier Modifier for the component
 * @param viewModel LyricsViewModel for state management (optional, will create one if not provided and if in proper context)
 */
@Composable
fun SharedLyricsDisplay(
    modifier: Modifier = Modifier,
    lrcLines: Map<Int, String>,
    currentTimeMs: Int,
    config: LyricsDisplayConfig = LyricsDisplayConfig(),
    viewModel: LyricsViewModel? = null
) {
    // For overlay mode, calculate lyrics directly without ViewModel to avoid Hilt issues in Service context
    if (config.mode == LyricsDisplayMode.OVERLAY) {
        // Calculate active line and lines to display directly
        val linesToDisplay = remember(lrcLines) {
            lrcLines.toList().sortedBy { it.first }
        }
        
        val activeLineIndex = remember(currentTimeMs, linesToDisplay) {
            if (linesToDisplay.isEmpty()) -1
            else {
                val currentTimestamp = linesToDisplay
                    .map { it.first }
                    .filter { it <= currentTimeMs }
                    .maxOrNull()
                
                linesToDisplay.indexOfFirst { it.first == currentTimestamp }
            }
        }
        
        OverlayLyricsDisplay(
            linesToDisplay = linesToDisplay,
            activeLineIndex = activeLineIndex,
            config = config,
            modifier = modifier
        )
    } else {
        // For full-screen and compact modes, use ViewModel approach
        val lyricsViewModel = viewModel ?: hiltViewModel<LyricsViewModel>()
        
        // Feed ViewModel with current data
        LaunchedEffect(lrcLines, currentTimeMs) {
            lyricsViewModel.calculateActiveLineIndex(lrcLines, currentTimeMs)
        }

        val activeLineIndex by lyricsViewModel.activeLineIndex.collectAsState()
        val linesToDisplay by lyricsViewModel.linesToDisplay.collectAsState()
        
        when (config.mode) {
            LyricsDisplayMode.FULL_SCREEN -> {
                FullScreenLyricsDisplay(
                    linesToDisplay = linesToDisplay,
                    activeLineIndex = activeLineIndex,
                    config = config,
                    modifier = modifier
                )
            }
            LyricsDisplayMode.COMPACT -> {
                CompactLyricsDisplay(
                    linesToDisplay = linesToDisplay,
                    activeLineIndex = activeLineIndex,
                    config = config,
                    modifier = modifier
                )
            }
            else -> { /* This case is handled above */ }
        }
    }
}

/**
 * Full-screen lyrics display with scrolling and auto-centering
 */
@Composable
private fun FullScreenLyricsDisplay(
    linesToDisplay: List<Pair<Int, String>>,
    activeLineIndex: Int,
    config: LyricsDisplayConfig,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()
    var lockEnabled by rememberSaveable { mutableStateOf(false) }
    val density = LocalDensity.current
    var isProgrammaticScrollInProgress by remember { mutableStateOf(false) }

    // Calculate fallback height
    val fallbackItemHeightPx = with(density) {
        config.lineHeight.roundToPx()
    }

    // Detect user scroll to pause auto-scroll
    var userScrolling by remember { mutableStateOf(false) }
    LaunchedEffect(listState.isScrollInProgress, isProgrammaticScrollInProgress) {
        if (listState.isScrollInProgress && !isProgrammaticScrollInProgress) {
            userScrolling = true
            lockEnabled = false
        } else if (userScrolling) {
            delay(1500)
            userScrolling = false
        }
    }

    // Auto-scroll logic
    LaunchedEffect(activeLineIndex, lockEnabled, userScrolling) {
        if (!config.enableAutoScroll || activeLineIndex < 0 || userScrolling) {
            return@LaunchedEffect
        }

        val layoutInfo = listState.layoutInfo
        if (layoutInfo.visibleItemsInfo.isEmpty() && layoutInfo.totalItemsCount > 0) {
            return@LaunchedEffect
        }

        if (layoutInfo.totalItemsCount == 0) {
            return@LaunchedEffect
        }

        val vpHeight = layoutInfo.viewportSize.height
        if (vpHeight <= 0) {
            return@LaunchedEffect
        }
        val viewportHalfHeight = vpHeight / 2f

        val targetItemInfo = layoutInfo.visibleItemsInfo.find { it.index == activeLineIndex }
        val targetItemHeight = targetItemInfo?.size ?: -1

        val itemHeightForOffsetCalc: Int = if (targetItemHeight > 0) {
            targetItemHeight
        } else {
            fallbackItemHeightPx
        }

        if (itemHeightForOffsetCalc <= 0) {
            return@LaunchedEffect
        }

        val centerOffset = (viewportHalfHeight - itemHeightForOffsetCalc / 2f)
            .roundToInt()
            .coerceAtLeast(0)

        // Phase 2: Lock enabled - center the item
        if (lockEnabled) {
            try {
                isProgrammaticScrollInProgress = true
                val targetOffset = -centerOffset
                listState.animateScrollToItem(index = activeLineIndex, scrollOffset = targetOffset)
            } catch (_: Exception) {
                // Ignored
            } finally {
                isProgrammaticScrollInProgress = false
            }
            return@LaunchedEffect
        }

        // Phase 1: Make item visible first
        if (targetItemInfo == null) {
            try {
                isProgrammaticScrollInProgress = true
                listState.safeAnimateScrollToItem(activeLineIndex, 0)
            } finally {
                isProgrammaticScrollInProgress = false
            }
            return@LaunchedEffect
        }

        // Check if item crossed midpoint to enable lock
        val currentItemCenter = targetItemInfo.offset + targetItemInfo.size / 2f
        if (currentItemCenter > viewportHalfHeight) {
            lockEnabled = true
            return@LaunchedEffect
        }
    }

    BoxWithConstraints(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = config.horizontalPadding)
    ) {
        val bottomPadding = remember(constraints.maxHeight) {
            with(density) { (constraints.maxHeight / 2f).toDp() }
        }

        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = bottomPadding),
            verticalArrangement = Arrangement.spacedBy(config.verticalSpacing),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (linesToDisplay.isEmpty() && config.showPlaceholder) {
                item {
                    Box(
                        modifier = Modifier
                            .fillParentMaxSize()
                            .padding(vertical = 32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_lyrics_available),
                            textAlign = TextAlign.Center,
                            color = config.inactiveColor,
                            fontSize = config.fontSize
                        )
                    }
                }
            } else {
                itemsIndexed(
                    items = linesToDisplay,
                    key = { _, pair -> pair.first }
                ) { index, (_, line) ->
                    LyricRow(
                        text = line,
                        isActive = index == activeLineIndex,
                        config = config
                    )
                }
            }
        }
    }
}

/**
 * Overlay lyrics display showing current + context lines
 */
@Composable
private fun OverlayLyricsDisplay(
    linesToDisplay: List<Pair<Int, String>>,
    activeLineIndex: Int,
    config: LyricsDisplayConfig,
    modifier: Modifier = Modifier
) {
    val visibleLines = remember(linesToDisplay, activeLineIndex, config.maxVisibleLines) {
        if (linesToDisplay.isEmpty() || activeLineIndex < 0) {
            emptyList()
        } else {
            val halfLines = config.maxVisibleLines / 2
            val startIndex = (activeLineIndex - halfLines).coerceAtLeast(0)
            val endIndex = (activeLineIndex + halfLines + 1).coerceAtMost(linesToDisplay.size)
            
            linesToDisplay.subList(startIndex, endIndex).mapIndexed { localIndex, pair ->
                val globalIndex = startIndex + localIndex
                Triple(globalIndex, pair.first, pair.second)
            }
        }
    }

    Column(
        modifier = modifier.padding(horizontal = config.horizontalPadding),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(config.verticalSpacing)
    ) {
        if (visibleLines.isEmpty() && config.showPlaceholder) {
            Text(
                text = stringResource(R.string.no_lyrics_available),
                textAlign = TextAlign.Center,
                color = config.inactiveColor,
                fontSize = config.fontSize,
                modifier = Modifier.fillMaxWidth()
            )
        } else {
            visibleLines.forEach { (globalIndex, _, line) ->
                LyricRow(
                    text = line,
                    isActive = globalIndex == activeLineIndex,
                    config = config
                )
            }
        }
    }
}

/**
 * Compact lyrics display showing only the current line
 */
@Composable
private fun CompactLyricsDisplay(
    linesToDisplay: List<Pair<Int, String>>,
    activeLineIndex: Int,
    config: LyricsDisplayConfig,
    modifier: Modifier = Modifier
) {
    val currentLine = remember(linesToDisplay, activeLineIndex) {
        if (linesToDisplay.isEmpty() || activeLineIndex < 0 || activeLineIndex >= linesToDisplay.size) {
            null
        } else {
            linesToDisplay[activeLineIndex].second
        }
    }

    Box(
        modifier = modifier.padding(horizontal = config.horizontalPadding),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = currentLine ?: if (config.showPlaceholder) stringResource(R.string.no_lyrics_available) else "",
            textAlign = TextAlign.Center,
            color = config.activeColor,
            fontSize = config.fontSize,
            fontWeight = config.activeWeight,
            lineHeight = config.lineHeight,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * Individual lyric line component
 */
@Composable
private fun LyricRow(
    text: String,
    isActive: Boolean,
    config: LyricsDisplayConfig
) {
    val color = if (isActive) config.activeColor else config.inactiveColor
    val alpha = if (isActive) config.activeAlpha else config.inactiveAlpha
    val weight = if (isActive) config.activeWeight else config.inactiveWeight

    Text(
        text = text,
        color = color,
        fontSize = config.fontSize,
        lineHeight = config.lineHeight,
        fontWeight = weight,
        textAlign = TextAlign.Center,
        modifier = Modifier
            .fillMaxWidth()
            .alpha(alpha)
    )
}

/**
 * Calculate the actual height needed for overlay lyrics display
 * This simulates the OverlayLyricsDisplay rendering to determine real height requirements
 */
fun calculateOverlayLyricsHeight(
    allLyrics: Map<Int, String>,
    activeLineIndex: Int,
    config: LyricsDisplayConfig,
    overlayWidth: Int,
    density: Float,
    scaledDensity: Float
): Int {

    // Convert the lyrics to sorted list (same as in OverlayLyricsDisplay)
    val linesToDisplay = allLyrics.toList().sortedBy { it.first }
    
    if (linesToDisplay.isEmpty() || activeLineIndex < 0) {
        return (config.fontSize.value * scaledDensity).toInt() // Single line for "no lyrics" message
    }
    
    // Calculate visible lines (same logic as OverlayLyricsDisplay)
    val halfLines = config.maxVisibleLines / 2
    val startIndex = (activeLineIndex - halfLines).coerceAtLeast(0)
    val endIndex = (activeLineIndex + halfLines + 1).coerceAtMost(linesToDisplay.size)
    
    val visibleLines = linesToDisplay.subList(startIndex, endIndex)
    
    // Calculate text width available (subtract horizontal padding)
    fun dpToPx(dp: Float): Int = (dp * density).toInt()
    val horizontalPadding = dpToPx(config.horizontalPadding.value) * 2 // both sides
    val textWidth = overlayWidth - horizontalPadding
    
    // Create Paint object for text measurement
    val textPaint = Paint().apply {
        textSize = config.fontSize.value * scaledDensity
        typeface = if (config.activeWeight == FontWeight.Bold) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
        isAntiAlias = true
    }
    
    // Calculate height for each visible line considering text wrapping
    var totalHeight = 0
    val lineHeight = (config.lineHeight.value * scaledDensity).toInt()
    val verticalSpacing = dpToPx(config.verticalSpacing.value)
    
    visibleLines.forEachIndexed { index, (_, line) ->
        // More accurate text wrapping calculation using StaticLayout approach
        val textWidthActual = textPaint.measureText(line)
        
        // Better wrapping estimation: if text exceeds width by small margin, consider word boundaries
        val wrappedLines = if (textWidthActual <= textWidth) {
            1
        } else {
            // For wrapped text, add safety margin of 20% to account for word boundaries and edge cases
            val roughEstimate = ceil(textWidthActual / textWidth).toInt()
            val safetyFactor = 1.2f
            (roughEstimate * safetyFactor).toInt().coerceAtLeast(2)
        }
        
        totalHeight += lineHeight * wrappedLines
        
        // Add vertical spacing between lines (except after last line)
        if (index < visibleLines.size - 1) {
            totalHeight += verticalSpacing
        }
    }
    
    // Add safety margin to prevent cutoff issues when all lines wrap to multiple rows
    val safetyMargin = (config.lineHeight.value * scaledDensity * 0.5f).toInt() // Extra half line height
    return totalHeight + safetyMargin
}