package soly.lyricsgenerator.ui.screens.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.sp
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.FileContentState
import soly.lyricsgenerator.domain.model.FileContentVisitor
import soly.lyricsgenerator.ui.viewmodel.LyricsViewModel

/**
 * Sealed class for rendering different types of file content
 * Follows Clean Architecture with polymorphic dispatch instead of when expressions
 */
sealed class FileContentRenderer {
    abstract fun render(
        modifier: Modifier,
        currentTimeMs: Int,
        lyricsViewModel: LyricsViewModel?
    ): @Composable () -> Unit
    
    /**
     * Renderer for LRC content with synchronized display
     */
    data class LrcRenderer(val lrcLines: Map<Int, String>) : FileContentRenderer() {
        override fun render(
            modifier: Modifier,
            currentTimeMs: Int,
            lyricsViewModel: LyricsViewModel?
        ): @Composable () -> Unit = {
            // Use original resource dimensions for song details screen (independent from overlay settings)
            val originalFontSize = dimensionResource(R.dimen.lyric_line_inactive_font_size).value.sp
            val originalLineHeight = dimensionResource(R.dimen.lyric_line_height).value.sp
            
            SharedLyricsDisplay(
                lrcLines = lrcLines,
                currentTimeMs = currentTimeMs,
                config = LyricsDisplayConfig(
                    mode = LyricsDisplayMode.FULL_SCREEN,
                    fontSize = originalFontSize,
                    lineHeight = originalLineHeight,
                    // Keep other defaults for song details screen
                    enableAutoScroll = true,
                    showPlaceholder = true
                ),
                modifier = modifier,
                viewModel = lyricsViewModel!!
            )
        }
    }
    
    /**
     * Renderer for TXT content with static display
     */
    data class TxtRenderer(val textLines: List<String>) : FileContentRenderer() {
        override fun render(
            modifier: Modifier,
            currentTimeMs: Int,
            lyricsViewModel: LyricsViewModel?
        ): @Composable () -> Unit = {
            TxtDisplay(
                textLines = textLines,
                modifier = modifier
            )
        }
    }
    /**
     * Renderer for RTF (Rich Text Format) content with static display
     */
    data class RtfRenderer(val textLines: List<String>) : FileContentRenderer() {
        override fun render(
            modifier: Modifier,
            currentTimeMs: Int,
            lyricsViewModel: LyricsViewModel?,
        ): @Composable () -> Unit = {
            TxtDisplay(
                textLines = textLines,
                modifier = modifier
            )
        }
    }

    
    /**
     * Renderer for empty content
     */
    data object EmptyRenderer : FileContentRenderer() {
        override fun render(
            modifier: Modifier,
            currentTimeMs: Int,
            lyricsViewModel: LyricsViewModel?
        ): @Composable () -> Unit = {
            TxtDisplay(
                textLines = emptyList(),
                modifier = modifier
            )
        }
    }
}

/**
 * Visitor implementation that creates renderers from file content states
 * Uses polymorphic dispatch via visitor pattern instead of when expressions
 */
class FileContentRendererVisitor : FileContentVisitor<FileContentRenderer> {
    override fun visitLrcContent(content: FileContentState.LrcContent): FileContentRenderer {
        return FileContentRenderer.LrcRenderer(content.lrcLines)
    }
    
    override fun visitTxtContent(content: FileContentState.TxtContent): FileContentRenderer {
        return FileContentRenderer.TxtRenderer(content.textLines)
    }

    override fun visitRtfContent(content: FileContentState.RtfContent): FileContentRenderer {
        return FileContentRenderer.RtfRenderer(content.textLines)
    }
    
    override fun visitEmpty(content: FileContentState.Empty): FileContentRenderer {
        return FileContentRenderer.EmptyRenderer
    }
}

/**
 * Extension function to create renderer using visitor pattern
 */
fun FileContentState.createRenderer(): FileContentRenderer {
    return this.accept(FileContentRendererVisitor())
}