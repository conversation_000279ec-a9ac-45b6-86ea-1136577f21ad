package soly.lyricsgenerator.ui.screens.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.model.SongDetails
import soly.lyricsgenerator.utils.SongDetailsUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SongDetailsBottomSheet(
    song: Song,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )
    
    var songDetails by remember { mutableStateOf<SongDetails?>(null) }
    
    // Load song details asynchronously
    LaunchedEffect(song) {
        songDetails = SongDetailsUtils.getSongDetails(context, song)
    }
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp)
        ) {
            Text(
                text = stringResource(R.string.song_details),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            songDetails?.let { details ->
                DetailRow(
                    label = stringResource(R.string.file_path),
                    value = details.filePath
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.details_file_name),
                    value = details.fileName
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.file_size),
                    value = SongDetailsUtils.formatFileSize(details.fileSize)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.last_modified),
                    value = SongDetailsUtils.formatDate(details.lastModified)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.container_format),
                    value = details.container
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.audio_codec),
                    value = details.audioCodec
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.length),
                    value = SongDetailsUtils.formatDuration(details.duration)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.bitrate),
                    value = SongDetailsUtils.formatBitrate(details.bitrate)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                DetailRow(
                    label = stringResource(R.string.sampling_rate),
                    value = SongDetailsUtils.formatSamplingRate(details.samplingRate)
                )
                
            } ?: run {
                // Loading state
                Text(
                    text = "Loading...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
    }
}