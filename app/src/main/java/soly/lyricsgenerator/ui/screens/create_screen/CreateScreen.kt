package soly.lyricsgenerator.ui.screens.create_screen

import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BottomSheetScaffold
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import createTxtFilePickerLauncher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.navigation.NavigationConstants
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.analytics.AnalyticsConstants
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.geometry.Offset
import androidx.compose.foundation.background
import androidx.compose.material3.MaterialTheme
import soly.lyricsgenerator.ui.screens.components.CompactMusicPlayerController
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import timber.log.Timber

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateScreen(
    navController: NavHostController,
    musicViewModel: MusicViewModel,
    syncSongId: Long? = null,
    syncFileId: Long? = null
) {
    val activity = LocalContext.current as ComponentActivity
    val songViewModel: CreateViewModel = hiltViewModel(viewModelStoreOwner = activity)
    val sharedViewModel: SharedViewModel = hiltViewModel(viewModelStoreOwner = activity)
    val selectedSong by songViewModel.selectedSong.collectAsState()
    val context = LocalContext.current

    // Handle navigation events from the music player
    LaunchedEffect(musicViewModel.navigationEvent) {
        musicViewModel.navigationEvent.collect { route ->
            // If navigating to SongDetails, pass the current song ID and a flag
            if (route == NavRoutes.SongDetails) {
                musicViewModel.getUIState().value.currentlyPlayingSong?.let { song ->
                    navController.currentBackStackEntry?.savedStateHandle?.apply {
                        set(NavigationConstants.KEY_SONG_ID, song.id)
                        set(NavigationConstants.KEY_FROM_COMPACT_PLAYER, true)
                    }
                }
            }
            navController.navigate(route.route)
        }
    }

    val showConfirmationDialog = remember { mutableStateOf(false) }
    val showDialog = remember { MutableStateFlow(false) }
    val showDialogState by showDialog.collectAsState()
    val showImportPasteLyricsDialog = remember { mutableStateOf(false) }
    val showUndoConfirmationDialog = remember { mutableStateOf(false) }

    val scaffoldState = rememberBottomSheetScaffoldState(
        bottomSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false)
    )
    val coroutineScope = rememberCoroutineScope()

    // State flows for broadcast receiver
    val songsFlow = remember { MutableStateFlow<List<Song>>(emptyList()) }
    val songsState by songsFlow.collectAsState()
    val currentPositionFlow = songViewModel.currentPositionFlow
    val currentPosition by currentPositionFlow.collectAsState()

    // State to manage the visibility of the "start creating lrc" button
    val showStartButton = remember { MutableStateFlow(true) }
    val showStartButtonState by showStartButton.collectAsState()
    
    // Use the SharedViewModel's isPostStartScreen instead of local state
    val isPostStartScreen by sharedViewModel.isPostStartScreen
    // We'll keep showStepsComposable for backwards compatibility but it will be !isPostStartScreen
    val showStepsComposable = !isPostStartScreen

    // MutableState to store the height of MusicPlayerController
    val musicControllerHeight = remember { MutableStateFlow(0) }
    val musicControllerHeightPx by musicControllerHeight.collectAsState()

    // Track completion state of steps
    val isSongSelected = songViewModel.selectedSong.collectAsState().value != null
    val isLyricsAdded = songViewModel.lrcKeyValuePairs.collectAsState().value.isNotEmpty()
    val allStepsCompleted = isSongSelected && isLyricsAdded
    
    // Observe MusicViewModel state for compact player
    val musicUiState by musicViewModel.getUIState().collectAsState()
    val showCompactPlayer = musicUiState.currentlyPlayingSong != null && !songViewModel.isSongAndFileSelected()
    
    // State to track when steps component buttons are clicked to hide compact player
    val hideCompactPlayerForSteps = remember { mutableStateOf(false) }
    

    // State for info tooltips
    val showSongInfo = remember { mutableStateOf(false) }
    val showLyricsInfo = remember { mutableStateOf(false) }
    val showStartInfo = remember { mutableStateOf(false) }

    // State for incomplete steps dialog
    val showIncompleteStepsDialog = remember { mutableStateOf(false) }

    if (showIncompleteStepsDialog.value) {
        AlertDialog(
            onDismissRequest = { showIncompleteStepsDialog.value = false },
            title = { Text(stringResource(id = R.string.incomplete_steps)) },
            text = {
                Text(stringResource(id = R.string.incomplete_steps_message))
            },
            confirmButton = {
                TextButton(onClick = { showIncompleteStepsDialog.value = false }) {
                    Text(stringResource(id = R.string.ok))
                }
            }
        )
    }

    if (showUndoConfirmationDialog.value) {
        AlertDialog(
            onDismissRequest = { showUndoConfirmationDialog.value = false },
            title = { Text(stringResource(id = R.string.undo_sync_confirmation_title)) },
            text = { Text(stringResource(id = R.string.undo_sync_confirmation_message)) },
            confirmButton = {
                TextButton(onClick = {
                    songViewModel.resetTimestamps()
                    showUndoConfirmationDialog.value = false
                }) {
                    Text(stringResource(id = R.string.yes_undo))
                }
            },
            dismissButton = {
                TextButton(onClick = { showUndoConfirmationDialog.value = false }) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

    val drawSecondItemUpperLine = remember { mutableStateOf(false) }
    val upperLineProgressSecond = remember { Animatable(0f) }
    val lowerLineProgressSecond = remember { Animatable(0f) }
    val upperLineProgressFirst = remember { Animatable(0f) }
    val lowerLineProgressFirst = remember { Animatable(0f) }
    val drawThirdItemUpperLine = remember { mutableStateOf(false) }
    val upperLineProgressThird = remember { Animatable(0f) }
    val lowerLineProgressThird = remember { Animatable(0f) }

    // Log screen view analytics
    LaunchedEffect(Unit) {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.CREATE)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
            param(AnalyticsConstants.Params.SYNC_SONG_ID, syncSongId?.toString() ?: "none")
            param(AnalyticsConstants.Params.SYNC_FILE_ID, syncFileId?.toString() ?: "none")
        }
    }

    BackHandler(enabled = songViewModel.isSongAndFileSelected()) {
        // Log back button pressed analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_BACK_BUTTON_PRESSED) {
            param(AnalyticsConstants.Params.HAS_UNSAVED_CHANGES, songViewModel.isSongAndFileSelected().toString())
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
        }
        showConfirmationDialog.value = true
    }

    if (showConfirmationDialog.value) {
        // Log confirmation dialog shown analytics
        LaunchedEffect(Unit) {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_CONFIRMATION_DIALOG_SHOWN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                param(AnalyticsConstants.Params.DIALOG_TYPE, AnalyticsConstants.Values.DIALOG_TYPE_UNSAVED_CHANGES)
            }
        }
        
        AlertDialog(
            onDismissRequest = { 
                // Log dialog dismissed analytics
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_CONFIRMATION_DIALOG_CANCELLED) {
                    param(AnalyticsConstants.Params.DISMISS_METHOD, AnalyticsConstants.Values.DISMISS_METHOD_OUTSIDE_CLICK)
                }
                showConfirmationDialog.value = false 
            },
            confirmButton = {
                TextButton(onClick = {
                    // Log dialog confirmed analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_CONFIRMATION_DIALOG_CONFIRMED) {
                        param(AnalyticsConstants.Params.BUTTON_TYPE, AnalyticsConstants.Values.BUTTON_TYPE_CONFIRM)
                        param(AnalyticsConstants.Params.ACTION, AnalyticsConstants.Values.ACTION_NAVIGATE_UP)
                    }
                    showConfirmationDialog.value = false
                    navController.navigateUp()
                }) {
                    Text(stringResource(id = R.string.yes_leave))
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    // Log dialog cancelled analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_CONFIRMATION_DIALOG_CANCELLED) {
                        param(AnalyticsConstants.Params.BUTTON_TYPE, AnalyticsConstants.Values.BUTTON_TYPE_CANCEL)
                        param(AnalyticsConstants.Params.DISMISS_METHOD, AnalyticsConstants.Values.DISMISS_METHOD_BUTTON_CLICK)
                    }
                    showConfirmationDialog.value = false 
                }) {
                    Text(stringResource(id = R.string.cancel))
                }
            },
            title = {
                Text(stringResource(id = R.string.unsaved_changes))
            },
            text = {
                Text(stringResource(id = R.string.unsaved_changes_message))
            }
        )
    }

    // Broadcast receiver for handling song list navigation
    DisposableEffect(Unit) {
        songViewModel.registerReceiver()
        onDispose {
            songViewModel.unregisterReceiver()
        }
    }

    // Track both if song and file are selected, and if the songs/lyrics data has been modified 
    // since the last save operation
    LaunchedEffect(songViewModel.isSongAndFileSelected()) {
        // Only set unsaved changes to true if song and file are selected
        // If they are not selected, there's nothing to save, so we don't show the warning
        if (songViewModel.isSongAndFileSelected()) {
            sharedViewModel.setUnsavedChanges(true)
        }
    }

    DisposableEffect(Unit) {
        ScreenStateHolder.isCreateScreenActive = true

        // Check if we're returning from a subflow navigation
        if (sharedViewModel.isNavigatingInSubflow.value) {
            // We're returning from a subflow, maintain current post-start state
            // Reset the navigation flag for next navigation
            sharedViewModel.setNavigatingInSubflow(false)
        } else {
            // First time or returning from non-subflow, reset post-start state
            sharedViewModel.setPostStartScreen(false)
        }
        
        showStartButton.value = true

        onDispose {
            ScreenStateHolder.isCreateScreenActive = false

            // Determine if navigating away from core creation/playback flow
            val currentRoute = navController.currentBackStackEntry?.destination?.route ?: ""
            
            // Check if we need to maintain playback (when going to any main tab or SongDetails)
            val isNavigatingToMainTab = currentRoute == NavRoutes.Music.route || 
                                       currentRoute == NavRoutes.Files.route
            
            // Check if navigating to SongDetails (likely from compact player)
            val isNavigatingToSongDetails = currentRoute == NavRoutes.SongDetails.route
            
            // Consider only SongPicker and PasteLyrics as part of the creation workflow
            // Navigation to Music, Files, or any other tab should clear selections
            val isStayingInCreationFlow = currentRoute.startsWith(NavRoutes.SongPickerScreen.route) || 
                                          currentRoute == NavRoutes.PasteLyricsScreen.route

            // Only preserve state if we're staying within the creation workflow
            if (!isStayingInCreationFlow) {
                // Only stop music when NOT navigating to main tabs (Music or Files) or SongDetails
                val shouldStopMusic = !isNavigatingToMainTab && !isNavigatingToSongDetails
                
                if (!isNavigatingToMainTab && !isNavigatingToSongDetails) {
                    // Only explicitly stop playback when not going to main tabs or SongDetails
                    if (selectedSong != null) {
                        MusicPlayerService.startService(context, MusicPlayerService.ACTION_STOP)
                    }
                }
                
                // Clear selections but maintain music playback if going to Music tab
                songViewModel.clearSelections(
                    clearSong = true, 
                    clearLyrics = true, 
                    stopMusic = shouldStopMusic
                )
                
                sharedViewModel.setUnsavedChanges(false)
                sharedViewModel.setPostStartScreen(false) // Reset post-start state
                
                // Only reset sync initialization if NOT navigating to Files tab
                if (currentRoute != NavRoutes.Files.route) {
                    sharedViewModel.setSyncInitialized(false) // Reset sync initialization state
                }
            }
        }
    }

     // Automatically play the song when it's selected
     LaunchedEffect(selectedSong) {
         selectedSong?.let { song ->
             MusicPlayerService.startService(
                 context = context,
                 action = MusicPlayerService.ACTION_PLAY,
                 song = song,
                 playMode = MusicPlayerService.MODE_CREATION
             )
             // We don't reset post-start state here, it should be preserved when changing songs
         }
     }

    // Load songs when entering the Create screen
    LaunchedEffect(Unit) {
        MusicPlayerService.startService(
            context,
            MusicPlayerService.ACTION_LOAD_SONGS,
            navRoute = NavRoutes.Create
        )
        
        // Notify MusicPlayerService about screen transition for playback continuity
        val previousRoute = navController.previousBackStackEntry?.destination?.route
        if (previousRoute == NavRoutes.Music.route) {
            MusicPlayerService.startService(
                context = context,
                action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                sourceScreen = previousRoute,
                destinationScreen = NavRoutes.Create.route
            )
        }
    }
    
    // Ensure songs are properly loaded into the ViewModel from the broadcast receiver
    LaunchedEffect(songsState) {
        if (songsState.isNotEmpty()) {
            songViewModel.songs.value = songsState
            Toast.makeText(context, "Loaded songs", Toast.LENGTH_SHORT).show()
        }
    }
    
    // Handle sync mode initialization - reinitialize if data was cleared
    LaunchedEffect(syncSongId, syncFileId) {
        if (syncSongId != null && syncFileId != null) {
            // Check if we need to reload the data (either first time or data was cleared)
            val needsInitialization = !sharedViewModel.isSyncInitialized.value || 
                                     songViewModel.lrcKeyValuePairs.value.isEmpty()
            
            timber.log.Timber.tag("DEBUG_FLOW").d("CreateScreen: Sync mode check - syncInitialized: ${sharedViewModel.isSyncInitialized.value}, lrcData empty: ${songViewModel.lrcKeyValuePairs.value.isEmpty()}, needs init: $needsInitialization")
            
            if (needsInitialization) {
                timber.log.Timber.tag("DEBUG_FLOW").d("CreateScreen: Initializing sync mode for songId: $syncSongId, fileId: $syncFileId")
                // Initialize sync mode in ViewModel and wait for completion
                songViewModel.initializeSyncMode(syncSongId, syncFileId)
                
                // Mark sync as initialized for this session
                sharedViewModel.setSyncInitialized(true)
                
                // Log analytics event
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SYNC_FLOW_STARTED) {
                    param(AnalyticsConstants.Params.SONG_ID, syncSongId)
                    param(AnalyticsConstants.Params.FILE_ID, syncFileId)
                }
            } else {
                timber.log.Timber.tag("DEBUG_FLOW").d("CreateScreen: Sync already initialized, skipping reload")
            }
            
            // Always ensure we're in post-start mode for sync
            sharedViewModel.setPostStartScreen(true)
            // Hide the start button since we're bypassing the steps
            showStartButton.value = false
        }
    }

    val bottomSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false)
    val filePickerLauncher = createTxtFilePickerLauncher(
        context = context,
        coroutineScope = coroutineScope,
        bottomSheetState = bottomSheetState,
        onTxtLinesParsed = { parsedLines, fileName ->
            songViewModel.pickedTextFileName.value = fileName
            songViewModel.lrcKeyValuePairs.value = parsedLines
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LYRICS_ADDED_FROM_FILE) {
                param(AnalyticsConstants.Params.FILENAME, fileName)
                param(AnalyticsConstants.Params.LINE_COUNT, parsedLines.size.toLong())
            }
            // Keep post-start state unchanged when replacing text file
        }
    )
    
    LaunchedEffect(showDialogState) {
        if (showDialogState) {
            // Log song picker trigger analytics
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_SONG_CHANGE_CLICKED) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                param(AnalyticsConstants.Params.NAVIGATION_METHOD, "dialog_trigger")
                param(AnalyticsConstants.Params.IS_CHANGING_SONG, (!isPostStartScreen).toString())
            }
            
            // Set navigating flag before navigating to picker
            sharedViewModel.setNavigatingInSubflow(true)
            
            // If not in post-start screen mode, steps should remain visible when changing song
            val isChangingSong = if (!isPostStartScreen) {
                // Always false when changing from Steps component to ensure steps stay visible
                false
            } else {
                // For other cases (post-start screen), use the original logic
                songViewModel.selectedSong.value != null
            }
            navController.navigate("${NavRoutes.SongPickerScreen.route}?isChangingSong=$isChangingSong")
        }
    }

    // Handle alert dialog for import/paste choices with navigating flag
    if (showImportPasteLyricsDialog.value) {
        // Log import/paste dialog shown analytics
        LaunchedEffect(Unit) {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_IMPORT_PASTE_DIALOG_SHOWN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                param(AnalyticsConstants.Params.DIALOG_TYPE, "import_paste_choice")
            }
        }
        
        androidx.compose.ui.window.Dialog(onDismissRequest = { 
            // Log dialog dismissed analytics
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_IMPORT_PASTE_DIALOG_DISMISSED) {
                param(AnalyticsConstants.Params.DISMISS_METHOD, AnalyticsConstants.Values.DISMISS_METHOD_OUTSIDE_CLICK)
            }
            showImportPasteLyricsDialog.value = false 
        }) {
            androidx.compose.material3.Surface(
                shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
                tonalElevation = 6.dp
            ) {
                androidx.compose.foundation.layout.Column(
                    modifier = androidx.compose.ui.Modifier
                        .padding(16.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        text = stringResource(R.string.lyrics_input_option_dialog_title),
                        modifier = androidx.compose.ui.Modifier.padding(bottom = 8.dp)
                    )
                    Text(
                        text = stringResource(R.string.lyrics_input_option_dialog_message),
                        modifier = androidx.compose.ui.Modifier.padding(bottom = 16.dp)
                    )
                    androidx.compose.material3.TextButton(onClick = {
                        // Log import file option selected analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LYRICS_ADDED_FROM_FILE) {
                            param(AnalyticsConstants.Params.LYRICS_SOURCE, "file_import")
                            param(AnalyticsConstants.Params.DIALOG_TYPE, "import_paste_choice")
                        }
                        showImportPasteLyricsDialog.value = false
                        filePickerLauncher.launch(arrayOf("text/plain"))
                    }) {
                        Text(stringResource(R.string.import_txt_option))
                    }
                    androidx.compose.material3.TextButton(onClick = {
                        // Log paste lyrics option selected analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LYRICS_PASTED_SAVED) {
                            param(AnalyticsConstants.Params.LYRICS_SOURCE, "paste_text")
                            param(AnalyticsConstants.Params.NAVIGATION_METHOD, "dialog_button")
                        }
                        showImportPasteLyricsDialog.value = false
                        sharedViewModel.setNavigatingInSubflow(true)
                        navController.navigate(NavRoutes.PasteLyricsScreen.route)
                    }) {
                        Text(stringResource(R.string.paste_lyrics_option))
                    }
                    // androidx.compose.material3.TextButton(onClick = {
                    //     showImportPasteLyricsDialog.value = false
                    //     coroutineScope.launch {
                    //         songViewModel.transcribeSelectedAudio()
                    //     }
                    //     Timber.tag("DEBUG_FLOW").d("CreateScreen: Transcribe Audio started for selected song.")
                    // }) {
                    //     Text("Transcribe Audio")
                    // }
                }
            }
        }
    }

    val rootBackgroundModifier = if (!isPostStartScreen) {
        Modifier.background(brush = AppGradientBrush)
    } else {
        Modifier.background(MaterialTheme.colorScheme.background)
    }

    Box(modifier = Modifier.fillMaxSize().then(rootBackgroundModifier)) { // Root Box with conditional background
        BottomSheetScaffold(
            scaffoldState = scaffoldState,
            sheetContent = {
            },
            sheetShape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            sheetPeekHeight = 0.dp,
            containerColor = Color.Transparent, // Make scaffold's own container transparent
            sheetContainerColor = MaterialTheme.colorScheme.surface // Keep sheet opaque
        ) { paddingValues ->
            // Content of BottomSheetScaffold
            if (!isPostStartScreen) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues) // Apply padding from scaffold
                        .background(Color.Transparent), // Ensure this Box is transparent
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        // Make sure musicControllerHeightPx is accessed correctly, e.g., .value if it's a State
                        modifier = Modifier.offset(y = (-musicControllerHeightPx / 4).dp),
                        contentAlignment = Alignment.Center
                    ) {
                        StepsComponent(
                            isSongSelected = isSongSelected,
                            isLyricsAdded = isLyricsAdded,
                            allStepsCompleted = allStepsCompleted,
                            showDialog = showDialog,
                            showSongInfo = showSongInfo,
                            showLyricsInfo = showLyricsInfo,
                            showStartInfo = showStartInfo,
                            showIncompleteStepsDialog = showIncompleteStepsDialog,
                            showStepsComposable = MutableStateFlow(!isPostStartScreen),
                            showStartButton = showStartButton,
                            upperLineProgressFirst = upperLineProgressFirst,
                            lowerLineProgressFirst = lowerLineProgressFirst,
                            upperLineProgressSecond = upperLineProgressSecond,
                            lowerLineProgressSecond = lowerLineProgressSecond,
                            upperLineProgressThird = upperLineProgressThird,
                            lowerLineProgressThird = lowerLineProgressThird,
                            drawSecondItemUpperLine = drawSecondItemUpperLine,
                            drawThirdItemUpperLine = drawThirdItemUpperLine,
                            onAddLyricsClicked = {
                                // Log add lyrics button clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_IMPORT_PASTE_DIALOG_SHOWN) {
                                    param(AnalyticsConstants.Params.SOURCE, "add_lyrics_button")
                                    param(AnalyticsConstants.Params.CREATION_STAGE, "lyrics_input")
                                }
                                showImportPasteLyricsDialog.value = true
                            },
                            sharedViewModel = sharedViewModel
                        )
                    }
                }
            } else {
                CreateScreenContent(
                    context = context,
                    songViewModel = songViewModel,
                    sharedViewModel = sharedViewModel,
                    showStartButtonState = showStartButtonState,
                    paddingValues = paddingValues,
                    currentPosition = currentPosition,
                    musicControllerHeightPx = musicControllerHeightPx,
                    currentPositionFlow = currentPositionFlow,
                    musicControllerHeight = musicControllerHeight,
                    onChangeSong = {
                        // Set navigating flag before navigating to song picker
                        sharedViewModel.setNavigatingInSubflow(true)
                        // Navigate to song picker with isChangingSong=true to indicate we're replacing a song
                        navController.navigate("${NavRoutes.SongPickerScreen.route}?isChangingSong=true")
                    },
                    onUndoClicked = { showUndoConfirmationDialog.value = true }
                )
            }
        }
        
        // CompactMusicPlayerController at the bottom
        if (showCompactPlayer && !hideCompactPlayerForSteps.value) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                CompactMusicPlayerController(
                    currentSong = musicUiState.currentlyPlayingSong,
                    currentPosition = musicUiState.currentPosition,
                    isPlaying = musicUiState.isPlaying,
                    isVisible = true,
                    onPlayPauseClicked = {
                        if (musicUiState.isPlaying) {
                            musicViewModel.pausePlaying()
                        } else {
                            musicViewModel.resumePlaying()
                        }
                    },
                    onNextClicked = {
                        musicViewModel.playNextSong()
                    },
                    onPreviousClicked = {
                        musicViewModel.playPreviousSong()
                    },
                    onPlayerClicked = {
                        musicViewModel.onInfoClicked()
                    }
                )
            }
        }
    }

    // Reset animation state at the beginning of each session
    LaunchedEffect(Unit) {
        // Ensure animations run at least once per session by resetting flags
        if (isSongSelected && !songViewModel.songAnimationPlayed.value) {
            // Set animated value directly without animation for initial state
            lowerLineProgressFirst.snapTo(1f)
            upperLineProgressSecond.snapTo(1f)
            // Mark as played since we've set the values directly
            songViewModel.markSongAnimationPlayed()
        }
        
        if (isLyricsAdded && !songViewModel.lyricsAnimationPlayed.value) {
            // Set animated value directly without animation for initial state
            lowerLineProgressSecond.snapTo(1f)
            upperLineProgressThird.snapTo(1f)
            // Mark as played since we've set the values directly
            songViewModel.markLyricsAnimationPlayed()
        }
    }

    LaunchedEffect(isSongSelected) {
        if (isSongSelected && !songViewModel.songAnimationPlayed.value) {
            lowerLineProgressFirst.animateTo(
                targetValue = 1f,
                animationSpec = tween(3000, easing = LinearEasing)
            )
            upperLineProgressSecond.animateTo(
                targetValue = 1f,
                animationSpec = tween(1000, easing = LinearEasing)
            )
            // Mark animation as played so it doesn't repeat
            songViewModel.markSongAnimationPlayed()
        } else if (isSongSelected) {
            // Ensure lines are visible even if not animating
            lowerLineProgressFirst.snapTo(1f)
            upperLineProgressSecond.snapTo(1f)
        }
    }

    LaunchedEffect(isLyricsAdded) {
        if (isLyricsAdded && !songViewModel.lyricsAnimationPlayed.value) {
            lowerLineProgressSecond.animateTo(
                targetValue = 1f,
                animationSpec = tween(1000, easing = LinearEasing)
            )
            upperLineProgressThird.animateTo(
                targetValue = 1f,
                animationSpec = tween(1000, easing = LinearEasing)
            )
            // Mark animation as played so it doesn't repeat
            songViewModel.markLyricsAnimationPlayed()
        } else if (isLyricsAdded) {
            // Ensure lines are visible even if not animating
            lowerLineProgressSecond.snapTo(1f)
            upperLineProgressThird.snapTo(1f)
        }
    }

    // Log screen view
    LaunchedEffect(Unit) {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.CREATE)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
        }
    }
}
