package soly.lyricsgenerator.ui.screens.create_screen

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R

@Composable
fun CompletedStepIndicator() {
    Box(contentAlignment = Alignment.Center) {
        // Crni krug pozadina
        <PERSON>(modifier = Modifier.size(24.dp)) {
            drawCircle(
                color = Color.Black,
                radius = size.minDimension / 2.5f
            )
        }
        // Ikonica preko crnog kruga
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = stringResource(R.string.content_desc_completed),
            tint = Color.Green,
            modifier = Modifier.size(24.dp)
        )
    }
} 