package soly.lyricsgenerator.ui.screens.song_details_screen.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.VideoFile
import androidx.compose.material.icons.filled.PictureInPicture
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetState
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.navigation.navigateToTagEditor
import soly.lyricsgenerator.domain.model.Song
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.only
import timber.log.Timber

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopAppBarSongDetails(
    navController: NavHostController,
    bottomSheetState: SheetState,
    coroutineScope: CoroutineScope,
    isExporting: Boolean,
    hasLyrics: Boolean,
    currentSong: Song? = null,
    onNavigatingToTagEditor: () -> Unit = {},
    onFloatingLyricsClick: () -> Unit = {}
) {
    TopAppBar(
        title = { },
        navigationIcon = {
            IconButton(onClick = { navController.popBackStack() }) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = stringResource(R.string.back),
                    tint = Color.White
                )
            }
        },
        actions = {
            // Floating lyrics button (only show if lyrics are available)
            if (hasLyrics) {
                IconButton(onClick = onFloatingLyricsClick) {
                    Icon(
                        imageVector = Icons.Default.PictureInPicture,
                        contentDescription = stringResource(R.string.floating_lyrics),
                        tint = Color.White
                    )
                }
            }
            
            // Import LRC file button
            IconButton(onClick = {
                Timber.tag("DEBUG_FLOW").d("TopAppBarSongDetails: Plus button clicked for LRC import")
                coroutineScope.launch {
                    Timber.tag("DEBUG_FLOW").d("TopAppBarSongDetails: Launching coroutine to show bottom sheet")
                    bottomSheetState.show()
                    Timber.tag("DEBUG_FLOW").d("TopAppBarSongDetails: Bottom sheet show() called, isVisible: ${bottomSheetState.isVisible}")
                }
            }) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = stringResource(R.string.import_text),
                    tint = Color.White
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent
        ),
        windowInsets = TopAppBarDefaults.windowInsets.only(
            WindowInsetsSides.Horizontal + WindowInsetsSides.Bottom
        )
    )
}