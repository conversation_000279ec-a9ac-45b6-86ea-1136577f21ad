package soly.lyricsgenerator.ui.screens.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.delay
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.viewmodel.LyricsViewModel
import timber.log.Timber
import kotlin.math.roundToInt

/**
 * Safely animates scrolling to a specific item index with a given offset.
 * Includes logging for start, success, and exceptions.
 */
private suspend fun LazyListState.safeAnimateScrollToItem(
    index: Int,
    offset: Int,
    logContext: String // e.g., "Phase-2", "Phase-1, item not visible"
) {
    try {
        this.animateScrollToItem(index, offset)
    } catch (e: Exception) {
        // Log general exceptions, but avoid explicitly catching CancellationExceptions
        // as they are part of the normal coroutine lifecycle.
        if (e !is kotlinx.coroutines.CancellationException) {
             Timber.tag("LyricsScroll").e(e, "DEBUG_FLOW: LyricsDisplay: Exception during animateScrollToItem (%s)", logContext)
        } else {
             Timber.tag("LyricsScroll").d("DEBUG_FLOW: LyricsDisplay: animateScrollToItem cancelled (%s)", logContext)
            // Optionally re-throw cancellation exceptions if needed, but usually not required.
            // throw e
        }
    }
}

@Composable
fun LyricsDisplay(
    lrcLines: Map<Int, String>,
    currentTimeMs: Int,
    modifier: Modifier = Modifier,
    viewModel: LyricsViewModel = hiltViewModel()
) {
    /* ---------- feed ViewModel --------------------------------------------------- */
    LaunchedEffect(lrcLines, currentTimeMs) {
        viewModel.calculateActiveLineIndex(lrcLines, currentTimeMs)
    }

    val activeLineIndex by viewModel.activeLineIndex.collectAsState()
    val linesToDisplay  by viewModel.linesToDisplay.collectAsState()

    /* ---------- state holders ---------------------------------------------------- */
    val listState   = rememberLazyListState()
    var lockEnabled by rememberSaveable { mutableStateOf(false) }   // Phase‑2 centre‑lock
    val density     = LocalDensity.current
    var isProgrammaticScrollInProgress by remember { mutableStateOf(false) }

    // Calculate fallback height outside LaunchedEffect
    val fallbackItemHeightPx = with(density) {
        dimensionResource(R.dimen.lyric_line_height).value.sp.roundToPx()
    }

    /* ---------- detect user scroll: pause centre‑lock --------------------------- */
    var userScrolling by remember { mutableStateOf(false) }
    LaunchedEffect(listState.isScrollInProgress, isProgrammaticScrollInProgress) {
        if (listState.isScrollInProgress && !isProgrammaticScrollInProgress) {
            userScrolling = true
            lockEnabled = false                    // reset lock when user interferes
        } else if (userScrolling) {
            delay(1500)                            // small grace period
            userScrolling = false
        }
    }

    /* ---------- auto‑scroll whenever activeLineIndex changes -------------------- */
    LaunchedEffect(activeLineIndex, lockEnabled, userScrolling) { // Removed lineHeightPx from keys
        // Early exit conditions
        if (activeLineIndex < 0 || userScrolling) {
            return@LaunchedEffect
        }

        val layoutInfo = listState.layoutInfo

        // Wait if layout is not ready but list is not empty
        if (layoutInfo.visibleItemsInfo.isEmpty() && layoutInfo.totalItemsCount > 0) {
            return@LaunchedEffect
        }

        if (layoutInfo.totalItemsCount == 0) {
             return@LaunchedEffect
         }


        val vpHeight = layoutInfo.viewportSize.height

        if (vpHeight <= 0) {
            return@LaunchedEffect
        }
        val viewportHalfHeight = vpHeight / 2f

        // --- Try to find the target item in the current layout ---
        val targetItemInfo = layoutInfo.visibleItemsInfo.find { it.index == activeLineIndex }
        val targetItemHeight = targetItemInfo?.size ?: -1 // Get actual height if visible, else -1

        // --- Calculate desired center offset (dynamically if item visible) ---
        val itemHeightForOffsetCalc: Int = if (targetItemHeight > 0) {
            targetItemHeight
        } else {
            // Fallback if item not visible/measured yet
            fallbackItemHeightPx
        }

        if (itemHeightForOffsetCalc <= 0) {
           return@LaunchedEffect
        }

        val centerOffset = (viewportHalfHeight - itemHeightForOffsetCalc / 2f)
            .roundToInt()
            .coerceAtLeast(0) // Ensure offset isn't negative

        // --- Scrolling Logic ---

        // Phase 2: Lock enabled - scroll to top to ensure visibility
        if (lockEnabled) {
            try {
                isProgrammaticScrollInProgress = true
                val targetOffset = -centerOffset
                listState.animateScrollToItem(index = activeLineIndex, scrollOffset = targetOffset)
            } catch (e: Exception) {
                 Timber.tag("LyricsScroll").e(e, "DEBUG_FLOW: LyricsDisplay: Exception during scrollToItem (Phase-2)")
            } finally {
                isProgrammaticScrollInProgress = false
            }
            return@LaunchedEffect // Done with phase 2
        }

        // Phase 1: Lock not enabled
        // Item not visible - scroll to bring it into view (using offset 0, as we can't calculate center yet)
        if (targetItemInfo == null) {
            try {
                isProgrammaticScrollInProgress = true
                listState.safeAnimateScrollToItem(
                    index = activeLineIndex,
                    offset = 0, // Scroll to top first to make it visible
                    logContext = "Phase-1, item not visible"
                )
            } finally {
                isProgrammaticScrollInProgress = false
            }
            // Return early, the next trigger will handle potential centering if it crosses midpoint
            return@LaunchedEffect
        }

        // Recalculate item center based on *actual* current offset and size
        val currentItemCenter = targetItemInfo.offset + targetItemInfo.size / 2f

        // Item crossed midpoint - enable lock
        if (currentItemCenter > viewportHalfHeight) {
            lockEnabled = true // Enable lock only
            return@LaunchedEffect // Done with phase 1 actions

        }
    }


    /* ---------- UI -------------------------------------------------------------- */
    BoxWithConstraints(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        val bottomPadding = remember(constraints.maxHeight) {
            with(density) { (constraints.maxHeight / 2f).toDp() }
        }

        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = bottomPadding),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (lrcLines.isEmpty()) {
                item {
                    Box(
                        modifier = Modifier
                            .fillParentMaxSize()
                            .padding(vertical = 32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_lyrics_available),
                            textAlign = TextAlign.Center,
                            color = colorResource(R.color.lyric_line_placeholder_color)
                        )
                    }
                }
            } else {
                itemsIndexed(
                    items = linesToDisplay,
                    key = { _, pair -> pair.first } // timestamp as key
                ) { index, (_, line) ->
                    LyricRow(
                        text = line,
                        isActive = index == activeLineIndex
                    )
                }
            }
        }
    }
}

@Composable
private fun LyricRow(text: String, isActive: Boolean) {
    val colour = colorResource(
        id = if (isActive) R.color.white else R.color.white
    )

    val alpha = if (isActive) 1f else 0.2f
    val weight = if (isActive) FontWeight.Bold else FontWeight.Normal
    val fontSize = dimensionResource(R.dimen.lyric_line_inactive_font_size).value.sp
    val lineHeight = dimensionResource(R.dimen.lyric_line_height).value.sp

    Text(
        text = text,
        color = colour,
        fontSize = fontSize,
        lineHeight = lineHeight,
        fontWeight = weight,
        textAlign = TextAlign.Center,
        modifier = Modifier
            .fillMaxWidth()
            .alpha(alpha)
    )
}
