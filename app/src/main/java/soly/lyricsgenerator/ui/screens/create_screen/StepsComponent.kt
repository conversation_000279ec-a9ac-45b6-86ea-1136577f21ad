package soly.lyricsgenerator.ui.screens.create_screen

import androidx.activity.result.ActivityResultLauncher
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.MutableStateFlow
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.screens.create_screen.utils.getStepText
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel

@Composable
fun StepsComponent(
    modifier: Modifier = Modifier,
    isSongSelected: <PERSON><PERSON><PERSON>,
    isLyricsAdded: <PERSON><PERSON><PERSON>,
    allStepsCompleted: <PERSON><PERSON><PERSON>,
    showDialog: MutableStateFlow<Boolean>,
    showSongInfo: MutableState<Boolean>,
    showLyricsInfo: MutableState<Boolean>,
    showStartInfo: MutableState<Boolean>,
    showIncompleteStepsDialog: MutableState<Boolean>,
    showStepsComposable: MutableStateFlow<Boolean>,
    showStartButton: MutableStateFlow<Boolean>,
    upperLineProgressFirst: Animatable<Float, AnimationVector1D>,
    lowerLineProgressFirst: Animatable<Float, AnimationVector1D>,
    upperLineProgressSecond: Animatable<Float, AnimationVector1D>,
    lowerLineProgressSecond: Animatable<Float, AnimationVector1D>,
    upperLineProgressThird: Animatable<Float, AnimationVector1D>,
    lowerLineProgressThird: Animatable<Float, AnimationVector1D>,
    drawSecondItemUpperLine: MutableState<Boolean>,
    drawThirdItemUpperLine: MutableState<Boolean>,
    onAddLyricsClicked: () -> Unit = {},
    sharedViewModel: SharedViewModel? = null
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 32.dp, vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.steps_title),
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        StepRow(
            stepNumber = 1,
            isCompleted = isSongSelected,
            onButtonClick = {
                showDialog.value = true
            },
            onInfoClick = { showSongInfo.value = !showSongInfo.value },
            infoText = stringResource(R.string.step_info_choose_song),
            stepText = stringResource(getStepText(1)),
            showInfoText = showSongInfo.value,
            upperLineProgress = upperLineProgressFirst,
            lowerLineProgress = lowerLineProgressFirst
        )

        StepRow(
            stepNumber = 2,
            isCompleted = isLyricsAdded,
            onButtonClick = {
                onAddLyricsClicked()
            },
            onInfoClick = { showLyricsInfo.value = !showLyricsInfo.value },
            infoText = stringResource(R.string.step_info_add_lyrics),
            stepText = stringResource(getStepText(2)),
            showInfoText = showLyricsInfo.value,
            drawUpperLineAnimated = drawSecondItemUpperLine.value,
            upperLineProgress = upperLineProgressSecond,
            lowerLineProgress = lowerLineProgressSecond
        )

        StepRow(
            stepNumber = 3,
            isCompleted = allStepsCompleted,
            onButtonClick = {
                if (allStepsCompleted) {
                    // Set post-start screen to true in the SharedViewModel when all steps are completed
                    sharedViewModel?.setPostStartScreen(true) 
                    // Hide the start button since we're now creating the lrc
                    showStartButton.value = false
                } else {
                    showIncompleteStepsDialog.value = true
                }
            },
            onInfoClick = { showStartInfo.value = !showStartInfo.value },
            infoText = stringResource(R.string.step_info_start_creating),
            stepText = stringResource(getStepText(3)),
            showInfoText = showStartInfo.value,
            drawUpperLineAnimated = drawThirdItemUpperLine.value,
            upperLineProgress = upperLineProgressThird,
            lowerLineProgress = lowerLineProgressThird
        )
    }
}
