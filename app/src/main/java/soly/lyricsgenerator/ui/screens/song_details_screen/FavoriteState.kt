package soly.lyricsgenerator.ui.screens.song_details_screen

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.theme.InteractiveAccent

sealed class FavoriteState {
    abstract fun getIcon(): ImageVector
    abstract fun getTint(): Color
    
    @Composable
    abstract fun getContentDescription(): String
    
    @Composable
    abstract fun getMenuText(): String
    
    data object NotFavorite : FavoriteState() {
        override fun getIcon(): ImageVector = Icons.Default.FavoriteBorder
        override fun getTint(): Color = Color.White
        
        @Composable
        override fun getContentDescription(): String = stringResource(R.string.content_desc_add_to_favorites)
        
        @Composable
        override fun getMenuText(): String = stringResource(R.string.content_desc_add_to_favorites)
    }
    
    data object Favorite : FavoriteState() {
        override fun getIcon(): ImageVector = Icons.Default.Favorite
        override fun getTint(): Color = InteractiveAccent
        
        @Composable
        override fun getContentDescription(): String = stringResource(R.string.content_desc_remove_from_favorites)
        
        @Composable
        override fun getMenuText(): String = stringResource(R.string.content_desc_remove_from_favorites)
    }
    
    data object Loading : FavoriteState() {
        override fun getIcon(): ImageVector = Icons.Default.FavoriteBorder
        override fun getTint(): Color = InteractiveAccent.copy(alpha = 0.5f)
        
        @Composable
        override fun getContentDescription(): String = stringResource(R.string.content_desc_loading_favorite_state)
        
        @Composable
        override fun getMenuText(): String = stringResource(R.string.content_desc_add_to_favorites)
    }
}