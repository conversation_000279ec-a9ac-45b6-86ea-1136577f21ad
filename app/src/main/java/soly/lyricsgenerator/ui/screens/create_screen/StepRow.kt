package soly.lyricsgenerator.ui.screens.create_screen

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.ui.theme.Purple80

@Composable
fun StepRow(
    stepNumber: Int,
    isCompleted: Boolean,
    onButtonClick: () -> Unit,
    onInfoClick: () -> Unit,
    infoText: String,
    stepText: String,
    showInfoText: Boolean,
    drawUpperLineAnimated: Boolean = false,
    upperLineProgress: Animatable<Float, AnimationVector1D>,
    lowerLineProgress: Animatable<Float, AnimationVector1D>
) {
    var buttonHeight by remember { mutableStateOf(0.dp) }
    val density = LocalDensity.current

    Box {
        Row(
            verticalAlignment = Alignment.Top,
            horizontalArrangement = Arrangement.Start,
            modifier = Modifier.fillMaxWidth()
        ) {
            StepIndicator(
                stepNumber = stepNumber,
                isCompleted = isCompleted,
                buttonHalfHeight = buttonHeight,
                modifier = Modifier,
                drawUpperLineAnimated = drawUpperLineAnimated,
                upperLineProgress = upperLineProgress,
                lowerLineProgress = lowerLineProgress
            )
            Spacer(modifier = Modifier.width(16.dp))
            Button(
                onClick = onButtonClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Purple80,
                    contentColor = Color.Black // Black text for good contrast with Purple80
                ),
                modifier = Modifier
                    .weight(1f)
                    .onGloballyPositioned { coordinates ->
                        buttonHeight = with(density) {
                            (coordinates.size.height.toDp().div(2) - 12.dp).coerceAtLeast(0.dp)
                        }
                    }
            ) {
                Text(stepText)
            }
            IconButton(onClick = onInfoClick) {
                Icon(Icons.Default.Info, "Info")
            }
        }

        if (showInfoText) {
            Text(
                text = infoText,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier
                    .padding(start = 40.dp)
                    .padding(top = 48.dp)
            )
        }
    }
} 