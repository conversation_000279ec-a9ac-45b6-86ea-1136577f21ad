package soly.lyricsgenerator.ui.screens.settings_screen

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.Divider
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.ui.screens.settings_screen.components.ChangelogSection
import soly.lyricsgenerator.ui.theme.LyricsGeneratorTheme
import soly.lyricsgenerator.ui.screens.components.FaqSection
import timber.log.Timber
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.runtime.getValue

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavHostController,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    Timber.tag("DEBUG_FLOW").d("SettingsScreen: Composable entered")
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Log screen open event when the composable enters the composition
    LaunchedEffect(Unit) {
        Timber.tag("DEBUG_FLOW").d("SettingsScreen: LaunchedEffect triggered for screen open log")
        viewModel.logSettingsScreenOpened()
    }

    // Assuming you have a theme defined, wrap content in it for consistency
    LyricsGeneratorTheme {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(brush = AppGradientBrush)
                .padding(16.dp), // Add some padding
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.Top // Align items to the top
        ) {
            item {
                // Privacy Policy Card
                SettingsItemCard(
                    text = stringResource(id = R.string.settings_privacy_policy),
                    onClick = {
                        Timber.tag("DEBUG_FLOW").d("SettingsScreen: Privacy Policy clicked")
                        val url = "https://gerrix90.github.io/SwD-Policy/privacy_policy_lyrics_generator.html"
                        val intent = Intent(Intent.ACTION_VIEW).apply {
                            data = Uri.parse(url)
                        }
                        try {
                           viewModel.logPrivacyPolicyOpened() // Log event
                           context.startActivity(intent)
                        } catch (e: Exception) {
                            Timber.tag("DEBUG_FLOW").e(e, "SettingsScreen: Failed to open browser for URL: %s", url)
                            // Optionally show a toast or error message to the user
                        }
                    }
                )
            }

            item {
                Divider(
                    color = Color.White.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                // Contact Us Card
                SettingsItemCard(
                    text = stringResource(id = R.string.settings_contact_us),
                    onClick = {
                        Timber.tag("DEBUG_FLOW").d("SettingsScreen: Contact Us clicked")
                        val email = "<EMAIL>"
                        val intent = Intent(Intent.ACTION_SENDTO).apply {
                            data = Uri.parse("mailto:$email") // Use mailto scheme
                            // You can optionally add subject or body here if needed:
                            // putExtra(Intent.EXTRA_SUBJECT, "Feedback for LyricsGenerator")
                            // putExtra(Intent.EXTRA_TEXT, "Hi,\n\nI would like to share some feedback...")
                        }
                        try {
                            viewModel.logContactUsOpened() // Log event
                            context.startActivity(Intent.createChooser(intent, "Send Email"))
                        } catch (e: Exception) {
                            Timber.tag("DEBUG_FLOW").e(e, "SettingsScreen: Failed to open email client for: %s", email)
                            // Optionally show a toast or error message to the user
                        }
                    }
                )
            }

            item {
                Divider(
                    color = Color.White.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                // FAQ Section
                FaqSection(
                    faqItems = uiState.faqItems,
                    onFaqItemExpanded = { faqItemId ->
                        viewModel.logFaqItemExpanded(faqItemId)
                    },
                    onFaqItemCollapsed = { faqItemId ->
                        viewModel.logFaqItemCollapsed(faqItemId)
                    }
                )
            }

            item {
                Divider(
                    color = Color.White.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                // Changelog Section
                ChangelogSection(
                    changelogState = uiState.changelogState,
                    onRetryClicked = {
                        // Reload changelog by reinitializing the ViewModel logic
                        // For simplicity, we can trigger the loadChangelog method
                        // In a real scenario, you might want to add a retry method to ViewModel
                    },
                    onChangelogOpened = {
                        viewModel.logChangelogOpened()
                    },
                    onEntryExpanded = { version ->
                        viewModel.logChangelogEntryExpanded(version)
                    },
                    onEntryCollapsed = { version ->
                        viewModel.logChangelogEntryCollapsed(version)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            item {
                Divider(
                    color = Color.White.copy(alpha = 0.2f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                // App Version Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.1f)
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Text(
                        text = stringResource(id = R.string.settings_app_version, uiState.versionName, uiState.versionCode),
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.8f),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun SettingsItemCard(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        onClick = onClick
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
    }
}