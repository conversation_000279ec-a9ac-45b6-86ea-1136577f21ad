package soly.lyricsgenerator.ui.screens.song_details_screen

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionUseCase
import soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionResult
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import com.google.firebase.analytics.FirebaseAnalytics
import javax.inject.Inject

/**
 * ViewModel for SongDetailsScreen handling overlay-related business logic
 * Following Clean Architecture and MVVM pattern
 */
@HiltViewModel
class SongDetailsViewModel @Inject constructor(
    private val overlayPermissionUseCase: OverlayPermissionUseCase
) : ViewModel() {
    
    /**
     * Check overlay permission using proper architecture layers
     */
    fun checkOverlayPermission(): OverlayPermissionResult {
        val result = overlayPermissionUseCase.checkOverlayPermission()
        
        // Log analytics event for overlay permission check
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SONG_DETAILS_OVERLAY_PERMISSION_CHECKED) {
            param(AnalyticsConstants.Params.PERMISSION_GRANTED, when(result) {
                OverlayPermissionResult.Granted -> "true"
                OverlayPermissionResult.Denied -> "false"
            })
        }
        
        return result
    }
    
    /**
     * Log screen view event for SongDetailsScreen
     */
    fun logScreenView() {
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.SONG_DETAILS_SCREEN)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.SONG_DETAILS_SCREEN + ".kt")
        }
    }
}