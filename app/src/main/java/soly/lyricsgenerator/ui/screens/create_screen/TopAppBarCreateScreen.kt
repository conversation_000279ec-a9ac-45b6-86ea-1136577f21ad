package soly.lyricsgenerator.ui.screens.create_screen

import android.Manifest
import android.app.Activity.RESULT_OK
import android.app.RecoverableSecurityException
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Preview
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Undo
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import createTxtFilePickerLauncher
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.ui.theme.AccentColor
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import timber.log.Timber
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.only
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.material3.NavigationBarDefaults

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopAppBarCreateScreen(
    context: Context,
    createViewModel: CreateViewModel,
    onChangeSong: (() -> Unit)? = null,
    onUndoClicked: (() -> Unit)? = null
) {
    val coroutineScope = rememberCoroutineScope()
    val activity = LocalContext.current as? ComponentActivity
    val sharedViewModel: SharedViewModel? = activity?.run { hiltViewModel(viewModelStoreOwner = this) }
    
    var hasWritePermission by remember {
        mutableStateOf(
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_MEDIA_AUDIO
                ) == PackageManager.PERMISSION_GRANTED
            } else {
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            }
        )
    }

    var showDialog by remember { mutableStateOf(false) }
    // Initialize fileName with an empty string that will be updated when the dialog opens
    var fileName by remember { mutableStateOf("lyrics") }
    var showEmptyFileDialog by remember { mutableStateOf(false) }
    var showExitDialog by remember { mutableStateOf(false) }
    var showOptionsMenu by remember { mutableStateOf(false) }
    
    // Add a state for showing the bottom sheet file picker
    var showBottomSheetFilePicker by remember { mutableStateOf(false) }
    val bottomSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false)
    
    // Create the file picker launcher
    val txtFilePickerLauncher = createTxtFilePickerLauncher(
        context = context,
        coroutineScope = coroutineScope,
        bottomSheetState = bottomSheetState,
        onTxtLinesParsed = { parsedLines, fileName ->
            Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: New lyric file selected: $fileName")
            createViewModel.pickedTextFileName.value = fileName
            createViewModel.lrcKeyValuePairs.value = parsedLines
            // Set the unsaved changes flag when new lyrics are loaded
            sharedViewModel?.setUnsavedChanges(true)
            // Hide the bottom sheet
            showBottomSheetFilePicker = false
            // Hide step indicator when changing lyrics
            createViewModel.hideStepsIndicator()
        }
    )

    val requestPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        hasWritePermission = isGranted
    }

    val intentSenderLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            coroutineScope.launch {
                val fileNameWithExtension = if (fileName.endsWith(".lrc")) fileName else "$fileName.lrc"
                val isFileSaved = createViewModel.saveLrcFile(fileNameWithExtension)
                Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: File saved via intent sender: $isFileSaved")
                if (isFileSaved) {
                    // Clear unsaved changes flag after successful save
                    sharedViewModel?.setUnsavedChanges(false)
                    // Set sync initialized flag for Files screen refresh fallback
                    sharedViewModel?.setSyncInitialized(true)
                    // Trigger Files screen silent refresh after successful save (no pull indicator)
                    Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: Triggering Files screen silent refresh after intent sender save")
                    sharedViewModel?.refreshFilesSilently()
                }
            }
        }
    }

    if (showEmptyFileDialog) {
        AlertDialog(
            onDismissRequest = { showEmptyFileDialog = false },
            title = { Text(stringResource(R.string.empty_file_warning)) },
            text = { Text(stringResource(R.string.empty_file_message)) },
            confirmButton = {
                TextButton(onClick = { showEmptyFileDialog = false }) {
                    Text(stringResource(R.string.ok))
                }
            }
        )
    }

    if (showExitDialog) {
        AlertDialog(
            onDismissRequest = { showExitDialog = false },
            title = { Text(stringResource(R.string.save_before_exit)) },
            text = { Text(stringResource(R.string.save_lyric_prompt)) },
            confirmButton = {
                TextButton(onClick = {
                    showExitDialog = false
                    if (createViewModel.lrcKeyValuePairs.value.isEmpty()) {
                        showEmptyFileDialog = true
                    } else {
                        showDialog = true
                    }
                }) {
                    Text(stringResource(R.string.save_and_exit))
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    showExitDialog = false
                    // Here you would add your exit logic
                    // For now, just logging the action
                    Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: User chose to exit without saving")
                }) {
                    Text(stringResource(R.string.keep_syncing))
                }
            }
        )
    }

    if (showDialog) {
        // Get the default filename from the ViewModel
        val initialFileName = remember { createViewModel.getDefaultLrcFilename() }
        
        // Update fileName with initialFileName when dialog is first shown
        LaunchedEffect(showDialog) {
            if (showDialog && fileName == "lyrics") {
                fileName = initialFileName
            }
        }
        
        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = { Text(stringResource(R.string.enter_file_name)) },
            text = {
                Row {
                    TextField(
                        value = fileName.removeSuffix(".lrc"),
                        onValueChange = { fileName = it.removeSuffix(".lrc") },
                        label = { Text(stringResource(R.string.file_name)) },
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = ".lrc",
                        modifier = Modifier
                            .align(Alignment.Bottom)
                            .padding(start = 8.dp)
                    )
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    showDialog = false
                    if (createViewModel.lrcKeyValuePairs.value.isEmpty()) {
                        showEmptyFileDialog = true
                    } else if (hasWritePermission) {
                        coroutineScope.launch {
                            try {
                                val fileNameWithExtension = if (fileName.endsWith(".lrc")) fileName else "$fileName.lrc"
                                val isFileSaved = createViewModel.saveLrcFile(fileNameWithExtension)
                                Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: File saved: $isFileSaved")
                                if (isFileSaved) {
                                    // Clear unsaved changes flag after successful save
                                    sharedViewModel?.setUnsavedChanges(false)
                                    // Set sync initialized flag for Files screen refresh fallback
                                    sharedViewModel?.setSyncInitialized(true)
                                    // Trigger Files screen silent refresh after successful save (no pull indicator)
                                    Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: Triggering Files screen silent refresh after LRC save")
                                    sharedViewModel?.refreshFilesSilently()
                                }
                            } catch (e: Exception) {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && e is RecoverableSecurityException) {
                                    val intentSender = e.userAction.actionIntent.intentSender
                                    intentSenderLauncher.launch(
                                        IntentSenderRequest.Builder(intentSender).build()
                                    )
                                } else {
                                    Toast.makeText(
                                        context,
                                        "An error occurred while saving the file: ${e.localizedMessage}",
                                        Toast.LENGTH_LONG
                                    ).show()
                                    e.printStackTrace()
                                }
                            }
                        }
                    } else {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            requestPermissionLauncher.launch(Manifest.permission.READ_MEDIA_AUDIO)
                        } else {
                            requestPermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        }
                    }
                }) {
                    Text(stringResource(R.string.save))
                }
            },
            dismissButton = {
                TextButton(onClick = { showDialog = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    val displayButtons = createViewModel.isSongAndFileSelected()

    TopAppBar(
        title = { },
        actions = {
            if (displayButtons) {
                // Save button - keep visible
                IconButton(onClick = {

                    if (createViewModel.lrcKeyValuePairs.value.isEmpty()) {
                        showEmptyFileDialog = true
                    } else {
                        showDialog = true
                    }
                }) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = stringResource(R.string.save),
                        tint = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                }

                // 3-dots menu with other options
                Box {
                    IconButton(onClick = { 
                        // Log 3-dots menu clicked analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_MORE_OPTIONS_CLICKED) {
                            param(AnalyticsConstants.Params.SOURCE, "top_app_bar_button")
                            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                        }
                        
                        showOptionsMenu = true 
                    }) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = stringResource(R.string.content_desc_more_options),
                            tint = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showOptionsMenu,
                        onDismissRequest = { showOptionsMenu = false },
                        offset = DpOffset(x = 0.dp, y = 4.dp)
                    ) {
                        // 1. Change Song
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.change_song)) },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_change_song),
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                showOptionsMenu = false
                                
                                // Log change song clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_SONG_CHANGE_CLICKED) {
                                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Values.SOURCE_DROPDOWN_MENU)
                                    param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                                }
                                
                                createViewModel.hideStepsIndicator()
                                onChangeSong?.invoke()
                            }
                        )
                        
                        // 2. Change Text
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.change_lyric)) },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_change_lyrics),
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                showOptionsMenu = false
                                
                                // Log change text clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_TEXT_CHANGE_CLICKED) {
                                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Values.SOURCE_DROPDOWN_MENU)
                                    param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                                }
                                
                                Timber.tag("DEBUG_FLOW").d("TopAppBarCreateScreen: Change lyric button clicked, launching file picker directly")
                                if (createViewModel.pickedTextFileName.value != null) {
                                    createViewModel.hideStepsIndicator()
                                }
                                txtFilePickerLauncher.launch(arrayOf("text/plain", "*/*"))
                            }
                        )
                        
                        // 3. Preview
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.content_desc_preview)) },
                            leadingIcon = {
                                val showPreview by createViewModel.showPreview.collectAsState()
                                Icon(
                                    imageVector = Icons.Default.Preview,
                                    contentDescription = null,
                                    tint = if (showPreview) AccentColor else MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                showOptionsMenu = false
                                createViewModel.switchPreview()
                            }
                        )
                        
                        // 4. Undo All
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.undo_sync_confirmation_title)) },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Undo,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            },
                            onClick = {
                                showOptionsMenu = false
                                
                                // Log undo clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_UNDO_CLICKED) {
                                    param(AnalyticsConstants.Params.SOURCE, AnalyticsConstants.Values.SOURCE_DROPDOWN_MENU)
                                    param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.CREATE_SCREEN)
                                }
                                
                                onUndoClicked?.invoke()
                            }
                        )
                    }
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surfaceColorAtElevation(NavigationBarDefaults.Elevation)
        ),
        windowInsets = TopAppBarDefaults.windowInsets.only(
            WindowInsetsSides.Horizontal + WindowInsetsSides.Bottom
        )
    )
}
