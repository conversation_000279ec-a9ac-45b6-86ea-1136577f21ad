package soly.lyricsgenerator.ui.screens.create_screen

object ScreenStateHolder {
    var isCreateScreenActive: Boolean = false
        set(value) {
            field = value
            onStateChangedCallback?.invoke()
        }
    
    var isPreviewModeActive: Boolean = false
        set(value) {
            field = value
            onStateChangedCallback?.invoke()
        }
    
    private var onStateChangedCallback: (() -> Unit)? = null
    
    /**
     * Returns true if the screen should be kept on (no dimming/timeout)
     * This happens when user is in Create Screen or Preview mode
     */
    fun shouldKeepScreenOn(): Boolean {
        return isCreateScreenActive || isPreviewModeActive
    }
    
    /**
     * Sets the callback to be invoked when screen state changes
     */
    fun setOnStateChangedCallback(callback: (() -> Unit)?) {
        onStateChangedCallback = callback
    }
} 