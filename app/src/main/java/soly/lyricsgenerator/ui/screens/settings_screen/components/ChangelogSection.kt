package soly.lyricsgenerator.ui.screens.settings_screen.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.History
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.ChangelogEntry
import soly.lyricsgenerator.domain.model.ChangelogItem
import soly.lyricsgenerator.domain.model.ChangelogCategory
import soly.lyricsgenerator.ui.screens.settings_screen.ChangelogState

/**
 * Changelog component with same design pattern as FAQ
 * Uses transparent white cards and +90° arrow rotation
 */
@Composable
fun ChangelogSection(
    changelogState: ChangelogState,
    onRetryClicked: () -> Unit,
    onChangelogOpened: () -> Unit,
    onEntryExpanded: (String) -> Unit,
    onEntryCollapsed: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var isSectionExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // Changelog Header - Clickable to expand/collapse
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        isSectionExpanded = !isSectionExpanded
                        onChangelogOpened()
                    }
                    .padding(16.dp)
                    .semantics {
                        contentDescription = if (isSectionExpanded) {
                            "Collapse changelog section"
                        } else {
                            "Expand changelog section"
                        }
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.changelog_title),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White
                )
                
                Icon(
                    imageVector = if (isSectionExpanded) {
                        Icons.Default.ExpandMore // Down arrow (↓)
                    } else {
                        Icons.Default.ExpandLess // Right arrow (→)
                    },
                    contentDescription = if (isSectionExpanded) {
                        stringResource(R.string.changelog_collapse)
                    } else {
                        stringResource(R.string.changelog_expand)
                    },
                    tint = Color.White,
                    modifier = Modifier
                        .size(20.dp)
                        .rotate(if (isSectionExpanded) 0f else 90f) // +90f for right arrow
                )
            }

            // Changelog Content with Animation
            AnimatedVisibility(
                visible = isSectionExpanded,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(
                    animationSpec = tween(durationMillis = 300)
                ),
                exit = shrinkVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeOut(
                    animationSpec = tween(durationMillis = 300)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp, bottom = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Content based on state using polymorphism
                    when {
                        changelogState.isLoading() -> LoadingContent()
                        changelogState.hasData() -> {
                            changelogState.getEntries().forEach { entry ->
                                ChangelogEntryCard(
                                    entry = entry,
                                    onExpanded = { onEntryExpanded(entry.version) },
                                    onCollapsed = { onEntryCollapsed(entry.version) }
                                )
                            }
                        }
                        else -> ErrorContent(onRetryClicked = onRetryClicked)
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                strokeWidth = 2.dp,
                color = Color.White
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = stringResource(R.string.changelog_loading),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
    }
}

@Composable
private fun ErrorContent(onRetryClicked: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.changelog_error),
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.8f)
        )
        Spacer(modifier = Modifier.height(8.dp))
        TextButton(
            onClick = onRetryClicked,
            colors = ButtonDefaults.textButtonColors(
                contentColor = Color.White
            )
        ) {
            Text(stringResource(R.string.changelog_retry))
        }
    }
}

@Composable
private fun ChangelogEntryCard(
    entry: ChangelogEntry,
    onExpanded: () -> Unit,
    onCollapsed: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.08f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // Version Row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        isExpanded = !isExpanded
                        if (isExpanded) {
                            onExpanded()
                        } else {
                            onCollapsed()
                        }
                    }
                    .padding(16.dp)
                    .semantics {
                        contentDescription = if (isExpanded) {
                            "Collapse changelog entry"
                        } else {
                            "Expand changelog entry"
                        }
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.History,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.changelog_version_prefix) + " ${entry.version}",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.White
                    )
                    Text(
                        text = entry.date,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
                
                IconButton(
                    onClick = {
                        isExpanded = !isExpanded
                        if (isExpanded) {
                            onExpanded()
                        } else {
                            onCollapsed()
                        }
                    },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = if (isExpanded) {
                            Icons.Default.ExpandMore // Down arrow (↓)
                        } else {
                            Icons.Default.ExpandLess // Right arrow (→)
                        },
                        contentDescription = if (isExpanded) {
                            stringResource(R.string.changelog_collapse)
                        } else {
                            stringResource(R.string.changelog_expand)
                        },
                        tint = Color.White.copy(alpha = 0.8f),
                        modifier = Modifier
                            .size(20.dp)
                            .rotate(if (isExpanded) 0f else 90f) // +90f for right arrow
                    )
                }
            }
            
            // Changes Section with Animation
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = tween(durationMillis = 300)
                ) + fadeIn(
                    animationSpec = tween(durationMillis = 300)
                ),
                exit = shrinkVertically(
                    animationSpec = tween(durationMillis = 300)
                ) + fadeOut(
                    animationSpec = tween(durationMillis = 300)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 48.dp, end = 16.dp, bottom = 16.dp)
                ) {
                    ChangelogCategoriesSection(entry = entry)
                }
            }
        }
    }
}

@Composable
private fun ChangelogCategoriesSection(
    entry: ChangelogEntry,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    // Group changes by category
    val changesByCategory = entry.changes.groupBy { it.category }
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        changesByCategory.forEach { (category, items) ->
            ChangelogCategorySection(
                category = category,
                items = items,
                context = context
            )
        }
    }
}

@Composable
private fun ChangelogCategorySection(
    category: ChangelogCategory,
    items: List<ChangelogItem>,
    context: android.content.Context,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Category header with emoji and name
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = category.emoji,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text(
                text = category.getDisplayName(context),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color.White.copy(alpha = 0.95f)
            )
        }
        
        Spacer(modifier = Modifier.height(6.dp))
        
        // Category items
        Column(
            modifier = Modifier.padding(start = 24.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items.forEach { item ->
                ChangelogItemRow(item = item)
            }
        }
    }
}

@Composable
private fun ChangelogItemRow(
    item: ChangelogItem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        // Bullet point instead of emoji (since emoji is now in category header)
        Text(
            text = "•",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.7f),
            modifier = Modifier.padding(end = 8.dp)
        )
        
        Text(
            text = item.description,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.9f),
            modifier = Modifier.weight(1f)
        )
    }
}