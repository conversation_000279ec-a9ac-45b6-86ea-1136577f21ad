package soly.lyricsgenerator.ui.screens.tag_editor

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Article
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.focus.onFocusChanged
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.domain.database.model.File
import timber.log.Timber
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.utils.FeatureFlags

/**
 * Tag Editor Screen for editing audio metadata tags.
 * Follows Material 3 design with sealed class state management and polymorphic dispatch.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TagEditorScreen(
    filePath: String,
    batchMode: Boolean = false,
    filePaths: List<String> = emptyList(),
    songData: Song? = null,
    viewModel: TagEditorViewModel = hiltViewModel()
) {
    Timber.tag("DEBUG_FLOW").d("TagEditorScreen: Using ViewModel: ${viewModel.hashCode()}")
    val uiState by viewModel.uiState.collectAsState()
    val batchState by viewModel.batchState.collectAsState()
    val snackbarMessage by viewModel.snackbarMessage.collectAsState()
    
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Initialize the view model
    LaunchedEffect(filePath, batchMode, filePaths, songData) {
        // Log screen opened analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_SCREEN_OPENED) {
            param(AnalyticsConstants.Params.BATCH_MODE, batchMode.toString())
            param(AnalyticsConstants.Params.BATCH_SIZE, if (batchMode) filePaths.size else 1)
            param(AnalyticsConstants.Params.HAS_SONG_DATA, (songData != null).toString())
        }
        
        // Log screen view analytics event
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.TAG_EDITOR)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.TAG_EDITOR_SCREEN)
        }
        
        when {
            batchMode && filePaths.isNotEmpty() -> {
                // Log batch mode initiated
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_BATCH_MODE_INITIATED) {
                    param(AnalyticsConstants.Params.BATCH_SIZE, filePaths.size)
                }
                viewModel.initializeBatchFiles(filePaths)
            }
            songData != null -> {
                Timber.tag("DEBUG_FLOW").d("TagEditorScreen: Initializing with song data: ${songData.title}")
                viewModel.initializeSingleFileWithSong(filePath, songData)
            }
            else -> {
                Timber.tag("DEBUG_FLOW").d("TagEditorScreen: Initializing without song data")
                viewModel.initializeSingleFile(filePath)
            }
        }
    }
    
    // Handle snackbar messages
    LaunchedEffect(snackbarMessage) {
        snackbarMessage?.let { message ->
            // Log snackbar shown analytics event
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SNACKBAR_SHOWN) {
                param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.TAG_EDITOR_SCREEN)
                param(AnalyticsConstants.Params.MESSAGE, message.getMessage())
                param(AnalyticsConstants.Params.ERROR_TYPE, if (message.isError()) "error" else "info")
            }
            
            snackbarHostState.showSnackbar(
                message = message.getMessage(),
                actionLabel = message.getActionText(),
                duration = if (message.isError()) SnackbarDuration.Long else SnackbarDuration.Short
            )
            viewModel.dismissSnackbar()
        }
    }
    
    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        TagEditorContent(
            uiState = uiState,
            batchState = batchState,
            paddingValues = paddingValues,
            onUpdateTag = viewModel::updateTag,
            onNavigateBatch = viewModel::navigateBatch
        )
    }
}


/**
 * Main content of the Tag Editor.
 */
@Composable
fun TagEditorContent(
    uiState: TagEditorUiState,
    batchState: BatchEditingState,
    paddingValues: PaddingValues,
    onUpdateTag: (String, String) -> Unit,
    onNavigateBatch: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = AppGradientBrush)
    ) {
        // Batch navigation if in batch mode
        if (batchState is BatchEditingState.BatchMode) {
            BatchNavigationRow(
                batchState = batchState,
                onNavigateBatch = onNavigateBatch,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
            HorizontalDivider()
        }
        
        // Main content based on state
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            when {
                uiState.isLoading() -> {
                    LoadingContent()
                }
                uiState.getErrorMessage() != null -> {
                    ErrorContent(
                        errorMessage = uiState.getErrorMessage()!!,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                uiState is TagEditorUiState.Loaded -> {
                    TagEditingContent(
                        state = uiState,
                        onUpdateTag = onUpdateTag,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * Batch navigation row for switching between files.
 */
@Composable
fun BatchNavigationRow(
    batchState: BatchEditingState.BatchMode,
    onNavigateBatch: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = { 
                // Log batch previous analytics event
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_BATCH_PREVIOUS_CLICKED) {
                    param(AnalyticsConstants.Params.BATCH_INDEX, batchState.getCurrentFileIndex())
                    param(AnalyticsConstants.Params.BATCH_SIZE, batchState.getTotalFiles())
                }
                onNavigateBatch(-1) 
            },
            enabled = batchState.getCurrentFileIndex() > 0
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = stringResource(R.string.content_desc_previous_file),
                tint = Color.White
            )
        }
        
        Text(
            text = "${batchState.getCurrentFileIndex() + 1} / ${batchState.getTotalFiles()}",
            style = MaterialTheme.typography.bodyLarge,
            color = Color.White
        )
        
        IconButton(
            onClick = { 
                // Log batch next analytics event
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_BATCH_NEXT_CLICKED) {
                    param(AnalyticsConstants.Params.BATCH_INDEX, batchState.getCurrentFileIndex())
                    param(AnalyticsConstants.Params.BATCH_SIZE, batchState.getTotalFiles())
                }
                onNavigateBatch(1) 
            },
            enabled = batchState.getCurrentFileIndex() < batchState.getTotalFiles() - 1
        ) {
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = stringResource(R.string.content_desc_next_file),
                tint = Color.White
            )
        }
    }
}

/**
 * Loading state content.
 */
@Composable
fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator()
            Text(
                text = "Loading audio tags...",
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White
            )
        }
    }
}

/**
 * Error state content.
 */
@Composable
fun ErrorContent(
    errorMessage: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(48.dp)
        )
        Text(
            text = errorMessage,
            style = MaterialTheme.typography.bodyLarge,
            color = Color.White
        )
    }
}

/**
 * Main tag editing content.
 */
@Composable
fun TagEditingContent(
    state: TagEditorUiState.Loaded,
    onUpdateTag: (String, String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(vertical = 16.dp)
    ) {
        item {
            BasicTagsSection(
                tags = state.currentTags,
                validationErrors = state.validationErrors,
                onUpdateTag = onUpdateTag
            )
        }
        
        item {
            AdvancedTagsSection(
                tags = state.currentTags,
                validationErrors = state.validationErrors,
                onUpdateTag = onUpdateTag
            )
        }
        
        if (FeatureFlags.isLyricsSectionEnabled()) {
            item {
                LyricsSection(
                    tags = state.currentTags,
                    onUpdateTag = onUpdateTag,
                    uiState = state
                )
            }
        }
        
    }
}

/**
 * Basic tags section (title, artist, album, etc.).
 */
@Composable
fun BasicTagsSection(
    tags: AudioTag,
    validationErrors: Map<String, String>,
    onUpdateTag: (String, String) -> Unit
) {
    TagSection(
        title = stringResource(R.string.basic_tags),
        icon = Icons.Default.MusicNote
    ) {
        TagTextField(
            label = stringResource(R.string.tag_title),
            value = tags.title ?: "",
            onValueChange = { onUpdateTag("title", it) },
            error = validationErrors["title"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_artist),
            value = tags.artist ?: "",
            onValueChange = { onUpdateTag("artist", it) },
            error = validationErrors["artist"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_album),
            value = tags.album ?: "",
            onValueChange = { onUpdateTag("album", it) },
            error = validationErrors["album"]
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TagTextField(
                label = stringResource(R.string.tag_track),
                value = tags.track ?: "",
                onValueChange = { onUpdateTag("track", it) },
                error = validationErrors["track"],
                modifier = Modifier.weight(1f)
            )
            
            TagTextField(
                label = stringResource(R.string.tag_year),
                value = tags.year ?: "",
                onValueChange = { onUpdateTag("year", it) },
                error = validationErrors["year"],
                modifier = Modifier.weight(1f)
            )
        }
        
        TagTextField(
            label = stringResource(R.string.tag_genre),
            value = tags.genre ?: "",
            onValueChange = { onUpdateTag("genre", it) },
            error = validationErrors["genre"]
        )
    }
}

/**
 * Advanced tags section.
 */
@Composable
fun AdvancedTagsSection(
    tags: AudioTag,
    validationErrors: Map<String, String>,
    onUpdateTag: (String, String) -> Unit
) {
    TagSection(
        title = stringResource(R.string.advanced_tags),
        icon = Icons.Default.Settings
    ) {
        TagTextField(
            label = stringResource(R.string.tag_album_artist),
            value = tags.albumArtist ?: "",
            onValueChange = { onUpdateTag("albumArtist", it) },
            error = validationErrors["albumArtist"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_composer),
            value = tags.composer ?: "",
            onValueChange = { onUpdateTag("composer", it) },
            error = validationErrors["composer"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_disc_number),
            value = tags.discNumber ?: "",
            onValueChange = { onUpdateTag("discNumber", it) },
            error = validationErrors["discNumber"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_comment),
            value = tags.comment ?: "",
            onValueChange = { onUpdateTag("comment", it) },
            error = validationErrors["comment"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_isrc),
            value = tags.isrc ?: "",
            onValueChange = { onUpdateTag("isrc", it) },
            error = validationErrors["isrc"]
        )
        
        TagTextField(
            label = stringResource(R.string.tag_musicbrainz_id),
            value = tags.musicBrainzId ?: "",
            onValueChange = { onUpdateTag("musicBrainzId", it) },
            error = validationErrors["musicBrainzId"]
        )
    }
}

/**
 * Lyrics section with simplified functionality.
 */
@Composable
fun LyricsSection(
    tags: AudioTag,
    onUpdateTag: (String, String) -> Unit,
    uiState: TagEditorUiState.Loaded
) {
    var showInfo by remember { mutableStateOf(false) }
    TagSection(
        title = stringResource(R.string.lyrics_section),
        icon = Icons.Default.Article
    ) {
        // Expandable info section
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showInfo = !showInfo },
            colors = CardDefaults.cardColors(
                containerColor = Color.White.copy(alpha = 0.1f)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Header row with expand/collapse
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.lyrics_info_title),
                        style = MaterialTheme.typography.titleSmall,
                        color = Color.White
                    )
                    Icon(
                        imageVector = if (showInfo) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (showInfo) stringResource(R.string.collapse_info) else stringResource(R.string.expand_info),
                        tint = Color.White
                    )
                }
                
                // Expandable content within the same card
                if (showInfo) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        if (uiState.linkedLrcFiles.isNotEmpty()) {
                            InfoSection(
                                title = stringResource(R.string.lyrics_linked_files_info),
                                description = stringResource(R.string.lyrics_linked_files_explanation)
                            )
                            InfoSection(
                                title = stringResource(R.string.lyrics_sync_button_info),
                                description = stringResource(R.string.lyrics_sync_explanation)
                            )
                            InfoSection(
                                title = stringResource(R.string.lyrics_plain_button_info),
                                description = stringResource(R.string.lyrics_plain_explanation)
                            )
                        }
                        InfoSection(
                            title = stringResource(R.string.lyrics_manual_editing_info),
                            description = stringResource(R.string.lyrics_manual_editing_explanation)
                        )
                        InfoSection(
                            title = stringResource(R.string.lyrics_automatic_embedding_info),
                            description = stringResource(R.string.lyrics_automatic_embedding_explanation)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Linked LRC files section
        if (uiState.linkedLrcFiles.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White.copy(alpha = 0.15f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Column(
                    modifier = Modifier.padding(12.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Linked LRC Files (${uiState.linkedLrcFiles.size})",
                        style = MaterialTheme.typography.titleSmall,
                        color = Color.White
                    )
                    
                    uiState.linkedLrcFiles.forEach { lrcFile ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = lrcFile.fileName,
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.White.copy(alpha = 0.9f),
                                modifier = Modifier.weight(1f)
                            )
                            
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                TextButton(
                                    onClick = { 
                                        // Clear existing and paste LRC content with timestamps (synchronized)
                                        try {
                                            val file = java.io.File(lrcFile.filePath)
                                            if (file.exists()) {
                                                val content = file.readText()
                                                onUpdateTag("lyrics", content)
                                            }
                                        } catch (e: Exception) {
                                            // Handle error silently
                                        }
                                    }
                                ) {
                                    Text("Sync", color = Color.White)
                                }
                                TextButton(
                                    onClick = { 
                                        // Clear existing and paste LRC content without timestamps (plain text)
                                        try {
                                            val file = java.io.File(lrcFile.filePath)
                                            if (file.exists()) {
                                                val content = file.readText()
                                                // Remove LRC timestamps to get plain text
                                                val plainText = content.lines()
                                                    .map { line -> 
                                                        line.replace(Regex("\\[\\d{2}:\\d{2}\\.\\d{2}]\\s*"), "")
                                                    }
                                                    .filter { it.isNotBlank() }
                                                    .joinToString("\n")
                                                onUpdateTag("lyrics", plainText)
                                            }
                                        } catch (e: Exception) {
                                            // Handle error silently
                                        }
                                    }
                                ) {
                                    Text("Plain", color = Color.White)
                                }
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
        }
        
        // Manual lyrics input
        OutlinedTextField(
            value = tags.lyrics ?: "",
            onValueChange = { onUpdateTag("lyrics", it) },
            label = { Text(stringResource(R.string.tag_lyrics), color = Color.White) },
            modifier = Modifier.fillMaxWidth(),
            minLines = 4,
            maxLines = 8,
            colors = TextFieldDefaults.colors(
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White,
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                cursorColor = Color.White,
                focusedIndicatorColor = Color.White,
                unfocusedIndicatorColor = Color.White.copy(alpha = 0.6f),
                focusedLabelColor = Color.White,
                unfocusedLabelColor = Color.White.copy(alpha = 0.6f)
            )
        )
    }
}

/**
 * Reusable tag section with title and icon.
 */
@Composable
fun TagSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White
                )
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White
                )
            }
            content()
        }
    }
}

/**
 * Reusable tag text field with validation.
 */
@Composable
fun TagTextField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    error: String? = null,
    modifier: Modifier = Modifier
) {
    // Create focus state tracker for analytics
    var isFocused by remember { mutableStateOf(false) }
    
    OutlinedTextField(
        value = value,
        onValueChange = { newValue ->
            // Log field edited analytics event
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_FIELD_EDITED) {
                param(AnalyticsConstants.Params.FIELD_NAME, label)
                param(AnalyticsConstants.Params.INPUT_LENGTH, newValue.length)
                param(AnalyticsConstants.Params.TEXT_MODIFIED, (newValue != value).toString())
            }
            onValueChange(newValue)
        },
        label = { Text(label, color = Color.White) },
        modifier = modifier
            .fillMaxWidth()
            .onFocusChanged { focusState ->
                if (focusState.isFocused && !isFocused) {
                    // Field gained focus
                    isFocused = true
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_FIELD_FOCUSED) {
                        param(AnalyticsConstants.Params.FIELD_NAME, label)
                        param(AnalyticsConstants.Params.FIELD_TYPE, "text")
                    }
                } else if (!focusState.isFocused && isFocused) {
                    // Field lost focus
                    isFocused = false
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TAG_EDITOR_FIELD_BLURRED) {
                        param(AnalyticsConstants.Params.FIELD_NAME, label)
                        param(AnalyticsConstants.Params.INPUT_LENGTH, value.length)
                        param(AnalyticsConstants.Params.HAS_ERROR, (error != null).toString())
                    }
                }
            },
        isError = error != null,
        supportingText = error?.let { { Text(it, color = Color.White) } },
        singleLine = true,
        colors = TextFieldDefaults.colors(
            focusedTextColor = Color.White,
            unfocusedTextColor = Color.White,
            focusedContainerColor = Color.Transparent,
            unfocusedContainerColor = Color.Transparent,
            cursorColor = Color.White,
            focusedIndicatorColor = Color.White,
            unfocusedIndicatorColor = Color.White.copy(alpha = 0.6f),
            errorIndicatorColor = Color.White,
            focusedLabelColor = Color.White,
            unfocusedLabelColor = Color.White.copy(alpha = 0.6f),
            errorLabelColor = Color.White
        )
    )
}

/**
 * Component for displaying info sections in the expandable lyrics help.
 */
@Composable
fun InfoSection(
    title: String,
    description: String
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White
        )
        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            color = Color.White.copy(alpha = 0.8f)
        )
    }
}

