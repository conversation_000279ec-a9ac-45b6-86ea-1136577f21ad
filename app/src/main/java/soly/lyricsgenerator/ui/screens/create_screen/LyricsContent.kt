package soly.lyricsgenerator.ui.screens.create_screen

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.screens.components.LyricsDisplay
import soly.lyricsgenerator.ui.screens.create_screen.utils.formatTimestamp
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import timber.log.Timber
import androidx.compose.ui.tooling.preview.Preview

@Composable
fun LyricsContent(
    context: Context,
    showPreview: Boolean,
    songViewModel: CreateViewModel,
    currentPosition: Long,
    musicControllerHeightPx: Int,
    songDuration: Long
) {
     // Remember the LazyListState to control scrolling programmatically
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // Function to scroll to a specific item
    val scrollToItem: (Int) -> Unit = { index ->
        coroutineScope.launch {
            listState.animateScrollToItem(index)
        }
    }
    
    // Get the line to show from the ViewModel
    val lineToShowIndex by songViewModel.lineToShowIndex.collectAsState()
    // Get the index of the last line with a positive timestamp
    val lastPositiveTimestampIndex by songViewModel.lastPositiveTimestampIndex.collectAsState()
    
    // Collect the auto-scroll state outside the LaunchedEffect
    val isAutoScrollEnabled by songViewModel.isAutoScrollEnabled.collectAsState()
    
    // Report visibility changes of the last positive item to the ViewModel (used only to re-enable scroll)
    LaunchedEffect(listState, lastPositiveTimestampIndex) {
        snapshotFlow { lastPositiveTimestampIndex } // Trigger when index changes
            .collect { lastIndex ->
                snapshotFlow { listState.layoutInfo } // Then monitor layout
                    .collect { layoutInfo ->
                        val isVisible = lastIndex == null || layoutInfo.visibleItemsInfo.any { it.index == lastIndex }
                        // Report visibility, ViewModel decides if it needs to re-enable scroll
                        Timber.tag("DEBUG_FLOW").d("LyricsContent (Visibility Reporter): Reporting visibility for lastIndex $lastIndex: $isVisible")
                        songViewModel.reportLastItemVisibility(isVisible)
                    }
            }
    }

    // Detect manual upward scroll to disable auto-scrolling
    LaunchedEffect(listState, isAutoScrollEnabled) {
        var previousFirstVisibleItemIndex = listState.firstVisibleItemIndex
        var previousFirstVisibleItemScrollOffset = listState.firstVisibleItemScrollOffset

        snapshotFlow { listState.isScrollInProgress } // Monitor scroll state changes
            .collect { isScrolling ->
                if (isScrolling) {
                    val currentIndex = listState.firstVisibleItemIndex
                    val currentOffset = listState.firstVisibleItemScrollOffset

                    // Check for upward movement compared to the last known position *before* this scroll started
                    val isScrollingUp = currentIndex < previousFirstVisibleItemIndex ||
                                           (currentIndex == previousFirstVisibleItemIndex && currentOffset < previousFirstVisibleItemScrollOffset)
                    
                    // Check if autoscroll is currently enabled in the viewmodel state
                    val autoScrollCurrentlyEnabled = songViewModel.isAutoScrollEnabled.value

                    if (isScrollingUp && autoScrollCurrentlyEnabled) {
                         Timber.tag("DEBUG_FLOW").d("LyricsContent (Manual Scroll Detector): Manual upward scroll detected while auto-scroll was enabled. Disabling auto-scroll.")
                         songViewModel.disableAutoScroll()
                    }
                } else {
                    // When scrolling stops, update the reference points
                    previousFirstVisibleItemIndex = listState.firstVisibleItemIndex
                    previousFirstVisibleItemScrollOffset = listState.firstVisibleItemScrollOffset
                }
            }
    }
    
    // Scroll to the appropriate line when lineToShowIndex changes, with conditions
    LaunchedEffect(lineToShowIndex) {
        // Read the auto-scroll enable state from the ViewModel
        Timber.tag("DEBUG_FLOW").d("LyricsContent: LaunchedEffect(lineToShowIndex) triggered. lineToShowIndex: $lineToShowIndex, isAutoScrollEnabled: $isAutoScrollEnabled")
        if (isAutoScrollEnabled) {
            lineToShowIndex?.let { targetIndex ->
                Timber.tag("DEBUG_FLOW").d("LyricsContent: -> Performing scroll to target line $targetIndex.")
                scrollToItem(targetIndex)
            }
        } else {
             // Log only when needed, check the actual target index to avoid noise
             lineToShowIndex?.let { targetIndex ->
                 Timber.tag("DEBUG_FLOW").d("LyricsContent: -> Auto-scroll DISABLED (ViewModel state = false). Won't scroll to target index $targetIndex")
             }
        }
    }
    
    // Get the sorted list of lyrics by timestamp
    val lrcKeyValuePairs by songViewModel.lrcKeyValuePairs.collectAsState()
    
    if (showPreview) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = with(LocalDensity.current) { musicControllerHeightPx.toDp() }),
            contentAlignment = Alignment.Center
        ) {
            LyricsDisplay(
                lrcLines = songViewModel.getLrcLinesAsMap(),
                currentTimeMs = currentPosition.toInt(),
            )
        }
    } else {
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = with(LocalDensity.current) { musicControllerHeightPx.toDp() })
        ) {
            items(lrcKeyValuePairs.entries.toList()) { (index, tuple) ->
                val (timestamp, line) = tuple
                // Wrap the Row in a Card
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp, horizontal = 8.dp) // Add padding around the card
                        .clickable { // Move clickable modifier here
                            if (currentPosition <= songDuration) {
                                songViewModel.updateLrcLineTimestamp(
                                    index,
                                    currentPosition
                                )
                            }
                        },
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.corner_radius_small)), // Use dimension resource
                    colors = CardDefaults.cardColors(
                        containerColor = if (index == lineToShowIndex) {
                            MaterialTheme.colorScheme.tertiary.copy(alpha = 0.2f) // Highlight color for current line
                        } else {
                            Color.Transparent // Default transparent background
                        }
                    ),
                    border = BorderStroke(
                        width = dimensionResource(id = R.dimen.border_width_standard),
                        color = if (timestamp != 0L) MaterialTheme.colorScheme.tertiary else Color.Gray.copy(alpha = 0.2f) // Conditional border color
                    )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp, vertical = 4.dp) // Add padding inside the card
                    ) {
                        // Left side with timestamp controls
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            IconButton(onClick = {
                                val newTimestamp = timestamp + 500
                                if (newTimestamp <= songDuration) {
                                    songViewModel.updateLrcLineTimestamp(
                                        index,
                                        newTimestamp
                                    )
                                }
                            }) {
                            Icon(
                                Icons.Default.Add,
                                contentDescription = stringResource(R.string.content_desc_add)
                            )
                        }

                            // Timestamp between buttons
                            Text(
                                text = formatTimestamp(timestamp),
                                color = Color.Gray,
                                modifier = Modifier.padding(vertical = 4.dp, horizontal = 8.dp)
                            )

                            IconButton(onClick = {
                                val newTimestamp = timestamp - 500
                                if (newTimestamp >= 0) {
                                    songViewModel.updateLrcLineTimestamp(
                                        index,
                                        newTimestamp
                                    )
                                }
                            }) {
                                Icon(
                                    Icons.Default.Remove,
                                    contentDescription = stringResource(R.string.content_desc_remove)
                                )
                            }
                        }

                        // Middle with lyrics text
                        Box(modifier = Modifier.weight(1f)) {
                            Text(
                                text = line,
                                color = Color.Gray,
                                modifier = Modifier.padding(start = 8.dp, end = 8.dp)
                            )
                        }

                        // Right side with play button
                        IconButton(onClick = {
                            Timber.tag("DEBUG_FLOW").d("LyricsContent: Playing from timestamp $timestamp")
                            val isPlaying = songViewModel.isPlaying.value
                            val currentSong = songViewModel.currentSongFlow.value
                            val selectedSong = songViewModel.selectedSong.value

                            if (currentSong?.id == selectedSong?.id) {
                                if (isPlaying) {
                                    // If already playing, just seek to the timestamp
                                    songViewModel.seekTo(context, timestamp)
                                } else {
                                    // If paused, seek and resume
                                    songViewModel.seekTo(context, timestamp)
                                    songViewModel.resumeSong(context)
                                }
                            } else {
                                // If different song or nothing loaded, set pending seek and play
                                songViewModel.pendingSeekTo.value = timestamp
                                songViewModel.playSong(context)
                            }
                        }) {
                            Icon(
                                Icons.Default.PlayArrow,
                                contentDescription = stringResource(R.string.content_desc_play_timestamp)
                            )
                        }
                    }
                }
            }
        }
    }
}

// Add the Preview function below
@Preview(showBackground = true)
@Composable
fun LyricsItemPreview() {
    val timestamp = 12345L // Sample timestamp (12.345s)
    val line = "This is a sample lyric line for preview."
    val cornerRadius = dimensionResource(id = R.dimen.corner_radius_small)

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 8.dp),
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent), // Set background to transparent
        border = BorderStroke(dimensionResource(id = R.dimen.border_width_standard), Color.Gray.copy(alpha = 0.2f)) // Use Gray with 20% opacity
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            // Left side with timestamp controls
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                IconButton(onClick = { /* Preview: No action */ }) {
                    Icon(
                        Icons.Default.Add,
                        contentDescription = stringResource(R.string.content_desc_add)
                    )
                }

                Text(
                    text = formatTimestamp(timestamp),
                    color = Color.Gray,
                    modifier = Modifier.padding(vertical = 4.dp, horizontal = 8.dp)
                )

                IconButton(onClick = { /* Preview: No action */ }) {
                    Icon(
                        Icons.Default.Remove,
                        contentDescription = stringResource(R.string.content_desc_remove)
                    )
                }
            }

            // Middle with lyrics text
            Box(modifier = Modifier.weight(1f)) {
                Text(
                    text = line,
                    color = Color.Gray,
                    modifier = Modifier.padding(start = 8.dp, end = 8.dp)
                )
            }

            // Right side with play button
            IconButton(onClick = { /* Preview: No action */ }) {
                Icon(
                    Icons.Default.PlayArrow,
                    contentDescription = stringResource(R.string.content_desc_play_timestamp)
                )
            }
        }
    }
}
