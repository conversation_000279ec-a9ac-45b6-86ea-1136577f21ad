import android.content.Context
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.constants.FileConstants
import soly.lyricsgenerator.ui.screens.components.getFileNameFromUri
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import java.io.File
import java.io.IOException

data class Tuple<A, B>(val first: A, val second: B)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun createTxtFilePickerLauncher(
    context: Context,
    coroutineScope: CoroutineScope,
    bottomSheetState: SheetState,
    onTxtLinesParsed: (Map<Int, Tuple<Long, String>>, String) -> Unit,
    isAudio: Boolean = false,
    viewModel: CreateViewModel? = null
): ActivityResultLauncher<Array<String>> {
    return rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let {
            val contentResolver = context.contentResolver
            val mimeType = contentResolver.getType(uri)

            val uriString = uri.toString().lowercase()
            if (!isAudio && (mimeType == FileConstants.MimeTypes.TEXT_PLAIN || uriString.endsWith(
                    FileConstants.Extensions.TXT
                ) || uriString.endsWith(FileConstants.Extensions.RTF))
            ) {
                try {
                    val inputStream = contentResolver.openInputStream(uri)
                    val fileContent = inputStream?.bufferedReader().use { it?.readText() }

                    val txtFile = File(context.cacheDir, "temp.txt") //TODO: Remove it after use
                    txtFile.writeText(fileContent ?: "")

                    val lines = txtFile.readLines().mapIndexed { index, line ->
                        index to Tuple(0L, line) // Default timestamp set to 0 milliseconds
                    }.toMap()
                    val fileName = getFileNameFromUri(context, uri)
                    onTxtLinesParsed(lines, fileName)

                    coroutineScope.launch {
                        bottomSheetState.hide()
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            } else if (isAudio && mimeType?.startsWith("audio") == true) {
                try {
                    val inputStream = contentResolver.openInputStream(uri)
                    val audioFile = File(context.cacheDir, "temp_audio")
                    inputStream?.use { input ->
                        audioFile.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                    val fileName = getFileNameFromUri(context, uri)

                    Toast.makeText(
                        context,
                        context.getString(R.string.uploading_audio_for_transcription),
                        Toast.LENGTH_SHORT
                    ).show()
                    coroutineScope.launch {
                        try {
                            val parsedLines =
                                viewModel?.transcribeAudioFile(audioFile) ?: emptyMap()
                            onTxtLinesParsed(parsedLines, fileName)
                            Toast.makeText(
                                context,
                                context.getString(R.string.transcription_complete),
                                Toast.LENGTH_SHORT
                            ).show()
                        } catch (e: Exception) {
                            e.printStackTrace()
                            Toast.makeText(
                                context,
                                context.getString(R.string.transcription_failed),
                                Toast.LENGTH_SHORT
                            ).show()
                        } finally {
                            bottomSheetState.hide()
                        }
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    Toast.makeText(
                        context,
                        context.getString(R.string.failed_to_process_audio_file),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } else {
                Toast.makeText(
                    context,
                    if (isAudio) context.getString(R.string.please_select_audio_file) else context.getString(
                        R.string.please_select_txt_or_rtf_file
                    ),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
}
