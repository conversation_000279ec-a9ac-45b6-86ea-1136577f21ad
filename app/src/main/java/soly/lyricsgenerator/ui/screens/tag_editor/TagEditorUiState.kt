package soly.lyricsgenerator.ui.screens.tag_editor

import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.database.model.File

/**
 * Sealed class representing different states of the Tag Editor screen.
 * Uses polymorphic dispatch instead of when expressions following project patterns.
 */
sealed class TagEditorUiState {
    abstract fun isLoading(): Boolean
    abstract fun isEditing(): Boolean
    abstract fun getTags(): AudioTag?
    abstract fun getErrorMessage(): String?
    
    data object Loading : TagEditorUiState() {
        override fun isLoading(): Boolean = true
        override fun isEditing(): Boolean = false
        override fun getTags(): AudioTag? = null
        override fun getErrorMessage(): String? = null
    }
    
    data class Loaded(
        val currentTags: AudioTag,
        val originalTags: AudioTag,
        val filePath: String,
        val fileName: String,
        val supportedFormats: List<String>,
        val supportsEmbeddedLyrics: Boolean,
        val isModified: Boolean = false,
        val validationErrors: Map<String, String> = emptyMap(),
        val linkedLrcFiles: List<File> = emptyList(),
        val songId: Long? = null
    ) : TagEditorUiState() {
        override fun isLoading(): Boolean = false
        override fun isEditing(): Boolean = isModified
        override fun getTags(): AudioTag = currentTags
        override fun getErrorMessage(): String? = null
    }
    
    data class Error(
        val errorText: String,
        val previousTags: AudioTag? = null
    ) : TagEditorUiState() {
        override fun isLoading(): Boolean = false
        override fun isEditing(): Boolean = false
        override fun getTags(): AudioTag? = previousTags
        override fun getErrorMessage(): String = errorText
    }
    
    data class Saving(
        val currentTags: AudioTag,
        val operation: String = "Saving tags..."
    ) : TagEditorUiState() {
        override fun isLoading(): Boolean = true
        override fun isEditing(): Boolean = false
        override fun getTags(): AudioTag = currentTags
        override fun getErrorMessage(): String? = null
    }
}

/**
 * Sealed class representing different batch editing states.
 * Uses polymorphic dispatch for batch operations.
 */
sealed class BatchEditingState {
    abstract fun getCurrentFileIndex(): Int
    abstract fun getTotalFiles(): Int
    abstract fun getCurrentFilePath(): String?
    
    data object NoBatch : BatchEditingState() {
        override fun getCurrentFileIndex(): Int = 0
        override fun getTotalFiles(): Int = 1
        override fun getCurrentFilePath(): String? = null
    }
    
    data class BatchMode(
        val filePaths: List<String>,
        val currentIndex: Int,
        val commonTags: AudioTag,
        val individualTags: Map<String, AudioTag>
    ) : BatchEditingState() {
        override fun getCurrentFileIndex(): Int = currentIndex
        override fun getTotalFiles(): Int = filePaths.size
        override fun getCurrentFilePath(): String? = filePaths.getOrNull(currentIndex)
    }
}

/**
 * Sealed class representing different tag editor actions.
 * Uses polymorphic dispatch instead of when expressions.
 */
sealed class TagEditorAction {
    abstract fun execute(): String
    
    data class UpdateTag(val field: String, val value: String) : TagEditorAction() {
        override fun execute(): String = "Updated $field to: $value"
    }
    
    data object LoadCurrentTags : TagEditorAction() {
        override fun execute(): String = "Loading current tags from file"
    }
    
    data object ClearAllTags : TagEditorAction() {
        override fun execute(): String = "Cleared all tags"
    }
    
    data object SaveTags : TagEditorAction() {
        override fun execute(): String = "Saving tags to file"
    }
    
    data class EmbedLyrics(val lrcContent: String) : TagEditorAction() {
        override fun execute(): String = "Embedding lyrics into audio file"
    }
    
    data object AutoFillFromLrc : TagEditorAction() {
        override fun execute(): String = "Auto-filling tags from LRC file"
    }
    
    data class NavigateBatch(val direction: Int) : TagEditorAction() {
        override fun execute(): String = if (direction > 0) "Next file" else "Previous file"
    }
    
    data class SelectCoverArt(val imageUri: String) : TagEditorAction() {
        override fun execute(): String = "Selected cover art"
    }
    
    data object RemoveCoverArt : TagEditorAction() {
        override fun execute(): String = "Removed cover art"
    }
}

/**
 * Sealed class for validation results.
 * Uses polymorphic dispatch for field validation.
 */
sealed class ValidationResult {
    abstract fun isValid(): Boolean
    abstract fun getErrorMessage(): String?
    
    data object Valid : ValidationResult() {
        override fun isValid(): Boolean = true
        override fun getErrorMessage(): String? = null
    }
    
    data class Invalid(val errorText: String) : ValidationResult() {
        override fun isValid(): Boolean = false
        override fun getErrorMessage(): String = errorText
    }
}

/**
 * Sealed class for different snackbar messages.
 * Uses polymorphic dispatch for message handling.
 */
sealed class SnackbarMessage {
    abstract fun getMessage(): String
    abstract fun getActionText(): String?
    abstract fun isError(): Boolean
    
    data class Success(val text: String, val action: String? = null) : SnackbarMessage() {
        override fun getMessage(): String = text
        override fun getActionText(): String? = action
        override fun isError(): Boolean = false
    }
    
    data class Error(val text: String, val action: String? = null) : SnackbarMessage() {
        override fun getMessage(): String = text
        override fun getActionText(): String? = action
        override fun isError(): Boolean = true
    }
    
    data class Warning(val text: String, val action: String? = null) : SnackbarMessage() {
        override fun getMessage(): String = text
        override fun getActionText(): String? = action
        override fun isError(): Boolean = false
    }
}