package soly.lyricsgenerator.ui.screens.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.SkipNext
import androidx.compose.material.icons.filled.SkipPrevious
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import soly.lyricsgenerator.R
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.foundation.border
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTag
import androidx.compose.ui.semantics.text
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.ui.constants.UITestTags
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.ui.theme.AccentColor
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook

@Composable
fun CompactMusicPlayerController(
    currentSong: Song?,
    currentPosition: Long,
    isPlaying: Boolean,
    isVisible: Boolean,
    onPlayPauseClicked: () -> Unit,
    onNextClicked: () -> Unit,
    onPreviousClicked: () -> Unit,
    onPlayerClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            animationSpec = tween(durationMillis = 300),
            initialOffsetY = { fullHeight -> fullHeight }
        ),
        exit = slideOutVertically(
            animationSpec = tween(durationMillis = 300),
            targetOffsetY = { fullHeight -> fullHeight }
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(72.dp)
                .padding(horizontal = 8.dp, vertical = 4.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(brush = AppGradientBrush)
                .border(
                    width = 1.dp,
                    color = Color.White.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(12.dp)
                )
                .clickable { 
                    // Log compact player clicked analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.COMPACT_PLAYER_CLICKED) {
                        param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                        param(AnalyticsConstants.Params.SONG_TITLE, currentSong?.title ?: "none")
                        param(AnalyticsConstants.Params.IS_PLAYING, isPlaying.toString())
                        param(AnalyticsConstants.Params.COMPONENT_TYPE, "compact_player")
                    }
                    onPlayerClicked() 
                }
                .testTag(UITestTags.COMPACT_MUSIC_PLAYER_CONTROLLER)
        ) {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // Thin progress bar at the top
                if (currentSong != null) {
                    val progress = if (currentSong.duration > 0) {
                        (currentPosition.toFloat() / currentSong.duration.toFloat()).coerceIn(0f, 1f)
                    } else {
                        0f
                    }
                    LinearProgressIndicator(
                        progress = progress,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp),
                        color = Color.White,
                        trackColor = Color.White.copy(alpha = 0.3f)
                    )
                }

                // Main content
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Song info - takes up most space
                    val songText = currentSong?.let { song ->
                        buildString {
                            append(song.title)
                            song.artist.takeIf { it.isNotBlank() }?.let {
                                append(" - ")
                                append(it)
                            }
                        }
                    } ?: stringResource(R.string.no_song_selected)
                    
                    Text(
                        text = songText,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = Color.White,
                        modifier = Modifier
                            .weight(1f)
                            .testTag("compact_player_song_title")
                            .semantics { 
                                testTag = "compact_player_song_title"
                                text = AnnotatedString(songText)
                            }
                    )

                    // Playback controls
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        IconButton(
                            onClick = {
                                // Log previous clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.COMPACT_PLAYER_PREVIOUS_CLICKED) {
                                    param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                    param(AnalyticsConstants.Params.COMPONENT_TYPE, "compact_player")
                                    param(AnalyticsConstants.Params.SOURCE, "compact_player_previous")
                                }
                                onPreviousClicked()
                            },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.SkipPrevious,
                                contentDescription = stringResource(R.string.content_desc_previous),
                                modifier = Modifier.size(20.dp),
                                tint = Color.White
                            )
                        }

                        IconButton(
                            onClick = {
                                // Log play/pause clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.COMPACT_PLAYER_PLAY_PAUSE_CLICKED) {
                                    param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                    param(AnalyticsConstants.Params.COMPONENT_TYPE, "compact_player")
                                    param(AnalyticsConstants.Params.IS_PLAYING, isPlaying.toString())
                                    param(AnalyticsConstants.Params.ACTION, if (isPlaying) "pause" else "play")
                                }
                                onPlayPauseClicked()
                            },
                            modifier = Modifier.size(48.dp)
                        ) {
                            Icon(
                                imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                                contentDescription = if (isPlaying) stringResource(R.string.content_desc_pause) else stringResource(R.string.content_desc_play),
                                modifier = Modifier.size(24.dp),
                                tint = Color.White
                            )
                        }

                        IconButton(
                            onClick = {
                                // Log next clicked analytics
                                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.COMPACT_PLAYER_NEXT_CLICKED) {
                                    param(AnalyticsConstants.Params.SONG_ID, currentSong?.id?.toString() ?: "none")
                                    param(AnalyticsConstants.Params.COMPONENT_TYPE, "compact_player")
                                    param(AnalyticsConstants.Params.SOURCE, "compact_player_next")
                                }
                                onNextClicked()
                            },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.SkipNext,
                                contentDescription = stringResource(R.string.content_desc_next),
                                modifier = Modifier.size(20.dp),
                                tint = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}