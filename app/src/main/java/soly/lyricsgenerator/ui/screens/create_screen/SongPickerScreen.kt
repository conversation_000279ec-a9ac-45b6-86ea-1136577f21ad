package soly.lyricsgenerator.ui.screens.create_screen

import androidx.compose.foundation.clickable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import soly.lyricsgenerator.R
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import com.google.firebase.analytics.FirebaseAnalytics
import timber.log.Timber
import androidx.compose.ui.platform.testTag
import soly.lyricsgenerator.ui.constants.UITestTags
import soly.lyricsgenerator.ui.theme.AppGradientBrush

@Composable
fun SongPickerScreen(
    navController: NavHostController,
    isChangingSong: Boolean = false, // Parameter indicating if this is a song replacement
    linkingMode: Boolean = false // New parameter for linking mode
) {
    val activity = LocalContext.current as androidx.activity.ComponentActivity
    val songViewModel: CreateViewModel = hiltViewModel(viewModelStoreOwner = activity)
    val sharedViewModel: SharedViewModel = hiltViewModel(viewModelStoreOwner = activity)
    
    // Get search state and filtered songs
    val searchQuery by songViewModel.searchQuery.collectAsState()
    val filteredSongsState by songViewModel.filteredSongsFlow.collectAsState()
    val allSongs = songViewModel.songs.collectAsState().value
    
    // Get post-start screen state from SharedViewModel
    val isPostStartScreen by sharedViewModel.isPostStartScreen
    
    // Log song list status
    LaunchedEffect(Unit) {
        Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Using ViewModel instance ${songViewModel.hashCode()}, songs count: ${allSongs.size}")
        Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Current post-start screen state: $isPostStartScreen, isChangingSong: $isChangingSong, linkingMode: $linkingMode")
        
        // Log screen view analytics event
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.SONG_PICKER)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, "SongPickerScreen.kt")
            param(AnalyticsConstants.Params.IS_CHANGING_SONG, isChangingSong.toString())
            param(AnalyticsConstants.Params.LINKING_MODE, linkingMode.toString())
        }
    }

    // Clear search when leaving the screen to avoid inconsistent state
    DisposableEffect(Unit) {
        onDispose {
            Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Clearing search query on screen disposal")
            songViewModel.updateSearchQuery("")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = AppGradientBrush)
            .padding(8.dp)
    ) {
        // Results list
        LazyColumn(modifier = Modifier.testTag(UITestTags.SONG_LIST)) {
            item {
                val displayedSongs = filteredSongsState.songs
                val totalSongs = allSongs.size
                val resultText = if (searchQuery.isEmpty()) {
                    stringResource(R.string.number_of_songs, totalSongs)
                } else {
                    stringResource(R.string.showing_songs_count, displayedSongs.size, totalSongs)
                }
                Text(
                    text = resultText,
                    fontSize = 18.sp,
                    modifier = Modifier.padding(8.dp)
                )
            }

            // Show "no results" message if search is active but no results
            if (searchQuery.isNotEmpty() && filteredSongsState.songs.isEmpty() && allSongs.isNotEmpty()) {
                item {
                    Text(
                        text = stringResource(R.string.no_search_results),
                        fontSize = 16.sp,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            } else {
                items(filteredSongsState.songs) { song ->
                    Text(
                        text = song.title,
                        fontSize = 18.sp,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Song selected: ${song.title}, ID: ${song.id}, linkingMode: $linkingMode")
                                // Reset playback state before changing the song
                                songViewModel.resetPlaybackState()

                                if (linkingMode) {
                                    Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Linking mode - setting song ID ${song.id} in SharedViewModel")
                                    sharedViewModel.setSongIdForLinking(song.id)
                                    // Log song picked for linking event
                                    songViewModel.logSongPickedForLinkingEvent(song.id, song.title)
                                } else {
                                    // Log analytics event for regular song selection
                                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SONG_PICKER_SONG_SELECTED) {
                                        param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                                        param(AnalyticsConstants.Params.SONG_TITLE, song.title ?: AnalyticsConstants.Params.UNKNOWN)
                                        param(AnalyticsConstants.Params.SONG_ARTIST, song.artist ?: AnalyticsConstants.Params.UNKNOWN)
                                        param(AnalyticsConstants.Params.IS_CHANGING_SONG, isChangingSong.toString())
                                    }
                                    
                                    // Set the selected song in CreateViewModel for normal Create flow
                                    songViewModel.setSelectedSong(song)
                                    
                                    // If we're in post-start screen mode, maintain that state regardless of isChangingSong
                                    if (isPostStartScreen) {
                                        Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Maintaining post-start screen state (steps hidden)")
                                        songViewModel.hideStepsIndicator()
                                    } else if (isChangingSong) {
                                        // When replacing a song but not in post-start mode, respect the isChangingSong parameter
                                        Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Keeping steps indicator hidden when replacing song")
                                        songViewModel.hideStepsIndicator()
                                    } else {
                                        // For initial song selection, show the steps
                                        Timber.tag("DEBUG_FLOW").d("SongPickerScreen: Showing steps indicator for initial song selection")
                                        songViewModel.showStepsIndicator()
                                    }
                                }
                                
                                navController.popBackStack()
                            }
                            .padding(vertical = 4.dp, horizontal = 8.dp)
                    )
                }
            }
        }
    }
}