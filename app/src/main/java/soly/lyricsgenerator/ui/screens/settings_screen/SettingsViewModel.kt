package soly.lyricsgenerator.ui.screens.settings_screen

import android.content.Context
import android.content.pm.PackageManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.analytics.FirebaseAnalytics
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.model.FaqItem
import soly.lyricsgenerator.domain.model.ChangelogEntry
import soly.lyricsgenerator.domain.usecase.changelog.GetChangelogUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Sealed class representing different states of changelog data
 * Following the project's pattern of using sealed classes with polymorphism instead of when expressions
 */
sealed class ChangelogState {
    abstract fun isLoading(): Boolean
    abstract fun hasData(): Boolean
    abstract fun getEntries(): List<ChangelogEntry>
    
    data object Loading : ChangelogState() {
        override fun isLoading(): Boolean = true
        override fun hasData(): Boolean = false
        override fun getEntries(): List<ChangelogEntry> = emptyList()
    }
    
    data class Success(private val changelogEntries: List<ChangelogEntry>) : ChangelogState() {
        override fun isLoading(): Boolean = false
        override fun hasData(): Boolean = changelogEntries.isNotEmpty()
        override fun getEntries(): List<ChangelogEntry> = changelogEntries
    }
    
    data class Error(val message: String) : ChangelogState() {
        override fun isLoading(): Boolean = false
        override fun hasData(): Boolean = false
        override fun getEntries(): List<ChangelogEntry> = emptyList()
    }
}

data class SettingsUiState(
    val versionName: String = "",
    val versionCode: Long = 0,
    val faqItems: List<FaqItem> = FaqItem.getAllFaqItems(),
    val changelogState: ChangelogState = ChangelogState.Loading
)

@HiltViewModel
class SettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val getChangelogUseCase: GetChangelogUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: init")
        loadAppVersion()
        loadChangelog()
    }

    private fun loadAppVersion() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: loadAppVersion called")
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val versionName = packageInfo.versionName ?: "N/A"
            val versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }
            _uiState.update { it.copy(versionName = versionName, versionCode = versionCode) }
            Timber.tag("DEBUG_FLOW").d("SettingsViewModel: App Version loaded - Name: %s, Code: %d", versionName, versionCode)
        } catch (e: PackageManager.NameNotFoundException) {
            Timber.tag("DEBUG_FLOW").e(e, "SettingsViewModel: Failed to load app version")
            _uiState.update { it.copy(versionName = "Error", versionCode = -1) }
        }
    }

    private fun loadChangelog() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: loadChangelog called")
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(changelogState = ChangelogState.Loading) }
                val entries = getChangelogUseCase()
                _uiState.update { it.copy(changelogState = ChangelogState.Success(entries)) }
                Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Changelog loaded - %d entries", entries.size)
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e(e, "SettingsViewModel: Failed to load changelog")
                _uiState.update { it.copy(changelogState = ChangelogState.Error(e.message ?: "Unknown error")) }
            }
        }
    }

    fun logPrivacyPolicyOpened() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Privacy Policy opened event")
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_PRIVACY_POLICY_OPENED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
        }
    }

    fun logContactUsOpened() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Contact Us opened event")
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_CONTACT_US_OPENED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
        }
    }

    fun logFaqOpened() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging FAQ opened event")
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_FAQ_OPENED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
        }
    }

    fun logFaqItemExpanded(faqItemId: String) {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging FAQ item expanded - ID: %s", faqItemId)
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_FAQ_ITEM_EXPANDED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(AnalyticsConstants.Params.FAQ_ITEM_ID, faqItemId)
        }
    }

    fun logFaqItemCollapsed(faqItemId: String) {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging FAQ item collapsed - ID: %s", faqItemId)
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_FAQ_ITEM_COLLAPSED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(AnalyticsConstants.Params.FAQ_ITEM_ID, faqItemId)
        }
    }

    fun logChangelogOpened() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Changelog opened event")
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_CHANGELOG_OPENED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(AnalyticsConstants.Params.CHANGELOG_ENTRY_COUNT, uiState.value.changelogState.getEntries().size)
        }
    }

    fun logChangelogEntryExpanded(version: String) {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Changelog entry expanded - Version: %s", version)
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_CHANGELOG_ENTRY_EXPANDED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(AnalyticsConstants.Params.CHANGELOG_VERSION, version)
        }
    }

    fun logChangelogEntryCollapsed(version: String) {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Changelog entry collapsed - Version: %s", version)
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SETTINGS_CHANGELOG_ENTRY_COLLAPSED) {
            param(AnalyticsConstants.Params.SCREEN, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(AnalyticsConstants.Params.CHANGELOG_VERSION, version)
        }
    }

    fun logSettingsScreenOpened() {
        Timber.tag("DEBUG_FLOW").d("SettingsViewModel: Logging Settings Screen opened event")
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.SETTINGS_SCREEN_KT) // You might adjust this if needed
        }
    }
}