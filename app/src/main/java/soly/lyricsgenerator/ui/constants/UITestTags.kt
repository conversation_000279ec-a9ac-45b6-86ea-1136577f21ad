package soly.lyricsgenerator.ui.constants

/**
 * UI Test Tags constants for Compose UI testing.
 * These tags are used to identify UI components in tests.
 * 
 * Following the project's ZERO tolerance policy for hardcoded strings,
 * all test tags must be defined as constants.
 */
object UITestTags {
    // Media Controller Components
    const val PLAY_PAUSE_BUTTON = "play_pause_button"
    const val SEEK_FORWARD_5_SECONDS_BUTTON = "seek_forward_5_seconds_button"
    const val SEEK_BACKWARD_5_SECONDS_BUTTON = "seek_backward_5_seconds_button"
    const val CURRENT_POSITION_TEXT = "current_position_text"
    const val DURATION_TEXT = "duration_text"
    
    // Navigation Components
    const val BOTTOM_NAVIGATION_BAR = "bottom_navigation_bar"
    
    // Song List Components
    const val SONG_LIST = "song_list"

    const val COMPACT_MUSIC_PLAYER_CONTROLLER = "compact_music_player_controller"
    
    // App Bar Components
    const val APP_BAR_MORE_OPTIONS = "app_bar_more_options"
    
    // Song Item Components  
    const val SONG_ITEM_MORE_OPTIONS = "song_item_more_options"
}