package soly.lyricsgenerator.ui.constants

/**
 * Constants for UI test tags to ensure consistent testing
 */
object TestTagConstants {
    
    /**
     * Test tag prefixes for different UI components
     */
    private const val ARTIST_FILTER_ITEM_PREFIX = "artist_filter_item_"
    
    /**
     * Generates test tag for artist filter bottom sheet items
     * @param artist The artist name
     * @return The test tag string for the artist filter item
     */
    fun getArtistFilterItemTag(artist: String): String = "$ARTIST_FILTER_ITEM_PREFIX$artist"
}