// ui/theme/NavigationBarItemColors.kt
package soly.lyricsgenerator.ui.theme

import androidx.compose.material3.NavigationBarItemColors
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

val DefaultNavigationBarItemColors: NavigationBarItemColors
    @Composable
    get() = NavigationBarItemDefaults.colors(
        selectedIconColor = Color.White,
        selectedTextColor = Color.White,
        unselectedIconColor = Color.White.copy(alpha = 0.6f),
        unselectedTextColor = Color.White.copy(alpha = 0.6f),
        indicatorColor = AccentColor.copy(alpha = 0.3f)
    )
