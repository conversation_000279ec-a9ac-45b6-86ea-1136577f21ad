package soly.lyricsgenerator.ui.theme

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

val Purple = Color(0xFF4a2246)
val AccentColor = Color(0xFFD81B60)

/**
 * Unified interactive accent color for active UI elements like favorite hearts and shuffle buttons
 * Uses the violet color for consistency across all interactive elements
 */
val InteractiveAccent = Purple80

/**
 * Reusable gradient brush for consistent styling across the app
 * Direction: Horizontal (left to right)
 * Colors: Purple (#4A2246) to Pink (#D81B60)
 */
val AppGradientBrush = Brush.linearGradient(
    colors = listOf(Purple, AccentColor),
    start = Offset(0f, 0f),
    end = Offset(Float.POSITIVE_INFINITY, 0f)
)