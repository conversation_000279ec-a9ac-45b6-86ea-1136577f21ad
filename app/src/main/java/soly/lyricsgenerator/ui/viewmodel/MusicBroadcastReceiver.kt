package soly.lyricsgenerator.ui.viewmodel

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import kotlinx.coroutines.flow.MutableStateFlow
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.repository.SongsCache
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook

class MusicBroadcastReceiver(
    private val _songsFlow: MutableStateFlow<List<Song>>,
    private val _currentSongFlow: MutableStateFlow<Song?>,
    private val isPlayingFlow: MutableStateFlow<Boolean>,
    private val _currentPositionFlow: MutableStateFlow<Long>,
    private val _lrcLinesFlow: MutableStateFlow<Map<Int, String>>,
    private val _errorMessageFlow: MutableStateFlow<String?>? = null,
    private val _isLoadingSongsFlow: MutableStateFlow<Boolean>? = null
) : BroadcastReceiver() {

    constructor() : this(
        _songsFlow = MutableStateFlow(emptyList()),
        _currentSongFlow = MutableStateFlow(null),
        isPlayingFlow = MutableStateFlow(false),
        _currentPositionFlow = MutableStateFlow(0L),
        _lrcLinesFlow = MutableStateFlow(emptyMap()),
        _errorMessageFlow = MutableStateFlow(null),
        _isLoadingSongsFlow = MutableStateFlow(false)
    )

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        
        
        when (action) {
            MusicPlayerService.ACTION_SONGS_LOADED -> {
                try {
                    // Retrieve songs from cache instead of Intent extras
                    val songs = SongsCache.getSongs()
                    
                    // Log analytics event for songs loaded
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BROADCAST_SONGS_LOADED) {
                        param(AnalyticsConstants.Params.SONGS_COUNT, songs.size.toLong())
                    }
                    
                    if (songs.isNotEmpty()) {
                        // CRITICAL: Songs from cache are already sorted by MusicPlayerService
                        // Update flow immediately to preserve sort order
                        _songsFlow.value = songs
                    }
                    
                    // Clear loading state regardless of whether songs are empty or not
                    _isLoadingSongsFlow?.value = false
                } catch (e: Exception) {
                    // Log analytics event for songs loading error
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BROADCAST_ERROR) {
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: AnalyticsConstants.ErrorMessages.UNKNOWN_ERROR)
                        param(AnalyticsConstants.Params.ACTION, MusicPlayerService.ACTION_SONGS_LOADED)
                    }
                    e.printStackTrace()
                    
                    // Clear loading state on error too
                    _isLoadingSongsFlow?.value = false
                }
            }
            MusicPlayerService.ACTION_OPEN_APP -> {
                // Log analytics event for app opening from notification
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BROADCAST_APP_OPENED_FROM_NOTIFICATION) {}
                
                // Launch MainActivity
                val launchIntent = Intent(context, Class.forName("soly.lyricsgenerator.MainActivity")).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    putExtra("FROM_NOTIFICATION", true)
                }
                context.startActivity(launchIntent)
            }
            MusicPlayerService.ACTION_SONG_CHANGED -> {
                val currentSong = intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG) as? Song
                currentSong?.let {
                    // Log analytics event for song change
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BROADCAST_SONG_CHANGED) {
                        param(AnalyticsConstants.Params.SONG_ID, it.id.toString())
                        param(AnalyticsConstants.Params.SONG_TITLE, it.title ?: AnalyticsConstants.Params.UNKNOWN)
                    }
                    
                    _currentSongFlow.value = it
                    // Clear lyrics when song changes to ensure proper UI state
                    // We'll load new lyrics after if they exist
                    _lrcLinesFlow.value = emptyMap()
                }
            }
            MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED -> {
                val isPlaying = intent.getBooleanExtra(MusicPlayerService.EXTRA_IS_PLAYING, false)
                
                // Log analytics event for playback state change
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BROADCAST_PLAYBACK_STATE_CHANGED) {
                    param(AnalyticsConstants.Params.IS_PLAYING, isPlaying.toString())
                }
                
                isPlayingFlow.value = isPlaying
            }
            MusicPlayerService.ACTION_POSITION_UPDATED -> {
                val currentPosition = intent.getLongExtra(MusicPlayerService.EXTRA_CURRENT_POSITION, 0L)
                _currentPositionFlow.value = currentPosition
            }
            MusicPlayerService.ACTION_STATE_UPDATED -> {
                val currentSong = intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG) as? Song
                val currentPosition = intent.getLongExtra(MusicPlayerService.EXTRA_CURRENT_POSITION, 0L)
                val isPlaying = intent.getBooleanExtra(MusicPlayerService.EXTRA_IS_PLAYING, false)
                
                // Only update current song if non-null to avoid clearing it after stop
                if (currentSong != null) {
                    _currentSongFlow.value = currentSong
                }
                
                _currentPositionFlow.value = currentPosition
                isPlayingFlow.value = isPlaying
            }
            MusicPlayerService.ACTION_PLAYBACK_ERROR -> {
                val errorMessage = intent.getStringExtra(MusicPlayerService.EXTRA_ERROR_MESSAGE)
                val errorSong = intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG) as? Song
                
                // Update error state
                _errorMessageFlow?.value = errorMessage
                
                // Reset playback state since there was an error
                isPlayingFlow.value = false
                
                // Log the error for debugging
                android.util.Log.e("MusicBroadcastReceiver", "Playback error for song ${errorSong?.title}: $errorMessage")
            }
            else -> {
                // Handle unexpected broadcast actions silently
            }
        }
    }
}
