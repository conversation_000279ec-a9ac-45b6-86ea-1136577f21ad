package soly.lyricsgenerator.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import soly.lyricsgenerator.domain.model.FileWithSong
import soly.lyricsgenerator.domain.usecase.database.DeleteFileUseCase
import soly.lyricsgenerator.domain.usecase.database.GetFilesWithSongsUseCase
import soly.lyricsgenerator.domain.usecase.database.GetFileContentUseCase
import javax.inject.Inject
import com.google.firebase.analytics.FirebaseAnalytics
import android.os.Bundle // Needed for Bundle
import com.google.firebase.analytics.FirebaseAnalytics.Param // For standard params
import com.google.firebase.analytics.FirebaseAnalytics.Event // For standard events
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import android.net.Uri // Added for Uri
import soly.lyricsgenerator.domain.database.model.File // Added for File model
import soly.lyricsgenerator.domain.database.model.FileType // Added for FileType
import soly.lyricsgenerator.domain.usecase.database.file.SaveFileUseCase // Added for SaveFileUseCase
import soly.lyricsgenerator.ui.screens.components.getFileNameFromUri // Import the utility function
import soly.lyricsgenerator.ui.screens.components.getFileTypeFromExtension // Import the file type detection function
import android.app.Application // Required for context
import java.io.FileOutputStream // For writing to internal storage
import java.io.IOException // For exception handling
import timber.log.Timber
import soly.lyricsgenerator.R

@HiltViewModel
class FilesViewModel @Inject constructor(
    private val getFilesWithSongsUseCase: GetFilesWithSongsUseCase,
    private val deleteFileUseCase: DeleteFileUseCase,
    private val getFileContentUseCase: GetFileContentUseCase,
    private val saveFileUseCase: SaveFileUseCase, // Inject SaveFileUseCase
    private val application: Application // Inject Application context
) : ViewModel() {

    private val _allFiles = MutableStateFlow<List<FileWithSong>>(emptyList())
    private val _searchQuery = MutableStateFlow("")
    private val _isLoading = MutableStateFlow(false)
    private val _error = MutableStateFlow<String?>(null)
    
    // Flow for filtered files similar to MusicViewModel
    val filteredFilesFlow: StateFlow<List<FileWithSong>> = combine(
        _allFiles,
        _searchQuery
    ) { files, query ->
        if (query.isEmpty()) {
            files
        } else {
            val filtered = files.filter { fileWithSong ->
                // Search in filename and song title/artist
                val matchesFileName = fileWithSong.file.fileName.contains(query, ignoreCase = true)
                val matchesSongTitle = fileWithSong.song?.title?.contains(query, ignoreCase = true) == true
                val matchesArtist = fileWithSong.song?.artist?.contains(query, ignoreCase = true) == true
                
                matchesFileName || matchesSongTitle || matchesArtist
            }
            filtered
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = emptyList()
    )

    // Combined UI state
    val uiState: StateFlow<FilesUiState> = combine(
        filteredFilesFlow,
        _isLoading,
        _error
    ) { filteredFiles, isLoading, error ->
        val state = when {
            isLoading -> FilesUiState.Loading
            error != null -> FilesUiState.Error(error)
            filteredFiles.isEmpty() && _allFiles.value.isEmpty() -> FilesUiState.Empty
            filteredFiles.isEmpty() -> FilesUiState.Empty // Empty search results
            else -> FilesUiState.Success(filteredFiles)
        }
        
        state
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = FilesUiState.Loading
    )

    private val _selectedLrcUri = MutableStateFlow<Uri?>(null)
    // No need to expose _selectedLrcUri as StateFlow if only used internally

    private val _snackbarMessage = MutableStateFlow<String?>(null)
    val snackbarMessage: StateFlow<String?> = _snackbarMessage.asStateFlow()

    init {
        loadFiles()
    }
    
    // Method to handle refresh event from outside
    fun onRefreshEvent() {
        loadFiles()
    }
    
    // Method to handle silent refresh event without showing loading indicator
    fun onSilentRefreshEvent() {
        Timber.tag("DEBUG_FLOW").d("FilesViewModel: onSilentRefreshEvent() called, loading files silently")
        loadFilesSilently()
    }

    fun loadFiles() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            try {
                val filesWithSongs = getFilesWithSongsUseCase()
                _allFiles.value = filesWithSongs
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // Load files without showing loading indicator (for silent refreshes)
    private fun loadFilesSilently() {
        viewModelScope.launch {
            // Don't set loading state to avoid showing pull indicator
            _error.value = null
            try {
                Timber.tag("DEBUG_FLOW").d("FilesViewModel: loadFilesSilently() - fetching files from database")
                val filesWithSongs = getFilesWithSongsUseCase()
                Timber.tag("DEBUG_FLOW").d("FilesViewModel: loadFilesSilently() - loaded ${filesWithSongs.size} files")
                _allFiles.value = filesWithSongs
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e("FilesViewModel: loadFilesSilently() - error: ${e.message}")
                _error.value = e.message ?: "Unknown error"
            }
        }
    }

    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    fun deleteFile(fileId: Long) {
        viewModelScope.launch {
            try {
                deleteFileUseCase(fileId)
                logFileDeletedEvent(fileId)
                loadFiles() // Reload the files after deletion
            } catch (e: Exception) {
            }
        }
    }

    // Function to log file deletion event
    private fun logFileDeletedEvent(fileId: Long) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILE_DELETED) {
            param(AnalyticsConstants.Params.FILE_ID, fileId)
        }
    }
    
    // Function to log file save event
    fun logFileSavedEvent(fileId: Long, fileName: String?) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILE_SAVED) {
            param(AnalyticsConstants.Params.FILE_ID, fileId)
            param(AnalyticsConstants.Params.FILE_NAME, fileName ?: AnalyticsConstants.Params.UNKNOWN)
        }
    }

    // Function to log screen open event
    fun logFilesScreenOpened() {
        GlobalAnalyticsHook.logEvent(Event.SCREEN_VIEW) {
            param(Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.FILES)
            param(Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.FILES + "Screen")
        }
    }

    // Function to log file item click event
    fun logFileItemClicked(fileId: Long, fileName: String) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_ITEM_CLICKED) {
            param(AnalyticsConstants.Params.FILE_ID, fileId)
            param(AnalyticsConstants.Params.FILE_NAME, fileName)
        }
    }

    // Function to log delete option click event
    fun logDeleteOptionClicked(fileId: Long, fileName: String) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_DELETE_OPTION_CLICKED) {
            param(AnalyticsConstants.Params.FILE_ID, fileId)
            param(AnalyticsConstants.Params.FILE_NAME, fileName)
        }
    }

    // Function to log save option click event
    fun logSaveOptionClicked(fileId: Long, fileName: String) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_SAVE_OPTION_CLICKED) {
            param(AnalyticsConstants.Params.FILE_ID, fileId)
            param(AnalyticsConstants.Params.FILE_NAME, fileName)
        }
    }

    // Function to log retry button click event
    fun logRetryLoadFilesClicked() {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_RETRY_LOAD_CLICKED) { }
    }

    // Function to retrieve file content for saving
    suspend fun getFileContent(fileId: Long): String? {
        return try {
            getFileContentUseCase(fileId)
        } catch (e: Exception) {
            null
        }
    }

    fun onLrcFileSelected(uri: Uri?) {
        _selectedLrcUri.value = uri
        if (uri != null) {
            logLrcFileSelectedEvent(uri.toString())
        }
    }

    fun onSongSelectedForLinking(songId: Long?) {
        viewModelScope.launch {
            val lrcUri = _selectedLrcUri.value

            if (lrcUri != null && songId != null) {
                val originalFileName = getFileNameFromUri(application, lrcUri)

                logSongSelectedForLinkingEvent(songId, originalFileName)

                try {
                    var internalFilePath: String? = null
                    application.contentResolver.openInputStream(lrcUri)?.use { inputStream ->
                        val internalFile = java.io.File(application.filesDir, originalFileName)

                        FileOutputStream(internalFile).use { outputStream ->
                            inputStream.copyTo(outputStream)
                        }
                        internalFilePath = internalFile.absolutePath
                    }

                    if (internalFilePath != null) {
                        val detectedFileType = getFileTypeFromExtension(originalFileName)

                        val fileToSave = File(
                            fileName = originalFileName,
                            filePath = internalFilePath!!, // internalFilePath is checked for null above
                            fileType = detectedFileType,
                            songId = songId
                        )
                        
                        saveFileUseCase(fileToSave)
                        
                        val fileTypeString = application.getString(detectedFileType.getStringResourceId())
                        
                        logLrcFileLinkedEvent(originalFileName, songId)
                        _snackbarMessage.value = application.getString(R.string.file_linked_successfully, fileTypeString)
                        loadFiles() // Refresh the list
                    } else {
                        logLrcFileLinkFailedEvent(originalFileName, songId, "Failed to read/write file content")
                        _snackbarMessage.value = application.getString(R.string.error_linking_file)
                    }
                } catch (e: IOException) {
                    logLrcFileLinkFailedEvent(originalFileName, songId, e.message)
                    _snackbarMessage.value = application.getString(R.string.error_linking_file)
                } catch (e: Exception) {
                    logLrcFileLinkFailedEvent(originalFileName, songId, e.message)
                    _snackbarMessage.value = application.getString(R.string.error_linking_file)
                } finally {
                    _selectedLrcUri.value = null // Clear the URI after processing
                }
            } else {
                _snackbarMessage.value = application.getString(R.string.failed_to_link_missing_file)
                 _selectedLrcUri.value = null // Clear the URI
            }
        }
    }
    
    fun onSnackbarShown() {
        _snackbarMessage.value = null
    }

    // Analytics events
    fun logFabClickedEvent() {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FAB_IMPORT_FILE_CLICKED) { }
    }

    private fun logLrcFileSelectedEvent(fileUriString: String) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_SELECTED) {
            param(AnalyticsConstants.Params.FILE_URI, fileUriString)
        }
    }

    private fun logSongSelectedForLinkingEvent(songId: Long, fileName: String) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_SONG_SELECTED_FOR_LINKING) {
            param(AnalyticsConstants.Params.SONG_ID, songId)
            param(AnalyticsConstants.Params.FILE_NAME, fileName)
        }
    }

    private fun logLrcFileLinkedEvent(fileName: String, songId: Long) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_LINKED_SUCCESSFULLY) {
            param(AnalyticsConstants.Params.FILE_NAME, fileName)
            param(AnalyticsConstants.Params.SONG_ID, songId)
        }
    }

    private fun logLrcFileLinkFailedEvent(fileName: String?, songId: Long?, errorMessage: String?) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_FILE_LINK_FAILED) {
            param(AnalyticsConstants.Params.FILE_NAME, fileName ?: AnalyticsConstants.Params.UNKNOWN)
            param(AnalyticsConstants.Params.SONG_ID, songId ?: -1L)
            param(AnalyticsConstants.Params.ERROR_MESSAGE, errorMessage ?: AnalyticsConstants.ErrorMessages.UNKNOWN_ERROR)
        }
    }
}

sealed class FilesUiState {
    object Loading : FilesUiState()
    object Empty : FilesUiState()
    data class Success(val filesWithSongs: List<FileWithSong>) : FilesUiState()
    data class Error(val message: String) : FilesUiState()
} 