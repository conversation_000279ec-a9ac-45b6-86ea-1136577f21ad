package soly.lyricsgenerator.ui.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import soly.lyricsgenerator.domain.usecase.lyrics.CalculateLyricsUseCase
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class LyricsViewModel @Inject constructor(
    private val calculateLyricsUseCase: CalculateLyricsUseCase
) : ViewModel() {
    // UI state for the lyrics display
    private val _activeLineIndex = MutableStateFlow(-1)
    val activeLineIndex: StateFlow<Int> = _activeLineIndex.asStateFlow()

    private val _linesToDisplay = MutableStateFlow<List<Pair<Int, String>>>(emptyList())
    val linesToDisplay: StateFlow<List<Pair<Int, String>>> = _linesToDisplay.asStateFlow()

    /**
     * Update the UI state based on the current lyrics and playback position
     * @param lrcLines Map of time in milliseconds to lyrics line
     * @param currentTimeMs Current playback position in milliseconds
     */
    fun calculateActiveLineIndex(lrcLines: Map<Int, String>, currentTimeMs: Int) {
        if (lrcLines.isEmpty()) {
            Timber.tag("DEBUG_FLOW").d("LyricsViewModel: No lyrics available, setting active line index to -1 and empty list")
            _activeLineIndex.value = -1
            _linesToDisplay.value = emptyList()
            
            // Log analytics event for empty lyrics
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.LYRICS_DISPLAY_EMPTY) {}
            
            return
        }

        // Log analytics event for lyrics loading if this is the first calculation with content
        if (_activeLineIndex.value == -1 && lrcLines.isNotEmpty()) {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.LYRICS_LOADED) {
                param(AnalyticsConstants.Params.LINE_COUNT, lrcLines.size.toLong())
            }
        }

        // Use the use case to calculate the active line index
        val newIndex = calculateLyricsUseCase.calculateActiveLineIndex(lrcLines, currentTimeMs)

        // Only update the state flow if the index has actually changed
        if (newIndex != _activeLineIndex.value) {
            Timber.tag("DEBUG_FLOW").d("LyricsViewModel: Active line index changed from ${_activeLineIndex.value} to $newIndex")
            
            // Log analytics event for line change
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.LYRICS_LINE_CHANGED) {
                param(AnalyticsConstants.Params.LINE_INDEX, newIndex.toLong())
                param(AnalyticsConstants.Params.CURRENT_TIME_MS, currentTimeMs.toLong())
            }
            
            _activeLineIndex.value = newIndex
        } else {
            // Timber.tag("DEBUG_FLOW").d("LyricsViewModel: Active line index $newIndex hasn't changed.") // Optional: uncomment for verbose logging
        }
        
        // Always use the use case to get the lines to display (which now returns the full list)
        // This update might be redundant if the index hasn't changed, but harmless.
        Timber.tag("DEBUG_FLOW").d("LyricsViewModel: Setting linesToDisplay using use case (expecting full list)")
        _linesToDisplay.value = calculateLyricsUseCase.calculateLinesToDisplay(lrcLines, newIndex)
    }
    
    /**
     * Check if a line is the active line
     * @param timeMs The timestamp of the line to check
     * @param lrcLines All available lyrics lines
     * @return True if the line is the active line
     */
    fun isActiveLine(timeMs: Int, lrcLines: Map<Int, String>): Boolean {
        return calculateLyricsUseCase.isActiveLine(timeMs, lrcLines, _activeLineIndex.value)
    }
} 