package soly.lyricsgenerator.ui.viewmodel

object PreferenceKeys {
    // SharedPreferences names
    const val LYRIC_GENERATOR_PREFS = "lyric_generator_prefs"
    
    // Preference keys
    const val SHUFFLE_ENABLED = "shuffle_enabled"
    const val SHOW_FAVORITES_ONLY = "show_favorites_only"
    const val SORT_TYPE = "sort_type"
    const val SORT_ORDER = "sort_order"
    const val ARTIST_FILTER = "artist_filter"
    
    // Default values
    const val DEFAULT_SORT_TYPE = "Title"
    const val DEFAULT_SORT_ORDER = "Ascending"
    const val DEFAULT_ARTIST_FILTER = "All Artists"
}
