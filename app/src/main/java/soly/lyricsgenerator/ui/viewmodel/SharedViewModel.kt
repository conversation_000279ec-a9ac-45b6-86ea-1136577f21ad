package soly.lyricsgenerator.ui.viewmodel

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SharedViewModel @Inject constructor() : ViewModel() {

    private val _hasUnsavedChanges = mutableStateOf(false)
    val hasUnsavedChanges: State<Boolean> = _hasUnsavedChanges
    
    private val _isPostStartScreen = mutableStateOf(false)
    val isPostStartScreen: State<Boolean> = _isPostStartScreen
    
    // Flag to track if we're navigating to/from subflows (SongPicker, PasteLyrics)
    private val _isNavigatingInSubflow = mutableStateOf(false)
    val isNavigatingInSubflow: State<Boolean> = _isNavigatingInSubflow

    // Event to trigger files refresh
    private val _refreshFilesEvent = MutableSharedFlow<Unit>(extraBufferCapacity = 1)
    val refreshFilesEvent: SharedFlow<Unit> = _refreshFilesEvent.asSharedFlow()

    // Event to trigger silent files refresh (without loading indicator)
    // Using replay = 1 to ensure the event is cached for late subscribers
    private val _refreshFilesSilentlyEvent = MutableSharedFlow<Unit>(replay = 1, extraBufferCapacity = 1)
    val refreshFilesSilentlyEvent: SharedFlow<Unit> = _refreshFilesSilentlyEvent.asSharedFlow()

    // Event to trigger LRC file import
    private val _importLrcFileEvent = MutableSharedFlow<Unit>(extraBufferCapacity = 1)
    val importLrcFileEvent: SharedFlow<Unit> = _importLrcFileEvent.asSharedFlow()

    // StateFlow to hold the song ID selected for linking with a file
    private val _songIdForLinking = MutableStateFlow<Long?>(null)
    val songIdForLinking: StateFlow<Long?> = _songIdForLinking.asStateFlow()
    
    // State for keyboard visibility
    private val _isKeyboardVisible = mutableStateOf(false)
    val isKeyboardVisible: State<Boolean> = _isKeyboardVisible
    
    // State to track if sync mode has been initialized for the current session
    private val _isSyncInitialized = mutableStateOf(false)
    val isSyncInitialized: State<Boolean> = _isSyncInitialized
    
    // Back navigation handler for PasteLyricsScreen
    private var _pasteLyricsBackHandler: (() -> Unit)? = null

    fun setUnsavedChanges(value: Boolean) {
        if (_hasUnsavedChanges.value != value) {
            // Log analytics event for unsaved changes state
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.NAVIGATION_UNSAVED_CHANGES_STATE) {
                param(AnalyticsConstants.Params.HAS_UNSAVED_CHANGES, value.toString())
            }
        }
        _hasUnsavedChanges.value = value
    }
    
    fun setPostStartScreen(value: Boolean) {
        _isPostStartScreen.value = value
    }
    
    fun setNavigatingInSubflow(value: Boolean) {
        if (_isNavigatingInSubflow.value != value) {
            // Log analytics event for navigation state
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.NAVIGATION_SUBFLOW_STATE) {
                param(AnalyticsConstants.Params.IN_SUBFLOW, value.toString())
            }
        }
        _isNavigatingInSubflow.value = value
    }
    
    fun refreshFiles() {
        // Log analytics event for files refresh
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_REFRESH_TRIGGERED) {}
        
        _refreshFilesEvent.tryEmit(Unit)
    }
    
    fun refreshFilesSilently() {
        // Log analytics event for silent files refresh
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_REFRESH_TRIGGERED) {
            param(AnalyticsConstants.Params.REFRESH_TYPE, "silent")
        }
        
        Timber.tag("DEBUG_FLOW").d("SharedViewModel: refreshFilesSilently() called, emitting event")
        val emitted = _refreshFilesSilentlyEvent.tryEmit(Unit)
        Timber.tag("DEBUG_FLOW").d("SharedViewModel: refreshFilesSilently() event emitted: $emitted")
    }

    fun importLrcFile() {
        // Log analytics event for LRC import trigger
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.FILES_IMPORT_TRIGGERED) {}
        
        _importLrcFileEvent.tryEmit(Unit)
    }

    fun setSongIdForLinking(songId: Long?) {
        if (songId != null) {
            // Log analytics event for song linking initiation
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SONG_LINKING_INITIATED) {
                param(AnalyticsConstants.Params.SONG_ID, songId.toString())
            }
        }
        _songIdForLinking.value = songId
    }
    
    fun setKeyboardVisible(visible: Boolean) {
        _isKeyboardVisible.value = visible
    }
    
    fun setSyncInitialized(value: Boolean) {
        _isSyncInitialized.value = value
    }
    
    fun setPasteLyricsBackHandler(handler: () -> Unit) {
        _pasteLyricsBackHandler = handler
    }
    
    fun handlePasteLyricsBack(): Boolean {
        return _pasteLyricsBackHandler?.let { 
            it.invoke()
            true
        } ?: false
    }
    
    fun clearPasteLyricsBackHandler() {
        _pasteLyricsBackHandler = null
    }
}
