package soly.lyricsgenerator.ui.viewmodel

import Tuple
import okio.source
import android.app.Application
import android.content.Context
import android.content.IntentFilter
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.usecase.database.file.SaveFileUseCase
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByIdUseCase
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByFileIdUseCase
import soly.lyricsgenerator.domain.usecase.database.song.GetSongUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ParseAndPrepareTextFileUseCase
import soly.lyricsgenerator.domain.usecase.lrc.SaveLrcFileUseCase
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.screens.create_screen.ScreenStateHolder
import timber.log.Timber
import javax.inject.Inject
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.BuildConfig
import androidx.core.content.ContextCompat
import soly.lyricsgenerator.domain.model.FilteredSongsState
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.constants.PlaybackConstants

@HiltViewModel
class CreateViewModel @Inject constructor(
    private val context: Application,
    private val saveFileUseCase: SaveFileUseCase,
    private val saveLrcFileUseCase: SaveLrcFileUseCase,
    private val getSongUseCase: GetSongUseCase,
    private val getFileByIdUseCase: GetFileByIdUseCase,
    private val getFileByFileIdUseCase: GetFileByFileIdUseCase,
    private val parseAndPrepareTextFileUseCase: ParseAndPrepareTextFileUseCase,
) : ViewModel() {

    val songs = MutableStateFlow<List<Song>>(emptyList())
    val pendingSeekTo = MutableStateFlow<Long?>(null)
    val selectedSong = MutableStateFlow<Song?>(null)
    val pickedTextFileName = MutableStateFlow<String?>(null)
    val isPlaying = MutableStateFlow(false)
    val lrcKeyValuePairs = MutableStateFlow<Map<Int, Tuple<Long, String>>>(emptyMap())
    val showPreview = MutableStateFlow(false)
    private val _enteredScreen = MutableStateFlow(false)
    private var musicReceiver: MusicBroadcastReceiver? = null
    private val _currentSongFlow = MutableStateFlow<Song?>(null)
    val currentSongFlow = _currentSongFlow.asStateFlow()
    val currentPositionFlow = MutableStateFlow(0L)
    private val _currentLyricLine = MutableStateFlow<String?>(null)
    val currentLyricLine = _currentLyricLine.asStateFlow()
    
    // New state to track whether steps indicator should be visible
    val showStepsIndicator = MutableStateFlow(true)
    
    // New state to track the height of the music controller
    val musicControllerHeight = MutableStateFlow(0)
    
    // State to track whether animations have played
    val songAnimationPlayed = MutableStateFlow(false)
    val lyricsAnimationPlayed = MutableStateFlow(false)
    
    // Search functionality
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery
    
    // Flow for filtered songs
    val filteredSongsFlow: StateFlow<FilteredSongsState> = combine(
        songs, _searchQuery
    ) { songsList, query ->
        val filteredSongs = if (query.isEmpty()) {
            songsList
        } else {
            songsList.filter { song ->
                song.title.contains(query, ignoreCase = true) || 
                song.artist.contains(query, ignoreCase = true)
            }
        }
        FilteredSongsState(songs = filteredSongs)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = FilteredSongsState(songs = emptyList())
    )
    
    private var internalMusicReceiver: MusicBroadcastReceiver? = null
    
    // Functions to mark animations as completed
    fun markSongAnimationPlayed() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Marking song animation as played")
        songAnimationPlayed.value = true
    }

    // Update search query for filtering songs
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * Initialize sync mode with pre-loaded song and TXT file data
     */
    suspend fun initializeSyncMode(songId: Long, fileId: Long) {
        try {
                Timber.tag("DEBUG_FLOW").d("CreateViewModel: Initializing sync mode with songId=$songId, fileId=$fileId")

                // Load song data
                val song = getSongUseCase(songId)
                if (song != null) {
                    setSelectedSong(song)
                    Timber.tag("DEBUG_FLOW").d("CreateViewModel: Loaded song: ${song.title}")
                } else {
                    Timber.tag("DEBUG_FLOW").e("CreateViewModel: Failed to load song with id $songId")
                    return
                }
                
                // Load file data
                // Note: We need to use getFileByFileIdUseCase here, not getFileByIdUseCase
                // because getFileByIdUseCase actually queries by songId (confusing naming)
                val file = getFileByFileIdUseCase(fileId)
                val parseResult = parseAndPrepareTextFileUseCase(file, fileId)
                
                parseResult.logResult()
                
                if (parseResult.isSuccess()) {
                    parseResult.getLrcData()?.let { lrcData ->
                        lrcKeyValuePairs.value = lrcData
                        pickedTextFileName.value = parseResult.getFileName()
                        
                        Timber.tag("DEBUG_FLOW").d("CreateViewModel: isSongAndFileSelected: ${isSongAndFileSelected()}")
                        
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SYNC_MODE_INITIALIZED) {
                            param(AnalyticsConstants.Params.SONG_ID, songId)
                            param(AnalyticsConstants.Params.FILE_ID, fileId)
                            param(AnalyticsConstants.Params.LINE_COUNT, parseResult.getLineCount().toLong())
                        }
                    }
                }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("CreateViewModel: Error initializing sync mode: ${e.message}")
            e.printStackTrace()
        }
    }
    
    // Set the selected song with proper encapsulation
    fun setSelectedSong(song: Song) {
        selectedSong.value = song
    }

    /**
     * Transcribe the currently selected audio file and update lyrics.
     */
    fun transcribeSelectedAudio() {
        viewModelScope.launch {
            val song = selectedSong.value
            if (song == null) {
                Timber.e("No song selected for transcription")
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TRANSCRIPTION_FAILED) {
                    param(AnalyticsConstants.Params.REASON, "No song selected")
                }
                return@launch
            }
            try {
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TRANSCRIPTION_STARTED) {
                    param(AnalyticsConstants.Params.SONG_TITLE, song.title ?: AnalyticsConstants.Params.UNKNOWN)
                    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                }
                val audioFile = java.io.File(song.data)
                val result = transcribeAudioFile(audioFile)
                lrcKeyValuePairs.value = result
                pickedTextFileName.value = "${song.title}_transcribed.srt"
                Timber.d("Transcription successful, ${result.size} lines generated")
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TRANSCRIPTION_SUCCEEDED) {
                    param(AnalyticsConstants.Params.SONG_TITLE, song.title ?: AnalyticsConstants.Params.UNKNOWN)
                    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                    param(AnalyticsConstants.Params.LINES_GENERATED, result.size.toLong())
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to transcribe selected audio")
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.TRANSCRIPTION_FAILED) {
                    param(AnalyticsConstants.Params.SONG_TITLE, song.title ?: AnalyticsConstants.Params.UNKNOWN)
                    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                    param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: AnalyticsConstants.ErrorMessages.UNKNOWN_ERROR)
                }
            }
        }
    }

    /**
     * Transcribe an audio file using OpenAI Whisper API and return parsed SRT lines.
     */
    suspend fun transcribeAudioFile(audioFile: java.io.File): Map<Int, Tuple<Long, String>> {
        return kotlinx.coroutines.withContext(Dispatchers.IO) {
            try {
                val openAI = com.aallam.openai.client.OpenAI(
                    com.aallam.openai.client.OpenAIConfig(
                        token = BuildConfig.OPENAI_API_KEY
                    )
                )

                val fileSource = com.aallam.openai.api.file.FileSource(
                    name = audioFile.name,
                    source = audioFile.source()
                )

                val request = com.aallam.openai.api.audio.TranscriptionRequest(
                    audio = fileSource,
                    model = com.aallam.openai.api.model.ModelId("whisper-1"),
                    responseFormat = com.aallam.openai.api.audio.AudioResponseFormat.Srt
                )

                val transcription = openAI.transcription(request)

                val srtText = transcription.text

                val lines = mutableMapOf<Int, Tuple<Long, String>>()
                val blocks = srtText.trim().split("\n\n")
                var index = 0
                for (block in blocks) {
                    val parts = block.split("\n")
                    if (parts.size >= 3) {
                        val timePart = parts[1]
                        val textPart = parts.subList(2, parts.size).joinToString(" ")

                        val startTimeStr = timePart.split(" --> ")[0]
                        val timeParts = startTimeStr.split(":", ",")
                        val hours = timeParts[0].toLong()
                        val minutes = timeParts[1].toLong()
                        val seconds = timeParts[2].toLong()
                        val millis = timeParts[3].toLong()

                        val totalMillis = (hours * 3600 + minutes * 60 + seconds) * 1000 + millis

                        lines[index] = Tuple(totalMillis, textPart)
                        index++
                    }
                }
                lines
            } catch (e: Exception) {
                e.printStackTrace()
                emptyMap()
            }
        }
    }
    
    fun markLyricsAnimationPlayed() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Marking lyrics animation as played")
        lyricsAnimationPlayed.value = true
    }
    
    // Reset animation state - for testing purposes only
    fun resetAnimationState() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Resetting animation state")
        songAnimationPlayed.value = false
        lyricsAnimationPlayed.value = false
    }

    // Function to log song picked for linking event
    fun logSongPickedForLinkingEvent(songId: Long, songTitle: String?) {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SONG_PICKED_FOR_LINKING) {
            param(AnalyticsConstants.Params.SONG_ID, songId)
            param(AnalyticsConstants.Params.SONG_TITLE, songTitle ?: AnalyticsConstants.Params.UNKNOWN)
        }
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Logged song_picked_for_linking event for ID $songId, Title: $songTitle")
    }

    init {
        setupInternalMusicReceiver()
        // Monitor for selected song changes to stop any currently playing song
        viewModelScope.launch {
            selectedSong.collect { newSong ->
                if (newSong != null) {
                    Timber.tag("DEBUG_FLOW").d("CreateViewModel: New song selected: ${newSong.title}")
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_SONG_SELECTED) {
                        param(AnalyticsConstants.Params.SONG_TITLE, newSong.title ?: AnalyticsConstants.Params.UNKNOWN)
                        param(AnalyticsConstants.Params.SONG_ARTIST, newSong.artist ?: AnalyticsConstants.Params.UNKNOWN)
                        param(AnalyticsConstants.Params.SONG_ID, newSong.id.toString())
                    }
                    // Make sure we're not playing the old song
                    if (isPlaying.value) {
                        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Stopping current playback due to new song selection")
                        stopPlaying()
                    }
                }
            }
        }

        // Combine current position and lyrics to find the current line
        viewModelScope.launch {
            combine(currentPositionFlow, lrcKeyValuePairs) { position, lyricsMap ->
                // Find the entry with the largest timestamp less than or equal to the current position
                lyricsMap.entries
                    .filter { it.value.first <= position } // Filter entries whose timestamp is before or at the current position
                    .maxByOrNull { it.value.first } // Find the one with the largest timestamp among those
                    ?.value?.second // Get the lyric text
            }.collect { lyric ->
                _currentLyricLine.value = lyric
                // Optional: Log the current lyric line for debugging
                // Timber.tag("LYRIC_SYNC").d("Current Line: $lyric at ${currentPositionFlow.value}ms")
            }
        }

        viewModelScope.launch {
            isPlaying.collect { playing ->
                val seekToValue = pendingSeekTo.value
                if (playing && seekToValue != null) {
                    Timber.tag("DEBUG_FLOW").d("CreateViewModel: Performing pending seek to $seekToValue ms after playback started")
                    seekTo(context, seekToValue)
                    pendingSeekTo.value = null
                }
            }
        }
    }

    fun registerReceiver() {
        val receiver = MusicBroadcastReceiver(
            _songsFlow = songs,
            _currentSongFlow = _currentSongFlow,
            isPlayingFlow = isPlaying,
            _currentPositionFlow = currentPositionFlow,
            _lrcLinesFlow = MutableStateFlow(emptyMap())
        )
        val intentFilter = IntentFilter().apply {
            addAction(MusicPlayerService.ACTION_SONGS_LOADED)
            addAction(MusicPlayerService.ACTION_SONG_CHANGED)
            addAction(MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED)
            addAction(MusicPlayerService.ACTION_POSITION_UPDATED)
            addAction(MusicPlayerService.ACTION_STATE_UPDATED)
        }
        context.registerReceiver(receiver, intentFilter, Context.RECEIVER_NOT_EXPORTED)
        musicReceiver = receiver
        _enteredScreen.value = true
        startPeriodicUpdates()
    }

    fun unregisterReceiver() {
        musicReceiver?.let {
            context.unregisterReceiver(it)
            musicReceiver = null
        }
        onExitScreen()
    }

    fun playSong(context: Context) {
        selectedSong.value?.let { song ->
            // First stop any current playback to ensure clean state
            MusicPlayerService.startService(context, MusicPlayerService.ACTION_STOP)
            // Then play the newly selected song in MODE_CREATION
            MusicPlayerService.startService(
                context = context, 
                action = MusicPlayerService.ACTION_PLAY, 
                song = song,
                playMode = MusicPlayerService.MODE_CREATION
            )
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PLAYBACK_PLAY) { 
                param(AnalyticsConstants.Params.SONG_ID, song.id.toString()) 
            }
            isPlaying.value = true
        }
    }

    fun pauseSong(context: Context) {
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_PAUSE)
        isPlaying.value = false
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PLAYBACK_PAUSE) { }
    }

    fun resumeSong(context: Context) {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Resuming song in MODE_CREATION")
        MusicPlayerService.startService(
            context = context, 
            action = MusicPlayerService.ACTION_RESUME,
            playMode = MusicPlayerService.MODE_CREATION
        )
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PLAYBACK_RESUME) { }
        isPlaying.value = true
    }

    fun seekTo(context: Context, position: Long) {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Seeking to $position in MODE_CREATION")
        MusicPlayerService.startService(
            context = context,
            action = MusicPlayerService.ACTION_SEEK_TO,
            seekPosition = position,
            playMode = MusicPlayerService.MODE_CREATION
        )
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PLAYBACK_SEEK) {
            param(AnalyticsConstants.Params.SEEK_POSITION_MS, position)
        }
        // User initiated seek, explicitly re-enable auto-scrolling
        val previousState = _isAutoScrollEnabled.value
        _isAutoScrollEnabled.value = true
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Seek detected - Forcing auto-scroll ON. Previous state: $previousState, New state: ${_isAutoScrollEnabled.value}")
    }

    fun seekForward5Seconds() {
        val currentPosition = currentPositionFlow.value
        val songDuration = selectedSong.value?.duration ?: 0L
        if (songDuration == 0L) return // Do nothing if no song or duration is 0

        var newPosition = currentPosition + PlaybackConstants.SeekDuration.FIVE_SECONDS_MS
        if (newPosition > songDuration) {
            newPosition = songDuration
        }

        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SEEK_FORWARD_5_SECONDS) {
            param(AnalyticsConstants.Params.SONG_ID, selectedSong.value?.id?.toString() ?: "none")
            param(AnalyticsConstants.Params.CURRENT_TIME_MS, currentPosition)
            param(AnalyticsConstants.Params.SEEK_POSITION_MS, newPosition)
        }
        seekTo(context, newPosition)
    }

    fun seekBackward5Seconds() {
        val currentPosition = currentPositionFlow.value
        // songDuration is not strictly needed for backward seek bounds, but good to have for context if song isn't loaded.
        val songDuration = selectedSong.value?.duration ?: 0L
        if (selectedSong.value == null) return // Do nothing if no song

        var newPosition = currentPosition - PlaybackConstants.SeekDuration.FIVE_SECONDS_MS
        if (newPosition < 0) {
            newPosition = 0
        }

        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SEEK_BACKWARD_5_SECONDS) {
            param(AnalyticsConstants.Params.SONG_ID, selectedSong.value?.id?.toString() ?: "none")
            param(AnalyticsConstants.Params.CURRENT_TIME_MS, currentPosition)
            param(AnalyticsConstants.Params.SEEK_POSITION_MS, newPosition)
        }
        seekTo(context, newPosition)
    }

    fun updateLrcLineTimestamp(position: Int, newTimestamp: Long) {
        val updatedMap = lrcKeyValuePairs.value.toMutableMap()
        val oldEntry = updatedMap[position]
        oldEntry?.let {
            updatedMap[position] = Tuple(newTimestamp, it.second)
            lrcKeyValuePairs.value = updatedMap
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LYRIC_TIMESTAMP_UPDATED) {
                param(AnalyticsConstants.Params.LINE_INDEX, position.toLong())
                param(AnalyticsConstants.Params.NEW_TIMESTAMP_MS, newTimestamp)
            }
            
            // Force scroll to the clicked item
            // Since the list gets re-sorted, we need to find where this item will be in the sorted list
            val sortedList = _sortedLyrics.value
            val newPositionInSortedList = sortedList.indexOfFirst { it.first == position }
            
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: Timestamp updated for item at original position $position to $newTimestamp.")
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: Item's new position in sorted list: $newPositionInSortedList")
            
            if (newPositionInSortedList != -1) {
                _lineToShowIndex.value = position  // Use original position as lineToShowIndex tracks by original index
                // Re-enable auto-scroll when user clicks on an item
                _isAutoScrollEnabled.value = true
            }
        }
    }

    fun resetTimestamps() {
        val currentLyrics = lrcKeyValuePairs.value
        val resetLyrics = currentLyrics.mapValues { entry ->
            Tuple(0L, entry.value.second)
        }
        lrcKeyValuePairs.value = resetLyrics
    }

    fun isSongAndFileSelected(): Boolean {
        return selectedSong.value != null && pickedTextFileName.value != null
    }

    fun getLrcLinesAsMap(): Map<Int, String> {
        return lrcKeyValuePairs.value
            .filter { it.value.first != 0L || it.key == 0 }
            .mapKeys { it.value.first.toInt() }
            .mapValues { it.value.second }
    }

    fun switchPreview() {
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PREVIEW_TOGGLED) {
            param(AnalyticsConstants.Params.PREVIEW_ACTIVE, (!showPreview.value).toString())
        }
        showPreview.value = !showPreview.value
        
        // Update screen state to control screen timeout
        ScreenStateHolder.isPreviewModeActive = showPreview.value
    }

    private fun startPeriodicUpdates() {
        viewModelScope.launch {
                    while (_enteredScreen.value) {
                        MusicPlayerService.startService(
                            context,
                            MusicPlayerService.ACTION_GET_CURRENT_STATE,
                            navRoute = NavRoutes.Create
                        )
                        delay(1000)
                    }
                }
    }

    private fun onExitScreen() {
        _enteredScreen.value = false
    }

    fun stopPlaying() {
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_STOP)
        isPlaying.value = false
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_PLAYBACK_STOP) { }
    }

    /**
     * Save LRC file and database record
     * @param fileName Name of the file to save
     * @return True if successful, false otherwise
     */
    suspend fun saveLrcFile(fileName: String): Boolean {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Saving LRC file - $fileName")
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LRC_SAVE_STARTED) { param(AnalyticsConstants.Params.FILENAME, fileName) }
        val isFileSaved = saveLrcFileUseCase(context, lrcKeyValuePairs.value, fileName)
        
        if (isFileSaved) {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LRC_SAVE_SUCCEEDED) { param(AnalyticsConstants.Params.FILENAME, fileName) }
            viewModelScope.launch(Dispatchers.IO) {
                val filePath = "${context.filesDir}/$fileName"
                val file = File(
                    songId = selectedSong.value!!.id, 
                    fileName = fileName, 
                    fileType = FileType.LRC, 
                    filePath = filePath
                )
                saveFileUseCase(file)
            }
        } else {
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_LRC_SAVE_FAILED) { param(AnalyticsConstants.Params.FILENAME, fileName) }
        }
        
        return isFileSaved
    }

    /**
     * Resets the playback state when a song is changed
     * This ensures that the player is in a clean state before playing a new song
     */
    fun resetPlaybackState() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Resetting playback state")
        stopPlaying()
        currentPositionFlow.value = 0L
    }

    // Function to hide steps indicator - can be called from any screen
    fun hideStepsIndicator() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Hiding steps indicator")
        showStepsIndicator.value = false
    }
    
    // Function to show steps indicator - can be called from any screen
    fun showStepsIndicator() {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Showing steps indicator")
        showStepsIndicator.value = true
    }
    
    /**
     * Clears all user selections (song and lyrics) when navigating away from the screen
     * This ensures a fresh state when returning to the CreateScreen
     * 
     * @param clearSong Whether to clear the song selection, defaults to true
     * @param clearLyrics Whether to clear the lyrics selection, defaults to true
     * @param stopMusic Whether to stop music playback, defaults to true
     */
    fun clearSelections(clearSong: Boolean = true, clearLyrics: Boolean = true, stopMusic: Boolean = true) {
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Clearing selections - song: $clearSong, lyrics: $clearLyrics, stopMusic: $stopMusic")
        
        if (clearSong) {
            selectedSong.value = null
        }
        
        if (clearLyrics) {
            pickedTextFileName.value = null
            lrcKeyValuePairs.value = emptyMap()
        }
        
        currentPositionFlow.value = 0L
        
        // Only stop playback if requested
        if (stopMusic) {
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: Stopping music playback as part of clearing selections")
            stopPlaying()
        } else {
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: Preserving music playback while clearing selections")
        }
        
        // Reset animation states for next visit
        if (clearSong) songAnimationPlayed.value = false
        if (clearLyrics) lyricsAnimationPlayed.value = false

        val clearedParams = mutableMapOf<String, String>()
        if (clearSong) clearedParams[AnalyticsConstants.Params.CLEARED_SONG] = "true"
        if (clearLyrics) clearedParams[AnalyticsConstants.Params.CLEARED_LYRICS] = "true"
        if (stopMusic) clearedParams[AnalyticsConstants.Params.STOPPED_MUSIC] = "true"

        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.CREATION_STATE_CLEARED) {
            clearedParams.forEach { (key, value) -> param(key, value) }
        }
    }
    
    /**
     * Prepares a default filename for LRC file based on the selected song title
     * Sanitizes the filename by replacing invalid characters with underscores
     * 
     * @return Sanitized song title or "lyrics" if no song is selected
     */
    fun getDefaultLrcFilename(): String {
        return selectedSong.value?.title?.replace(Regex("[\\\\/:*?\"<>|]"), "_") ?: "lyrics"
    }

    // Sorted lyrics for UI operations
    private val _sortedLyrics = MutableStateFlow<List<Triple<Int, Long, String>>>(emptyList())
    val sortedLyrics = _sortedLyrics.asStateFlow()
    
    // Flag to check if all timestamps are 0 (initial state)
    private val _allTimestampsAreZero = MutableStateFlow(false)
    val allTimestampsAreZero = _allTimestampsAreZero.asStateFlow()
    
    // Current line to show in the UI
    private val _lineToShowIndex = MutableStateFlow<Int?>(null)
    val lineToShowIndex = _lineToShowIndex.asStateFlow()
    
    // Index of the last lyric line with a timestamp > 0
    private val _lastPositiveTimestampIndex = MutableStateFlow<Int?>(null)
    val lastPositiveTimestampIndex = _lastPositiveTimestampIndex.asStateFlow()

    // State defining if auto-scrolling should currently be active
    private val _isAutoScrollEnabled = MutableStateFlow(true)
    val isAutoScrollEnabled = _isAutoScrollEnabled.asStateFlow()

    init {
        // Update sorted lyrics whenever lrcKeyValuePairs changes
        viewModelScope.launch {
            lrcKeyValuePairs.collect { pairs ->
                val sorted = pairs.entries
                    .sortedBy { it.value.first }
                    .map { entry -> Triple(entry.key, entry.value.first, entry.value.second) }
                _sortedLyrics.value = sorted
                
                // Check if all timestamps are 0
                _allTimestampsAreZero.value = sorted.isNotEmpty() && sorted.all { it.second == 0L }

                // Calculate and update the index of the last positive timestamp
                updateLastPositiveTimestampIndex(sorted)
            }
        }
        
        // Calculate the line to show based on current position and sorted lyrics
        viewModelScope.launch {
            combine(currentPositionFlow, _sortedLyrics, _allTimestampsAreZero) { position, lyrics, allZero ->
                calculateLineToShow(position, lyrics, allZero)
            }.collect { lineIndex ->
                _lineToShowIndex.value = lineIndex
            }
        }
    }
    
    /**
     * Calculates which line should be shown based on current position
     */
    private fun calculateLineToShow(
        currentPosition: Long,
        sortedLyrics: List<Triple<Int, Long, String>>,
        allTimestampsAreZero: Boolean
    ): Int? {
        if (sortedLyrics.isEmpty()) return null

        // Special handling for low positions when there are items with timestamp 0
        // Keep showing the first timestamp 0 item until we reach the first timestamp > 0
        val firstNonZeroTimestamp = sortedLyrics.firstOrNull { it.second > 0L }?.second
        val firstZeroLine = sortedLyrics.firstOrNull { it.second == 0L }
        
        // Special handling for position 0 or when we're before the first non-zero timestamp
        if (currentPosition == 0L && firstZeroLine != null) {
            return firstZeroLine.first
        }
        
        if (firstZeroLine != null && firstNonZeroTimestamp != null && currentPosition < firstNonZeroTimestamp) {
            // We're still before the first non-zero timestamp, so keep showing the first zero-timestamp item
            return firstZeroLine.first
        }
        
        // If all timestamps are 0, don't auto-scroll at all (except for the special case above)
        if (allTimestampsAreZero) {
            return null
        }
        
        // Find the current line (largest timestamp <= current position)
        val currentLineIndex = sortedLyrics
            .lastOrNull { it.second <= currentPosition }
            ?.first
        
        // If we're at the seekbar position exactly, use that line
        if (sortedLyrics.any { it.second == currentPosition }) {
            return currentLineIndex
        }
        
        // If we found a current line
        if (currentLineIndex != null) {
            // Find the next line's timestamp (if any)
            val currentLinePosition = sortedLyrics.indexOfFirst { it.first == currentLineIndex }
            val nextLine = if (currentLinePosition < sortedLyrics.size - 1) {
                sortedLyrics[currentLinePosition + 1]
            } else null
            
            // If there's a next line and we're very close to it (within 500ms),
            // we should show the next line instead
            if (nextLine != null) {
                val timeToNextLine = nextLine.second - currentPosition
                if (timeToNextLine <= 500) {
                    return nextLine.first
                }
            }

            return currentLineIndex
        }
        
        // If no current line found, show the first upcoming line
        return sortedLyrics.firstOrNull { it.second > currentPosition }?.first
    }
    
    /**
     * Calculates and updates the index of the last lyric line with a timestamp > 0.
     */
    private fun updateLastPositiveTimestampIndex(sortedLyrics: List<Triple<Int, Long, String>>) {
        val lastPositiveIndex = sortedLyrics.lastOrNull { it.second > 0 }?.first
        _lastPositiveTimestampIndex.value = lastPositiveIndex
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Updated lastPositiveTimestampIndex to: $lastPositiveIndex")
    }

    /**
     * Called by the UI to report the visibility state of the last relevant lyric item.
     * Used to disable auto-scrolling if the user scrolls away from the end,
     * and re-enable it if the user scrolls back or a seek makes it visible again.
     */
    fun reportLastItemVisibility(isVisible: Boolean) {
        val currentlyEnabled = _isAutoScrollEnabled.value
        val currentPos = currentPositionFlow.value
        val lastIdx = lastPositiveTimestampIndex.value
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: reportLastItemVisibility called. isVisible: $isVisible, currentlyEnabled: $currentlyEnabled, lastPositiveIndex: $lastIdx, currentPos: $currentPos")

        // ONLY re-enable if it was previously disabled AND the item is now visible.
        if (isVisible && !currentlyEnabled) {
             Timber.tag("DEBUG_FLOW").d("CreateViewModel: -> Auto-scroll RE-ENABLED because last item ($lastIdx) is now visible.")
            _isAutoScrollEnabled.value = true
        } else {
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: -> Visibility report resulted in NO CHANGE to isAutoScrollEnabled ($currentlyEnabled). isVisible=$isVisible")
        }
        // NOTE: We no longer disable auto-scroll here based on visibility alone.
    }

    /**
     * Explicitly disables auto-scrolling, typically called when manual scroll is detected.
     */
    fun disableAutoScroll() {
        if (_isAutoScrollEnabled.value) {
            Timber.tag("DEBUG_FLOW").d("CreateViewModel: disableAutoScroll() called. Disabling auto-scroll.")
            _isAutoScrollEnabled.value = false
        }
    }

    private fun setupInternalMusicReceiver() {
        // Create dummy flows for parameters not managed by CreateViewModel directly,
        // as MusicBroadcastReceiver expects them, but CreateViewModel only cares about songs list for now.
        val dummyCurrentSongFlow = MutableStateFlow<Song?>(null)
        val dummyIsPlayingFlow = MutableStateFlow(false)
        val dummyCurrentPositionFlow = MutableStateFlow(0L)
        val dummyLrcLinesFlow = MutableStateFlow<Map<Int, String>>(emptyMap())

        internalMusicReceiver = MusicBroadcastReceiver(
            _songsFlow = songs, // Pass CreateViewModel's songs flow
            _currentSongFlow = dummyCurrentSongFlow,
            isPlayingFlow = dummyIsPlayingFlow,
            _currentPositionFlow = dummyCurrentPositionFlow,
            _lrcLinesFlow = dummyLrcLinesFlow
        )

        val intentFilter = IntentFilter(MusicPlayerService.ACTION_SONGS_LOADED)
        // If CreateViewModel needs other song-related updates in the future, add their actions here.
        
        ContextCompat.registerReceiver(
            context,
            internalMusicReceiver,
            intentFilter,
            ContextCompat.RECEIVER_NOT_EXPORTED 
        )
        Timber.tag("DEBUG_FLOW").d("CreateViewModel: Registered internal MusicBroadcastReceiver for ACTION_SONGS_LOADED.")
    }
    
    override fun onCleared() {
        super.onCleared()
        // Reset preview mode state when ViewModel is cleared
        ScreenStateHolder.isPreviewModeActive = false
    }
}
