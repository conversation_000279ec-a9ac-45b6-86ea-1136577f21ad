package soly.lyricsgenerator.ui.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.net.Uri
import androidx.core.content.ContextCompat
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.constants.ServiceConstants
import soly.lyricsgenerator.domain.usecase.database.file.SaveFileUseCase
import soly.lyricsgenerator.domain.model.FileContentState
import soly.lyricsgenerator.domain.usecase.lyrics.GetFileContentForSongUseCase
import soly.lyricsgenerator.domain.usecase.audiotag.GetEmbeddedLyricsUseCase
import soly.lyricsgenerator.domain.usecase.database.song.ToggleSongFavoriteUseCase
import soly.lyricsgenerator.domain.usecase.database.song.GetSongFavoriteStatusUseCase
import soly.lyricsgenerator.domain.usecase.MusicUseCase
import soly.lyricsgenerator.domain.usecase.GetSongsByArtistUseCase
import soly.lyricsgenerator.domain.usecase.preferences.SaveArtistFilterUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadArtistFilterUseCase
import soly.lyricsgenerator.domain.usecase.shared_audio.ResolveSharedAudioUriUseCase
import soly.lyricsgenerator.ui.screens.song_details_screen.FavoriteState
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.domain.repository.SongsCache
import soly.lyricsgenerator.domain.model.FilteredSongsState
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import com.google.firebase.analytics.FirebaseAnalytics
import timber.log.Timber
import javax.inject.Inject
import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.model.SortOrder

// Data class for the UI state of the music screen
data class MusicScreenUIState(
    val songs: List<Song> = emptyList(),
    val currentlyPlayingSong: Song? = null,
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val hasAudioPermission: Boolean = true,
    val isLoading: Boolean = false
)

@HiltViewModel
class MusicViewModel @Inject constructor(
    private val context: Application,
    private val saveFileUseCase: SaveFileUseCase,
    private val getFileContentForSongUseCase: GetFileContentForSongUseCase,
    private val getEmbeddedLyricsUseCase: GetEmbeddedLyricsUseCase,
    private val toggleSongFavoriteUseCase: ToggleSongFavoriteUseCase,
    private val getSongFavoriteStatusUseCase: GetSongFavoriteStatusUseCase,
    private val musicUseCase: MusicUseCase,
    private val getSongsByArtistUseCase: GetSongsByArtistUseCase,
    private val saveArtistFilterUseCase: SaveArtistFilterUseCase,
    private val loadArtistFilterUseCase: LoadArtistFilterUseCase,
    private val resolveSharedAudioUriUseCase: ResolveSharedAudioUriUseCase,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PreferenceKeys.LYRIC_GENERATOR_PREFS, Context.MODE_PRIVATE)
    }

    // Handle shared audio URIs from SavedStateHandle
    private val _sharedAudioUris = MutableStateFlow<List<Uri>>(emptyList())
    val sharedAudioUris: StateFlow<List<Uri>> = _sharedAudioUris.asStateFlow()
    
    // Flag to prevent multiple shared audio processing
    private var isProcessingSharedAudio = false

    init {
        // Check for shared audio URIs in SavedStateHandle
        val uriStrings: List<String>? = savedStateHandle.get<List<String>>("shared_audio_uris")
        if (!uriStrings.isNullOrEmpty()) {
            val uris = uriStrings.mapNotNull { uriString ->
                try {
                    Uri.parse(uriString)
                } catch (e: Exception) {
                    Timber.tag("DEBUG_FLOW").e(e, "MusicViewModel: Failed to parse shared audio URI: $uriString")
                    null
                }
            }
            _sharedAudioUris.value = uris
            
            // Log analytics for received shared audio
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_RECEIVED_IN_MUSIC) {
                param(AnalyticsConstants.Params.MULTIPLE_AUDIO_COUNT, uris.size.toLong())
            }
            
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: Received ${uris.size} shared audio URIs")
            
            // Process shared audio after songs are loaded
            processSharedAudioWhenReady()
        }
    }

    /**
     * Sets shared audio URIs for the music screen
     */
    fun setSharedAudioUris(uris: List<Uri>) {
        Timber.tag("DEBUG_FLOW").d("MusicViewModel: setSharedAudioUris called with ${uris.size} URIs")
        uris.forEachIndexed { index, uri ->
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: URI[$index] = $uri")
        }
        
        _sharedAudioUris.value = uris
        // Save to SavedStateHandle for persistence
        savedStateHandle["shared_audio_uris"] = uris.map { it.toString() }
        
        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Shared audio URIs set, hasAudioPermission=${_hasAudioPermission.value}, songsCount=${_songsFlow.value.size}")
        
        // Trigger processing if URIs are provided
        if (uris.isNotEmpty()) {
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: Triggering processSharedAudioWhenReady")
            processSharedAudioWhenReady()
        }
    }

    /**
     * Clears shared audio URIs
     */
    fun clearSharedAudioUris() {
        _sharedAudioUris.value = emptyList()
        savedStateHandle.remove<List<String>>("shared_audio_uris")
        isProcessingSharedAudio = false // Reset the processing flag
        
        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Cleared shared audio URIs")
    }
    
    /**
     * Processes shared audio URIs when songs are loaded
     */
    private fun processSharedAudioWhenReady() {
        if (isProcessingSharedAudio) {
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: Already processing shared audio, but will reset and retry for new URIs")
            isProcessingSharedAudio = false
        }
        
        isProcessingSharedAudio = true
        
        viewModelScope.launch {
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: Starting processSharedAudioWhenReady")
            
            try {
                // First ensure we have audio permission
                if (!_hasAudioPermission.value) {
                    Timber.tag("DEBUG_FLOW").w("MusicViewModel: No audio permission, cannot process shared audio")
                    clearSharedAudioUris()
                    return@launch
                }
                
                // Ensure songs are loaded (this will trigger loading if empty)
                loadSongsIfEmpty()
                
                // Wait for songs to be loaded
                _songsFlow.collect { songs ->
                    val sharedUris = _sharedAudioUris.value
                    
                    if (songs.isNotEmpty() && sharedUris.isNotEmpty()) {
                        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Processing ${sharedUris.size} shared URIs with ${songs.size} loaded songs")
                        
                        // Find the first matching song
                        val matchingSong = findSongByUri(songs, sharedUris.first())
                        
                        if (matchingSong != null) {
                            Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found matching song: ${matchingSong.title} for shared URI")
                            
                            // Log analytics for successful match
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_SONG_MATCHED) {
                                param(AnalyticsConstants.Params.SONG_ID, matchingSong.id.toString())
                                param(AnalyticsConstants.Params.SONG_TITLE, matchingSong.title ?: AnalyticsConstants.Params.UNKNOWN)
                            }
                            
                            // Play the matched song
                            withContext(Dispatchers.Main) {
                                playSong(matchingSong)
                            }
                            
                            // Clear shared URIs after processing
                            clearSharedAudioUris()
                            
                            // Exit the collection since we've processed the shared audio
                            return@collect
                        } else {
                            Timber.tag("DEBUG_FLOW").w("MusicViewModel: No matching song found for shared URI")
                            
                            // Log analytics for no match
                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_NO_MATCH) {
                                param(AnalyticsConstants.Params.URI_COUNT, sharedUris.size.toLong())
                            }
                            
                            // Clear URIs even if no match found to avoid retrying
                            clearSharedAudioUris()
                            return@collect
                        }
                    } else if (sharedUris.isNotEmpty() && songs.isEmpty()) {
                        // Songs are still loading, keep waiting
                        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Waiting for songs to load before processing shared audio")
                    }
                }
            } finally {
                isProcessingSharedAudio = false
            }
        }
    }
    
    /**
     * Finds a song in the list that matches the given URI using enhanced resolution
     */
    private suspend fun findSongByUri(songs: List<Song>, uri: Uri): Song? {
        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Looking for song with URI: $uri")
        
        // First try exact match with mediaStoreUri
        songs.forEach { song ->
            if (song.mediaStoreUri == uri) {
                Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found exact match by mediaStoreUri")
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_SONG_MATCHED) {
                    param(AnalyticsConstants.Params.MATCHING_STRATEGY, "exact_uri_match")
                    param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                }
                return song
            }
        }
        
        // Use ResolveSharedAudioUriUseCase for enhanced resolution
        val resolvedInfo = resolveSharedAudioUriUseCase(uri)
        if (resolvedInfo != null) {
            Timber.tag("DEBUG_FLOW").d("MusicViewModel: URI resolved to: path=${resolvedInfo.resolvedPath}, display=${resolvedInfo.displayName}, filename=${resolvedInfo.fileName}")
            
            // Log successful resolution
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_URI_RESOLVED) {
                param(AnalyticsConstants.Params.RESOLVED_PATH, resolvedInfo.resolvedPath ?: "null")
                param(AnalyticsConstants.Params.DISPLAY_NAME, resolvedInfo.displayName ?: "null")
            }
            
            // Try matching by resolved path
            resolvedInfo.resolvedPath?.let { resolvedPath ->
                songs.forEach { song ->
                    if (song.data == resolvedPath) {
                        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found exact match by resolved path")
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PATH_MATCHED) {
                            param(AnalyticsConstants.Params.MATCHING_STRATEGY, "resolved_path_exact")
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                        }
                        return song
                    }
                }
            }
            
            // Try matching by filename from resolved info
            resolvedInfo.fileName?.let { fileName ->
                songs.forEach { song ->
                    val songFileName = song.data.substringAfterLast('/')
                    if (songFileName == fileName) {
                        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found match by resolved filename")
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PATH_MATCHED) {
                            param(AnalyticsConstants.Params.MATCHING_STRATEGY, "resolved_filename_exact")
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                        }
                        return song
                    }
                }
            }
            
            // Try partial matching by filename
            resolvedInfo.fileName?.let { fileName ->
                songs.forEach { song ->
                    if (song.data.endsWith(fileName)) {
                        Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found partial match by resolved filename")
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PATH_MATCHED) {
                            param(AnalyticsConstants.Params.MATCHING_STRATEGY, "resolved_filename_partial")
                            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                        }
                        return song
                    }
                }
            }
        } else {
            Timber.tag("DEBUG_FLOW").w("MusicViewModel: Failed to resolve URI")
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_RESOLUTION_FAILED) {
                param(AnalyticsConstants.Params.ORIGINAL_URI_SCHEME, uri.scheme ?: "null")
            }
        }
        
        // Fallback: try matching by URI path
        val sharedPath = uri.path
        if (sharedPath != null) {
            songs.forEach { song ->
                if (song.data == sharedPath || song.data.endsWith(sharedPath)) {
                    Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found fallback match by URI path")
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PATH_MATCHED) {
                        param(AnalyticsConstants.Params.MATCHING_STRATEGY, "fallback_uri_path")
                        param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                    }
                    return song
                }
            }
        }
        
        // Final fallback: try matching by last path segment
        val fileName = uri.lastPathSegment
        if (fileName != null) {
            songs.forEach { song ->
                if (song.data.endsWith(fileName)) {
                    Timber.tag("DEBUG_FLOW").d("MusicViewModel: Found fallback match by filename")
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PATH_MATCHED) {
                        param(AnalyticsConstants.Params.MATCHING_STRATEGY, "fallback_filename")
                        param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
                    }
                    return song
                }
            }
        }
        
        return null
    }

    private val _shuffleEnabled = MutableStateFlow(prefs.getBoolean(PreferenceKeys.SHUFFLE_ENABLED, false))
    val shuffleEnabled: StateFlow<Boolean> = _shuffleEnabled.asStateFlow()

    fun toggleShuffle() {
        val newValue = !_shuffleEnabled.value
        _shuffleEnabled.value = newValue
        prefs.edit().putBoolean(PreferenceKeys.SHUFFLE_ENABLED, newValue).apply()

        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_SHUFFLE_TOGGLED) {
            param(AnalyticsConstants.Params.SHUFFLE_ENABLED, newValue.toString())
        }

        // Notify service to reload songs with new shuffle state
        MusicPlayerService.startService(
            context,
            MusicPlayerService.ACTION_LOAD_SONGS
        )
    }

    private val _showFavoritesOnly = MutableStateFlow(prefs.getBoolean(PreferenceKeys.SHOW_FAVORITES_ONLY, false))
    val showFavoritesOnly: StateFlow<Boolean> = _showFavoritesOnly.asStateFlow()

    fun toggleShowFavoritesOnly() {
        val newValue = !_showFavoritesOnly.value
        _showFavoritesOnly.value = newValue
        prefs.edit().putBoolean(PreferenceKeys.SHOW_FAVORITES_ONLY, newValue).apply()
        
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_FAVORITES_FILTER_TOGGLED) {
            param(AnalyticsConstants.Params.FAVORITES_ONLY, newValue.toString())
        }
        
        // Notify service to reload songs with new filter state
        MusicPlayerService.startService(
            context,
            MusicPlayerService.ACTION_LOAD_SONGS
        )
    }

    fun setArtistFilter(artist: String) {
        viewModelScope.launch {
            saveArtistFilterUseCase(artist)
            _artistFilter.value = artist
            
            // Notify service to reload songs with new filter state
            MusicPlayerService.startService(
                context,
                MusicPlayerService.ACTION_LOAD_SONGS
            )
        }
    }
    
    // Sort functionality
    private val _sortType = MutableStateFlow(loadSortType())
    val sortType: StateFlow<SortType> = _sortType.asStateFlow()
    
    private val _sortOrder = MutableStateFlow(loadSortOrder())
    val sortOrder: StateFlow<SortOrder> = _sortOrder.asStateFlow()
    
    private val _showSortBottomSheet = MutableStateFlow(false)
    val showSortBottomSheet: StateFlow<Boolean> = _showSortBottomSheet.asStateFlow()

    private val _showArtistFilterBottomSheet = MutableStateFlow(false)
    val showArtistFilterBottomSheet: StateFlow<Boolean> = _showArtistFilterBottomSheet.asStateFlow()
    
    private fun loadSortType(): SortType {
        val sortTypeName = prefs.getString(PreferenceKeys.SORT_TYPE, PreferenceKeys.DEFAULT_SORT_TYPE) ?: PreferenceKeys.DEFAULT_SORT_TYPE
        return SortType.fromName(sortTypeName)
    }
    
    private fun loadSortOrder(): SortOrder {
        val sortOrderName = prefs.getString(PreferenceKeys.SORT_ORDER, PreferenceKeys.DEFAULT_SORT_ORDER) ?: PreferenceKeys.DEFAULT_SORT_ORDER
        return SortOrder.fromName(sortOrderName)
    }
    
    
    fun onSortChanged(newSortType: SortType) {
        val currentSortType = _sortType.value
        
        val (finalSortType, finalSortOrder) = if (currentSortType::class == newSortType::class) {
            // Toggle order if same type selected
            val newOrder = if (_sortOrder.value is SortOrder.Ascending) {
                SortOrder.Descending
            } else {
                SortOrder.Ascending
            }
            newSortType to newOrder
        } else {
            // Set new type with ascending order
            newSortType to SortOrder.Ascending
        }
        
        // Update local state for UI
        _sortType.value = finalSortType
        _sortOrder.value = finalSortOrder
        
        // Hide bottom sheet
        _showSortBottomSheet.value = false
        
        // Trigger service to handle sorting and song reload
        MusicPlayerService.startService(
            context = context,
            action = ServiceConstants.Actions.UPDATE_SORTING,
            sortType = finalSortType::class.simpleName,
            sortOrder = finalSortOrder::class.simpleName
        )
    }
    
    fun showSortBottomSheet() {
        _showSortBottomSheet.value = true
    }
    
    fun hideSortBottomSheet() {
        _showSortBottomSheet.value = false
    }

    fun showArtistFilterBottomSheet() {
        _showArtistFilterBottomSheet.value = true
    }

    fun hideArtistFilterBottomSheet() {
        _showArtistFilterBottomSheet.value = false
    }
    
    private val _songsFlow = MutableStateFlow<List<Song>>(emptyList())

    private val _currentPosition = MutableStateFlow(0L)
    private val currentPosition: StateFlow<Long> = _currentPosition

    private val _currentlyPlayingSong = MutableStateFlow<Song?>(null)
    private val currentlyPlayingSong: StateFlow<Song?> = _currentlyPlayingSong

    private val _enteredScreen = MutableStateFlow(false)
    val enteredScreen: StateFlow<Boolean> = _enteredScreen

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery

    private val _artistFilter = MutableStateFlow(PreferenceKeys.DEFAULT_ARTIST_FILTER)
    val artistFilter: StateFlow<String> = _artistFilter.asStateFlow()
    
    private val _hasAudioPermission = MutableStateFlow(true)
    val hasAudioPermission: StateFlow<Boolean> = _hasAudioPermission
    
    private val _isLoadingSongs = MutableStateFlow(false)
    val isLoadingSongs: StateFlow<Boolean> = _isLoadingSongs

    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying

    private val _isStopped = MutableStateFlow(false)
    val isStopped: StateFlow<Boolean> = _isStopped

    // LRC lines state (kept for backward compatibility)
    private val _lrcLines = MutableStateFlow<Map<Int, String>>(emptyMap())
    val lrcLines: StateFlow<Map<Int, String>> = _lrcLines
    
    // File content state for both LRC and TXT files
    private val _fileContentState = MutableStateFlow<FileContentState>(FileContentState.Empty)
    val fileContentState: StateFlow<FileContentState> = _fileContentState

    private val _favoriteState = MutableStateFlow<FavoriteState>(FavoriteState.Loading)
    val favoriteState: StateFlow<FavoriteState> = _favoriteState
    
    // Debouncing for favorite toggle
    private var favoriteToggleJob: Job? = null

    private var isReceiverRegistered = false
    
    // Track the current navigation destination
    private var _currentDestination = MutableStateFlow<NavRoutes?>(null)

    // Flow for filtered songs (sorting and filtering now handled by service)
    val filteredSongsFlow: StateFlow<FilteredSongsState> = combine(
        _songsFlow, _searchQuery, _showFavoritesOnly, _artistFilter
    ) { songs, query, favoritesOnly, artistFilter ->
        // IMPORTANT: Songs from _songsFlow are already sorted and filtered by the MusicPlayerService
        // We must preserve this order and only apply search filter
        var filteredSongs = songs
        
        // Favorites and artist filters are now handled by the Service
        // The Service applies both filters to its queue and cache

        // Apply search filter (only filtering handled in ViewModel now)
        if (query.isNotEmpty()) {
            filteredSongs = filteredSongs.filter {
                it.title.contains(query, ignoreCase = true) || it.artist.contains(query, ignoreCase = true)
            }
        }
        
        // CRITICAL: Never re-sort here - preserve the order from MusicPlayerService
        FilteredSongsState(songs = filteredSongs)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Lazily, // Changed from Eagerly to Lazily to avoid premature processing
        initialValue = FilteredSongsState(songs = emptyList())
    )
    
    // Combined UI state for the music screen
    private val _uiState = MutableStateFlow(MusicScreenUIState())
    
    fun getUIState(): StateFlow<MusicScreenUIState> = combine(
        _songsFlow,
        _currentlyPlayingSong,
        _isPlaying,
        _currentPosition,
        _hasAudioPermission,
        _isLoadingSongs
    ) { flows ->
        val songs = flows[0] as List<Song>
        val currentSong = flows[1] as Song?
        val isPlaying = flows[2] as Boolean
        val position = flows[3] as Long
        val hasPermission = flows[4] as Boolean
        val isLoading = flows[5] as Boolean
        
        MusicScreenUIState(
            songs = songs,
            currentlyPlayingSong = currentSong,
            isPlaying = isPlaying,
            currentPosition = position,
            hasAudioPermission = hasPermission,
            isLoading = isLoading
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = MusicScreenUIState()
    )

    private val songsReceiver = MusicBroadcastReceiver(_songsFlow, _currentlyPlayingSong, _isPlaying, _currentPosition, _lrcLines, null, _isLoadingSongs)

    private val _navigationEvent = MutableSharedFlow<NavRoutes>()
    val navigationEvent: SharedFlow<NavRoutes> = _navigationEvent

    fun onInfoClicked() {
        viewModelScope.launch {
            _navigationEvent.emit(NavRoutes.SongDetails)
            // Update destination tracking
            _currentDestination.value = NavRoutes.SongDetails
        }
    }

    init {
        onEnterScreen()
        
        // Check if songs are cached before requesting from service
        if (SongsCache.hasSongs()) {
            val cachedSongs = SongsCache.getSongs()
            _songsFlow.value = cachedSongs
            
            // CRITICAL FIX: Force service to re-apply sorting even with cached songs
            // This ensures the sorting preferences are applied to cached songs
            MusicPlayerService.startService(context, MusicPlayerService.ACTION_LOAD_SONGS)
        } else {
            loadSongsIfEmpty()
        }

        startPeriodicUpdates()

        viewModelScope.launch {
            _artistFilter.value = loadArtistFilterUseCase()
        }

        viewModelScope.launch {
            artistFilter.collect { artist ->
                // Artist filtering is now handled consistently by MusicPlayerService
                // Service applies filter to both songsQueue and cache
                // No additional UI filtering needed
            }
        }

        viewModelScope.launch {
            currentlyPlayingSong.collect { song ->
                song?.let {
                    loadFileContent(it.id)
                }
            }
        }
        
        // Safety check to preserve lrcLines if they are accidentally cleared
        viewModelScope.launch {
            _lrcLines.collect { lines ->
                if (lines.isEmpty() && _currentlyPlayingSong.value != null) {
                    val currentSongId = _currentlyPlayingSong.value?.id
                    currentSongId?.let { loadFileContent(it) }
                }
            }
        }
        
    }
    
    /**
     * Sets the audio permission state
     */
    fun setHasAudioPermission(hasPermission: Boolean) {
        _hasAudioPermission.value = hasPermission
    }

    fun onEnterScreen() {
        // Log screen view analytics event
        GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, AnalyticsConstants.ScreenNames.MUSIC_SCREEN)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, AnalyticsConstants.ScreenNames.MUSIC_SCREEN + ".kt")
        }
        
        ensureBroadcastReceiverRegistered()
        _enteredScreen.value = true
    }

    // New method to ensure the broadcast receiver is registered
    private fun ensureBroadcastReceiverRegistered() {
        if (!isReceiverRegistered) {
            val intentFilter = IntentFilter(MusicPlayerService.ACTION_SONGS_LOADED).apply {
                addAction(MusicPlayerService.ACTION_SONGS_LOADED)
                addAction(MusicPlayerService.ACTION_SONG_CHANGED)
                addAction(MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED)
                addAction(MusicPlayerService.ACTION_POSITION_UPDATED)
                addAction(MusicPlayerService.ACTION_STATE_UPDATED)
            }
            try {
                ContextCompat.registerReceiver(
                    context,
                    songsReceiver,
                    intentFilter,
                    ContextCompat.RECEIVER_NOT_EXPORTED
                )
                isReceiverRegistered = true
            } catch (e: Exception) {
                // Registration failed
            }
        }
    }

    private fun startPeriodicUpdates() {
        viewModelScope.launch {
            _enteredScreen.collect { entered ->
                if (entered) {
                    while (_enteredScreen.value) {
                        MusicPlayerService.startService(context, MusicPlayerService.ACTION_GET_CURRENT_STATE)
                        delay(1000)
                    }
                }
            }
        }
    }

    fun updateLrcLines(newLines: Map<Int, String>) {
        _lrcLines.value = newLines
    }
    
    /**
     * Updates the file content state with new LRC content
     * This ensures the UI renders the updated content immediately
     */
    fun updateFileContentWithLrcLines(newLines: Map<Int, String>) {
        _fileContentState.value = FileContentState.LrcContent(newLines)
        _lrcLines.value = newLines // Also update lrcLines for backward compatibility
    }

    override fun onCleared() {
        super.onCleared()
        safelyUnregisterReceiver("onCleared")
        _enteredScreen.value = false
    }

    fun onExitScreen() {
        _enteredScreen.value = false // Will stop the periodic updates

        // REFINED: Keep receiver registered even when screen is not visible
        // If we unregister, we won't get song changes etc. when navigating back.
    }
    
    // New helper method to safely unregister the receiver
    private fun safelyUnregisterReceiver(source: String) {
        if (isReceiverRegistered) {
            try {
                context.unregisterReceiver(songsReceiver)
                isReceiverRegistered = false
            } catch (e: Exception) {
                // Unregister failed
            }
        }
    }

    fun playSong(song: Song) {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PLAY_SONG) {
            param(AnalyticsConstants.Params.SONG_ID, song.id.toString())
            param(AnalyticsConstants.Params.SONG_TITLE, song.title ?: AnalyticsConstants.Params.UNKNOWN)
            param(AnalyticsConstants.Params.SONG_ARTIST, song.artist ?: AnalyticsConstants.Params.UNKNOWN)
        }
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_PLAY, song, playMode = MusicPlayerService.MODE_LISTENING)
        _currentlyPlayingSong.value = song
        _isPlaying.value = true
        _isStopped.value = false
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_GET_CURRENT_STATE)
    }

    fun pausePlaying() {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PAUSE_SONG) {}
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_PAUSE)
        _isPlaying.value = false
    }

    fun resumePlaying() {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_RESUME_SONG) {}
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_RESUME)
        _isPlaying.value = true
        _isStopped.value = false
    }

    fun seekTo(position: Long) {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_SEEK_SONG) {
            param(AnalyticsConstants.Params.SEEK_POSITION_MS, position)
        }
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_SEEK_TO, null, position)
    }

    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    fun playNextSong() {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_NEXT_SONG_CLICK) {}
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_PLAY_NEXT)
    }

    fun playPreviousSong() {
        // Log analytics event
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_PREVIOUS_SONG_CLICK) {}
        
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_PLAY_PREVIOUS)
    }

    fun loadLrcFile(songId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val fileContent = getFileContentForSongUseCase(songId)
            _lrcLines.value = when (fileContent) {
                is FileContentState.LrcContent -> fileContent.lrcLines
                else -> emptyMap()
            }
        }
    }
    
    fun loadFileContent(songId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            // First try to get content from linked files
            var content = getFileContentForSongUseCase(songId)
            
            // If no linked files found, try embedded lyrics as fallback
            if (content.isEmpty()) {
                // Find the song to extract embedded lyrics
                val song = _songsFlow.value.find { it.id == songId }
                if (song != null) {
                    content = getEmbeddedLyricsUseCase(song)
                }
            }
            
            _fileContentState.value = content
            
            // Update lrcLines for backward compatibility
            _lrcLines.value = content.extractLrcLines()
        }
    }

    fun loadSongsIfEmpty() {
        if (_songsFlow.value.isEmpty() && _hasAudioPermission.value) {
            _isLoadingSongs.value = true
            MusicPlayerService.startService(context, MusicPlayerService.ACTION_LOAD_SONGS)
        }
    }
    
    /**
     * Force reload songs from service (with current sorting)
     */
    fun forceReloadSongs() {
        _isLoadingSongs.value = true
        MusicPlayerService.startService(context, MusicPlayerService.ACTION_LOAD_SONGS)
    }

    fun saveFile(filePath: String, fileName: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val song = _currentlyPlayingSong.value
                if (song != null) {
                    val file = File(
                        songId = song.id,
                        filePath = filePath,
                        fileType = FileType.LRC,
                        fileName = fileName
                    )
                    saveFileUseCase(file)
                    
                    // After successful save, refresh the file content for this song
                    withContext(Dispatchers.Main) {
                        loadFileContent(song.id)
                    }
                }
            } catch (e: Exception) {
                // File save failed
            }
        }
    }

    fun toggleFavorite(songId: Long) {
        
        // Cancel any pending favorite toggle
        favoriteToggleJob?.cancel()
        
        // Set loading state on main thread
        _favoriteState.value = FavoriteState.Loading
        
        favoriteToggleJob = viewModelScope.launch {
            try {
                // Perform database operation on IO thread
                withContext(Dispatchers.IO) {
                    toggleSongFavoriteUseCase(songId)
                }
                
                // Update UI state on main thread
                val isFavorite = withContext(Dispatchers.IO) {
                    val status = getSongFavoriteStatusUseCase(songId)
                    status
                }

                // Log analytics event
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MUSIC_FAVORITE_TOGGLED) {
                    param(AnalyticsConstants.Params.SONG_ID, songId.toString())
                    param(AnalyticsConstants.Params.IS_FAVORITE, isFavorite.toString())
                }

                _favoriteState.value = if (isFavorite) FavoriteState.Favorite else FavoriteState.NotFavorite
                
                // Instead of manually updating local state, reload songs from service
                // This ensures we get the fresh data with correct favorite status
                MusicPlayerService.startService(context, MusicPlayerService.ACTION_LOAD_SONGS)
                
            } catch (e: Exception) {
                e.printStackTrace()
                _favoriteState.value = FavoriteState.NotFavorite
            }
        }
    }

    fun loadFavoriteStatus(songId: Long) {
        viewModelScope.launch {
            try {
                val isFavorite = withContext(Dispatchers.IO) {
                    val status = getSongFavoriteStatusUseCase(songId)
                    status
                }
                _favoriteState.value = if (isFavorite) FavoriteState.Favorite else FavoriteState.NotFavorite
            } catch (e: Exception) {
                e.printStackTrace()
                _favoriteState.value = FavoriteState.NotFavorite
            }
        }
    }
}
