package soly.lyricsgenerator.features.shared_audio.data.repositories

import android.content.ContentResolver
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import soly.lyricsgenerator.features.shared_audio.data.validators.AudioValidator
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult
import soly.lyricsgenerator.features.shared_audio.domain.repository.SharedAudioRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of SharedAudioRepository.
 * Handles audio file validation and metadata extraction.
 */
@Singleton
class SharedAudioRepositoryImpl @Inject constructor(
    private val contentResolver: ContentResolver,
    private val audioValidator: AudioValidator
) : SharedAudioRepository {
    
    override suspend fun validateAudioUri(uri: Uri): Boolean {
        return audioValidator.validate(uri, contentResolver)
    }
    
    override suspend fun extractAudioMetadata(uri: Uri): Map<String, Any> {
        Timber.tag("DEBUG_FLOW").d("SharedAudioRepositoryImpl: Extracting metadata for URI $uri")
        
        return try {
            val metadata = mutableMapOf<String, Any>()
            
            // Try to get metadata from MediaStore first
            val mediaStoreMetadata = getMediaStoreMetadata(uri)
            metadata.putAll(mediaStoreMetadata)
            
            // Add basic URI information
            metadata["uri"] = uri.toString()
            metadata["scheme"] = uri.scheme ?: "unknown"
            metadata["mime_type"] = audioValidator.getMimeType(uri, contentResolver) ?: "unknown"
            
            // Try to get file size
            getFileSize(uri)?.let { size ->
                metadata["file_size_bytes"] = size
            }
            
            Timber.tag("DEBUG_FLOW").d("SharedAudioRepositoryImpl: Extracted metadata for URI $uri: $metadata")
            metadata
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "SharedAudioRepositoryImpl: Failed to extract metadata for URI $uri")
            mapOf(
                "uri" to uri.toString(),
                "error" to "Metadata extraction failed: ${exception.message}"
            )
        }
    }
    
    override suspend fun validateMultipleAudioUris(uris: List<Uri>): SharedAudioResult {
        Timber.tag("DEBUG_FLOW").d("SharedAudioRepositoryImpl: Validating ${uris.size} URIs")
        
        if (uris.isEmpty()) {
            return SharedAudioResult.NoContent
        }
        
        return try {
            val validUris = audioValidator.validateMultiple(uris, contentResolver)
            
            when {
                validUris.isEmpty() -> {
                    SharedAudioResult.ValidationError("No valid audio files found in shared content")
                }
                validUris.size == uris.size -> {
                    SharedAudioResult.Success(validUris)
                }
                else -> {
                    // Some files are valid, some are not
                    val metadata = mapOf(
                        "total_shared" to uris.size,
                        "valid_files" to validUris.size,
                        "invalid_files" to (uris.size - validUris.size)
                    )
                    SharedAudioResult.Success(validUris, metadata)
                }
            }
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "SharedAudioRepositoryImpl: Validation failed")
            SharedAudioResult.ProcessingError(
                "Failed to validate audio files: ${exception.message}",
                exception
            )
        }
    }
    
    override suspend fun getMimeType(uri: Uri): String? {
        return audioValidator.getMimeType(uri, contentResolver)
    }
    
    override suspend fun canReadUri(uri: Uri): Boolean {
        return try {
            contentResolver.openInputStream(uri)?.use { true } ?: false
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").w(exception, "SharedAudioRepositoryImpl: Cannot read URI $uri")
            false
        }
    }
    
    /**
     * Attempts to get metadata from MediaStore
     */
    private fun getMediaStoreMetadata(uri: Uri): Map<String, Any> {
        val metadata = mutableMapOf<String, Any>()
        
        try {
            val projection = arrayOf(
                MediaStore.Audio.Media.TITLE,
                MediaStore.Audio.Media.ARTIST,
                MediaStore.Audio.Media.ALBUM,
                MediaStore.Audio.Media.DURATION,
                MediaStore.Audio.Media.SIZE,
                MediaStore.Audio.Media.DISPLAY_NAME
            )
            
            contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    metadata.putAll(extractFromCursor(cursor))
                }
            }
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").w(exception, "SharedAudioRepositoryImpl: Failed to get MediaStore metadata for $uri")
        }
        
        return metadata
    }
    
    /**
     * Extracts metadata from cursor
     */
    private fun extractFromCursor(cursor: Cursor): Map<String, Any> {
        val metadata = mutableMapOf<String, Any>()
        
        try {
            cursor.getColumnIndex(MediaStore.Audio.Media.TITLE).takeIf { it >= 0 }?.let { index ->
                cursor.getString(index)?.let { metadata["title"] = it }
            }
            
            cursor.getColumnIndex(MediaStore.Audio.Media.ARTIST).takeIf { it >= 0 }?.let { index ->
                cursor.getString(index)?.let { metadata["artist"] = it }
            }
            
            cursor.getColumnIndex(MediaStore.Audio.Media.ALBUM).takeIf { it >= 0 }?.let { index ->
                cursor.getString(index)?.let { metadata["album"] = it }
            }
            
            cursor.getColumnIndex(MediaStore.Audio.Media.DURATION).takeIf { it >= 0 }?.let { index ->
                metadata["duration_ms"] = cursor.getLong(index)
            }
            
            cursor.getColumnIndex(MediaStore.Audio.Media.SIZE).takeIf { it >= 0 }?.let { index ->
                metadata["file_size_bytes"] = cursor.getLong(index)
            }
            
            cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME).takeIf { it >= 0 }?.let { index ->
                cursor.getString(index)?.let { metadata["display_name"] = it }
            }
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").w(exception, "SharedAudioRepositoryImpl: Error extracting from cursor")
        }
        
        return metadata
    }
    
    /**
     * Gets file size from content resolver
     */
    private fun getFileSize(uri: Uri): Long? {
        return try {
            contentResolver.openAssetFileDescriptor(uri, "r")?.use { afd ->
                afd.length
            }
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").w(exception, "SharedAudioRepositoryImpl: Failed to get file size for $uri")
            null
        }
    }
}