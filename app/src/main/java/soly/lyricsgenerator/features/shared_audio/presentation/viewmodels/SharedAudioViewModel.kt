package soly.lyricsgenerator.features.shared_audio.presentation.viewmodels

import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.constants.IntentConstants
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioIntent
import soly.lyricsgenerator.features.shared_audio.domain.use_cases.ProcessSharedAudioUseCase
import soly.lyricsgenerator.features.shared_audio.presentation.states.SharedAudioUiState
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for handling shared audio content.
 * Follows MVVM pattern with Hilt dependency injection.
 */
@HiltViewModel
class SharedAudioViewModel @Inject constructor(
    private val processSharedAudioUseCase: ProcessSharedAudioUseCase,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {
    
    private val _uiState = MutableStateFlow<SharedAudioUiState>(SharedAudioUiState.Idle)
    val uiState: StateFlow<SharedAudioUiState> = _uiState.asStateFlow()
    
    private val _navigationEvent = MutableStateFlow<NavigationEvent?>(null)
    val navigationEvent: StateFlow<NavigationEvent?> = _navigationEvent.asStateFlow()
    
    /**
     * Processes incoming share intent
     */
    fun processIntent(intent: Intent) {
        viewModelScope.launch {
            val startTime = System.currentTimeMillis()
            
            Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: Processing intent action=${intent.action}")
            
            // Log analytics for intent received
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_INTENT_RECEIVED) {
                param(AnalyticsConstants.Params.INTENT_ACTION_TYPE, intent.action ?: "unknown")
                param(AnalyticsConstants.Params.SOURCE, getIntentSource(intent.action))
                param(AnalyticsConstants.Params.TIMESTAMP, startTime)
            }
            
            try {
                _uiState.value = SharedAudioUiState.Processing
                
                // Parse the intent to SharedAudioIntent
                val sharedAudioIntent = parseIntent(intent)
                
                // Log processing started
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PROCESSING_STARTED) {
                    param(AnalyticsConstants.Params.INTENT_ACTION_TYPE, intent.action ?: "unknown")
                    param(AnalyticsConstants.Params.SOURCE, getIntentSource(intent.action))
                    param(AnalyticsConstants.Params.MULTIPLE_AUDIO_COUNT, sharedAudioIntent.getAudioCount().toLong())
                }
                
                // Process the audio content
                val result = processSharedAudioUseCase(sharedAudioIntent)
                
                val processingTime = System.currentTimeMillis() - startTime
                
                if (result.isSuccess()) {
                    val audioUris = result.getAudioUris()
                    val metadata = result.getMetadata()
                    
                    // Log success
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PROCESSING_COMPLETED) {
                        param(AnalyticsConstants.Params.MULTIPLE_AUDIO_COUNT, audioUris.size.toLong())
                        param(AnalyticsConstants.Params.PROCESSING_TIME_MS, processingTime)
                    }
                    
                    _uiState.value = SharedAudioUiState.Success(audioUris, metadata)
                    
                    // Log navigation trigger
                    Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: Triggering navigation with ${audioUris.size} URIs for action=${intent.action}")
                    
                    // Trigger navigation to music screen
                    _navigationEvent.value = NavigationEvent.NavigateToMusicWithAudio(audioUris)
                    
                } else {
                    val errorMessage = result.getErrorMessage() ?: "Unknown processing error"
                    
                    // Log failure
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PROCESSING_FAILED) {
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, errorMessage)
                        param(AnalyticsConstants.Params.PROCESSING_TIME_MS, processingTime)
                    }
                    
                    _uiState.value = SharedAudioUiState.Error(errorMessage)
                }
                
            } catch (exception: Exception) {
                val processingTime = System.currentTimeMillis() - startTime
                val errorMessage = "Failed to process shared audio: ${exception.message}"
                
                Timber.tag("DEBUG_FLOW").e(exception, "SharedAudioViewModel: Processing failed")
                
                // Log processing error
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SHARED_AUDIO_PROCESSING_FAILED) {
                    param(AnalyticsConstants.Params.ERROR_MESSAGE, errorMessage)
                    param(AnalyticsConstants.Params.PROCESSING_TIME_MS, processingTime)
                }
                
                _uiState.value = SharedAudioUiState.Error(errorMessage)
            }
        }
    }
    
    /**
     * Clears navigation event after it has been handled
     */
    fun clearNavigationEvent() {
        _navigationEvent.value = null
    }
    
    /**
     * Resets the UI state to idle
     */
    fun resetState() {
        _uiState.value = SharedAudioUiState.Idle
    }
    
    /**
     * Parses Android Intent to SharedAudioIntent
     */
    private fun parseIntent(intent: Intent): SharedAudioIntent {
        return try {
            when (intent.action) {
                Intent.ACTION_SEND,
                IntentConstants.Actions.ACTION_SHARE_AUDIO -> {
                    val uri: Uri? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(Intent.EXTRA_STREAM, Uri::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                    }
                    if (uri != null) {
                        SharedAudioIntent.SingleAudio(uri)
                    } else {
                        SharedAudioIntent.NoAudio
                    }
                }
                
                Intent.ACTION_SEND_MULTIPLE,
                IntentConstants.Actions.ACTION_SHARE_MULTIPLE_AUDIO -> {
                    val uris: ArrayList<Uri>? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableArrayListExtra(Intent.EXTRA_STREAM, Uri::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableArrayListExtra<Uri>(Intent.EXTRA_STREAM)
                    }
                    if (!uris.isNullOrEmpty()) {
                        SharedAudioIntent.MultipleAudio(uris)
                    } else {
                        SharedAudioIntent.NoAudio
                    }
                }
                
                IntentConstants.Actions.ACTION_VIEW_AUDIO -> {
                    // For ACTION_VIEW, URI is in intent.data, not EXTRA_STREAM
                    val uri: Uri? = intent.data
                    if (uri != null) {
                        Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: Processing VIEW intent with URI: $uri")
                        SharedAudioIntent.SingleAudio(uri)
                    } else {
                        Timber.tag("DEBUG_FLOW").w("SharedAudioViewModel: VIEW intent has no data URI")
                        SharedAudioIntent.NoAudio
                    }
                }
                
                else -> {
                    Timber.tag("DEBUG_FLOW").w("SharedAudioViewModel: Unknown action ${intent.action}")
                    SharedAudioIntent.NoAudio
                }
            }
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "SharedAudioViewModel: Failed to parse intent")
            SharedAudioIntent.NoAudio
        }
    }
    
    /**
     * Helper function to determine intent source for analytics
     */
    private fun getIntentSource(action: String?): String {
        return when (action) {
            IntentConstants.Actions.ACTION_VIEW_AUDIO -> IntentConstants.Sources.SOURCE_VIEW_INTENT
            IntentConstants.Actions.ACTION_SHARE_AUDIO,
            IntentConstants.Actions.ACTION_SHARE_MULTIPLE_AUDIO -> IntentConstants.Sources.SOURCE_SHARE_INTENT
            else -> IntentConstants.Sources.SOURCE_UNKNOWN
        }
    }
    
    /**
     * Sealed class for navigation events
     */
    sealed class NavigationEvent {
        abstract fun execute(navController: androidx.navigation.NavController)
        
        data class NavigateToMusicWithAudio(val audioUris: List<Uri>) : NavigationEvent() {
            override fun execute(navController: androidx.navigation.NavController) {
                Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: NavigateToMusicWithAudio executing with ${audioUris.size} URIs")
                
                // Navigate to Music screen
                navController.navigate(soly.lyricsgenerator.ui.navigation.NavRoutes.Music.route) {
                    popUpTo(0) // Clear back stack
                }
                
                // Try to get MainActivity and set shared URIs with retry mechanism
                try {
                    val context = navController.context
                    if (context is soly.lyricsgenerator.MainActivity) {
                        val handler = android.os.Handler(android.os.Looper.getMainLooper())
                        var retryCount = 0
                        val maxRetries = 3
                        val retryDelayMs = 500L
                        
                        fun trySetUris() {
                            try {
                                if (context.musicViewModel.enteredScreen.value) {
                                    Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: Setting ${audioUris.size} URIs on MusicViewModel (attempt ${retryCount + 1})")
                                    context.musicViewModel.setSharedAudioUris(audioUris)
                                    Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: Successfully set URIs on MusicViewModel")
                                } else {
                                    throw UninitializedPropertyAccessException("MusicViewModel not ready")
                                }
                            } catch (e: UninitializedPropertyAccessException) {
                                retryCount++
                                if (retryCount < maxRetries) {
                                    Timber.tag("DEBUG_FLOW").d("SharedAudioViewModel: MusicViewModel not ready, retrying in ${retryDelayMs}ms (attempt $retryCount/$maxRetries)")
                                    handler.postDelayed({ trySetUris() }, retryDelayMs)
                                } else {
                                    Timber.tag("DEBUG_FLOW").e("SharedAudioViewModel: Failed to set URIs after $maxRetries attempts")
                                }
                            } catch (e: Exception) {
                                Timber.tag("DEBUG_FLOW").e(e, "SharedAudioViewModel: Error setting URIs on MusicViewModel")
                            }
                        }
                        
                        // Start with a delay to ensure navigation completes
                        handler.postDelayed({ trySetUris() }, 300L)
                    } else {
                        Timber.tag("DEBUG_FLOW").w("SharedAudioViewModel: Context is not MainActivity")
                    }
                } catch (e: Exception) {
                    Timber.tag("DEBUG_FLOW").e(e, "SharedAudioViewModel: Failed to set URIs directly")
                }
            }
        }
    }
}