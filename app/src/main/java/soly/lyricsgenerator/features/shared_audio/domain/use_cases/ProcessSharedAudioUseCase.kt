package soly.lyricsgenerator.features.shared_audio.domain.use_cases

import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioIntent
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult
import soly.lyricsgenerator.features.shared_audio.domain.repository.SharedAudioRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for processing shared audio content.
 * Follows Clean Architecture with business logic in domain layer.
 */
@Singleton
class ProcessSharedAudioUseCase @Inject constructor(
    private val sharedAudioRepository: SharedAudioRepository
) {
    
    /**
     * Processes shared audio intent and validates the content
     * @param intent The shared audio intent to process
     * @return SharedAudioResult with processing outcome
     */
    suspend operator fun invoke(intent: SharedAudioIntent): SharedAudioResult {
        Timber.tag("DEBUG_FLOW").d("ProcessSharedAudioUseCase: Processing ${intent.logIntent()}")
        
        return try {
            // Validate the intent first
            if (!intent.isValid()) {
                Timber.tag("DEBUG_FLOW").w("ProcessSharedAudioUseCase: Invalid intent received")
                return SharedAudioResult.ValidationError("Invalid audio intent received")
            }
            
            // Process URIs from the intent
            val audioUris = intent.process()
            
            if (audioUris.isEmpty()) {
                Timber.tag("DEBUG_FLOW").w("ProcessSharedAudioUseCase: No audio URIs to process")
                return SharedAudioResult.NoContent
            }
            
            // Validate all URIs
            val validationResult = sharedAudioRepository.validateMultipleAudioUris(audioUris)
            
            if (validationResult.isSuccess()) {
                val validUris = validationResult.getAudioUris()
                val metadata = extractMetadataForAll(validUris)
                
                Timber.tag("DEBUG_FLOW").d("ProcessSharedAudioUseCase: Successfully processed ${validUris.size} audio files")
                SharedAudioResult.Success(validUris, metadata)
            } else {
                Timber.tag("DEBUG_FLOW").e("ProcessSharedAudioUseCase: Validation failed - ${validationResult.getErrorMessage()}")
                validationResult
            }
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "ProcessSharedAudioUseCase: Processing failed")
            SharedAudioResult.ProcessingError(
                "Failed to process shared audio: ${exception.message}",
                exception
            )
        }
    }
    
    /**
     * Extracts metadata for all valid URIs
     */
    private suspend fun extractMetadataForAll(uris: List<android.net.Uri>): Map<String, Any> {
        val allMetadata = mutableMapOf<String, Any>()
        
        uris.forEachIndexed { index, uri ->
            try {
                val metadata = sharedAudioRepository.extractAudioMetadata(uri)
                allMetadata["file_$index"] = metadata
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").w(e, "ProcessSharedAudioUseCase: Failed to extract metadata for URI $uri")
                allMetadata["file_$index"] = mapOf("error" to "Metadata extraction failed")
            }
        }
        
        allMetadata["total_files"] = uris.size
        allMetadata["processing_timestamp"] = System.currentTimeMillis()
        
        return allMetadata
    }
}