package soly.lyricsgenerator.features.shared_audio.domain.models

import android.net.Uri

/**
 * Sealed class representing the result of processing shared audio content.
 * Uses polymorphism instead of when expressions following project guidelines.
 */
sealed class SharedAudioResult {
    abstract fun isSuccess(): Boolean
    abstract fun getAudioUris(): List<Uri>
    abstract fun getErrorMessage(): String?
    abstract fun getMetadata(): Map<String, Any>
    abstract fun handleResult(): String
    
    /**
     * Successfully processed audio files
     */
    data class Success(
        private val audioUris: List<Uri>,
        private val metadata: Map<String, Any> = emptyMap()
    ) : SharedAudioResult() {
        override fun isSuccess(): Boolean = true
        
        override fun getAudioUris(): List<Uri> = audioUris
        
        override fun getErrorMessage(): String? = null
        
        override fun getMetadata(): Map<String, Any> = metadata
        
        override fun handleResult(): String = "Successfully processed ${audioUris.size} audio file(s)"
    }
    
    /**
     * Failed to process audio files due to validation errors
     */
    data class ValidationError(
        private val errorMessage: String,
        private val failedUris: List<Uri> = emptyList()
    ) : SharedAudioResult() {
        override fun isSuccess(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getErrorMessage(): String = errorMessage
        
        override fun getMetadata(): Map<String, Any> = mapOf(
            "failed_uris" to failedUris,
            "error_type" to "validation"
        )
        
        override fun handleResult(): String = "Validation failed: $errorMessage"
    }
    
    /**
     * Failed to process audio files due to system or I/O errors
     */
    data class ProcessingError(
        private val errorMessage: String,
        private val exception: Exception? = null
    ) : SharedAudioResult() {
        override fun isSuccess(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getErrorMessage(): String = errorMessage
        
        override fun getMetadata(): Map<String, Any> = mapOf(
            "exception" to (exception?.message ?: "Unknown"),
            "error_type" to "processing"
        )
        
        override fun handleResult(): String = "Processing failed: $errorMessage"
    }
    
    /**
     * No audio content to process
     */
    data object NoContent : SharedAudioResult() {
        override fun isSuccess(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getErrorMessage(): String = "No audio content received"
        
        override fun getMetadata(): Map<String, Any> = mapOf("error_type" to "no_content")
        
        override fun handleResult(): String = "No audio content to process"
    }
    
    companion object {
        /**
         * Factory method to create result from validation results
         */
        fun from(validationResults: List<Uri>): SharedAudioResult {
            return if (validationResults.isNotEmpty()) {
                Success(validationResults)
            } else {
                ValidationError("No valid audio files found")
            }
        }
    }
}