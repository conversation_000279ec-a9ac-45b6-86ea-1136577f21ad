package soly.lyricsgenerator.features.shared_audio.domain.repository

import android.net.Uri
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult

/**
 * Repository interface for shared audio operations.
 * Follows Clean Architecture principles with domain layer interfaces.
 */
interface SharedAudioRepository {
    
    /**
     * Validates if the provided URI is a valid audio file
     * @param uri The URI to validate
     * @return True if valid audio file, false otherwise
     */
    suspend fun validateAudioUri(uri: Uri): Boolean
    
    /**
     * Extracts metadata from an audio file URI
     * @param uri The audio file URI
     * @return Map containing metadata (title, artist, duration, etc.)
     */
    suspend fun extractAudioMetadata(uri: Uri): Map<String, Any>
    
    /**
     * Validates multiple audio URIs
     * @param uris List of URIs to validate
     * @return SharedAudioResult with validation outcome
     */
    suspend fun validateMultipleAudioUris(uris: List<Uri>): SharedAudioResult
    
    /**
     * Gets the MIME type of a content URI
     * @param uri The content URI
     * @return MIME type string or null if cannot be determined
     */
    suspend fun getMimeType(uri: Uri): String?
    
    /**
     * Checks if the app has permission to read from the given URI
     * @param uri The content URI
     * @return True if readable, false otherwise
     */
    suspend fun canReadUri(uri: Uri): Boolean
}