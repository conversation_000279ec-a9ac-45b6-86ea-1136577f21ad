package soly.lyricsgenerator.features.shared_audio.presentation.states

import android.net.Uri
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R

/**
 * Sealed class representing UI states for shared audio feature.
 * Uses polymorphism with render() methods instead of when expressions.
 */
sealed class SharedAudioUiState {
    abstract fun render(): @Composable () -> Unit
    abstract fun isLoading(): Boolean
    abstract fun hasError(): Boolean
    abstract fun getAudioUris(): List<Uri>
    abstract fun getStateDescription(): String
    
    /**
     * Initial idle state - no processing happening
     */
    data object Idle : SharedAudioUiState() {
        override fun render(): @Composable () -> Unit = {
            // Empty state - no UI needed
        }
        
        override fun isLoading(): Boolean = false
        
        override fun hasError(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getStateDescription(): String = "Idle"
    }
    
    /**
     * Processing shared audio content
     */
    data object Processing : SharedAudioUiState() {
        override fun render(): @Composable () -> Unit = {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Text(
                        text = stringResource(R.string.processing_shared_audio),
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(top = 16.dp)
                    )
                }
            }
        }
        
        override fun isLoading(): Boolean = true
        
        override fun hasError(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getStateDescription(): String = "Processing"
    }
    
    /**
     * Successfully processed audio files
     */
    data class Success(
        private val audioUris: List<Uri>,
        private val metadata: Map<String, Any> = emptyMap()
    ) : SharedAudioUiState() {
        override fun render(): @Composable () -> Unit = {
            // Success state typically triggers navigation
            // No specific UI needed here as navigation handles display
        }
        
        override fun isLoading(): Boolean = false
        
        override fun hasError(): Boolean = false
        
        override fun getAudioUris(): List<Uri> = audioUris
        
        override fun getStateDescription(): String = "Success: ${audioUris.size} files processed"
        
        fun getMetadata(): Map<String, Any> = metadata
    }
    
    /**
     * Error state with message display
     */
    data class Error(
        private val errorMessage: String,
        private val retryable: Boolean = true
    ) : SharedAudioUiState() {
        override fun render(): @Composable () -> Unit = {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.error_loading_shared_audio),
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.error
                    )
                    Text(
                        text = errorMessage,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }
        
        override fun isLoading(): Boolean = false
        
        override fun hasError(): Boolean = true
        
        override fun getAudioUris(): List<Uri> = emptyList()
        
        override fun getStateDescription(): String = "Error: $errorMessage"
        
        fun isRetryable(): Boolean = retryable
        
        fun getErrorMessage(): String = errorMessage
    }
}