package soly.lyricsgenerator.features.shared_audio.data.validators

import android.content.ContentResolver
import android.net.Uri
import soly.lyricsgenerator.domain.constants.FileConstants
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Validator for audio files shared to the app.
 * Handles MIME type validation and content verification.
 */
@Singleton
class AudioValidator @Inject constructor() {
    
    /**
     * Validates if the URI represents a valid audio file
     * @param uri The URI to validate
     * @param contentResolver ContentResolver to use for validation
     * @return True if valid audio file, false otherwise
     */
    suspend fun validate(uri: Uri, contentResolver: ContentResolver): Boolean {
        return try {
            Timber.tag("DEBUG_FLOW").d("AudioValidator: Validating URI - $uri")
            
            // Check if URI is not empty
            if (uri == Uri.EMPTY) {
                Timber.tag("DEBUG_FLOW").w("AudioValidator: Empty URI provided")
                return false
            }
            
            // Get MIME type
            val mimeType = getMimeType(uri, contentResolver)
            if (mimeType == null) {
                Timber.tag("DEBUG_FLOW").w("AudioValidator: Could not determine MIME type for URI $uri")
                return false
            }
            
            // Validate MIME type
            val isValidMimeType = isAudioMimeType(mimeType)
            if (!isValidMimeType) {
                Timber.tag("DEBUG_FLOW").w("AudioValidator: Invalid MIME type '$mimeType' for URI $uri")
                return false
            }
            
            // Check if we can read the content
            val canRead = canReadContent(uri, contentResolver)
            if (!canRead) {
                Timber.tag("DEBUG_FLOW").w("AudioValidator: Cannot read content from URI $uri")
                return false
            }
            
            Timber.tag("DEBUG_FLOW").d("AudioValidator: URI $uri validated successfully with MIME type $mimeType")
            true
            
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "AudioValidator: Validation failed for URI $uri")
            false
        }
    }
    
    /**
     * Gets the MIME type of a content URI
     */
    fun getMimeType(uri: Uri, contentResolver: ContentResolver): String? {
        return try {
            contentResolver.getType(uri)
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "AudioValidator: Failed to get MIME type for URI $uri")
            null
        }
    }
    
    /**
     * Checks if the MIME type represents an audio file
     */
    private fun isAudioMimeType(mimeType: String): Boolean {
        return FileConstants.AudioMimeTypes.SUPPORTED_AUDIO_MIME_TYPES.any { supportedType ->
            mimeType.equals(supportedType, ignoreCase = true) || 
            (supportedType.endsWith("/*") && mimeType.startsWith(supportedType.removeSuffix("/*"), ignoreCase = true))
        }
    }
    
    /**
     * Checks if we can read content from the URI
     */
    private fun canReadContent(uri: Uri, contentResolver: ContentResolver): Boolean {
        return try {
            contentResolver.openInputStream(uri)?.use { inputStream ->
                // Try to read a small amount to verify accessibility
                inputStream.read() != -1
            } ?: false
        } catch (exception: Exception) {
            Timber.tag("DEBUG_FLOW").e(exception, "AudioValidator: Cannot read content from URI $uri")
            false
        }
    }
    
    /**
     * Validates multiple URIs and returns a list of valid ones
     */
    suspend fun validateMultiple(uris: List<Uri>, contentResolver: ContentResolver): List<Uri> {
        return uris.filter { validate(it, contentResolver) }
    }
}