package soly.lyricsgenerator.features.shared_audio.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.features.shared_audio.data.repositories.SharedAudioRepositoryImpl
import soly.lyricsgenerator.features.shared_audio.domain.repository.SharedAudioRepository
import javax.inject.Singleton

/**
 * Hilt module for shared audio feature dependencies.
 * Follows Clean Architecture with interface bindings.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class SharedAudioModule {
    
    /**
     * Binds SharedAudioRepository interface to its implementation
     */
    @Binds
    @Singleton
    abstract fun bindSharedAudioRepository(
        impl: SharedAudioRepositoryImpl
    ): SharedAudioRepository
}