package soly.lyricsgenerator.features.shared_audio.domain.models

import android.net.Uri

/**
 * Sealed class representing different types of shared audio intents.
 * Uses polymorphism instead of when expressions following project guidelines.
 */
sealed class SharedAudioIntent {
    abstract fun process(): List<Uri>
    abstract fun getSourceDescription(): String
    abstract fun getAudioCount(): Int
    abstract fun isValid(): Boolean
    abstract fun logIntent(): String
    
    /**
     * Single audio file shared to the app
     */
    data class SingleAudio(val uri: Uri) : SharedAudioIntent() {
        override fun process(): List<Uri> = listOf(uri)
        
        override fun getSourceDescription(): String = "Single audio file"
        
        override fun getAudioCount(): Int = 1
        
        override fun isValid(): Boolean = uri != Uri.EMPTY
        
        override fun logIntent(): String = "SingleAudio(uri=$uri)"
    }
    
    /**
     * Multiple audio files shared to the app
     */
    data class MultipleAudio(val uris: List<Uri>) : SharedAudioIntent() {
        override fun process(): List<Uri> = uris
        
        override fun getSourceDescription(): String = "Multiple audio files (${uris.size})"
        
        override fun getAudioCount(): Int = uris.size
        
        override fun isValid(): Boolean = uris.isNotEmpty() && uris.all { it != Uri.EMPTY }
        
        override fun logIntent(): String = "MultipleAudio(count=${uris.size}, uris=$uris)"
    }
    
    /**
     * No audio content or invalid intent
     */
    data object NoAudio : SharedAudioIntent() {
        override fun process(): List<Uri> = emptyList()
        
        override fun getSourceDescription(): String = "No audio content"
        
        override fun getAudioCount(): Int = 0
        
        override fun isValid(): Boolean = false
        
        override fun logIntent(): String = "NoAudio"
    }
}