package soly.lyricsgenerator.features.update.data.repository

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import kotlinx.coroutines.tasks.await
import soly.lyricsgenerator.features.update.data.constants.RemoteConfigConstants
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import javax.inject.Inject

class UpdateRepositoryImpl @Inject constructor(
    private val remoteConfig: FirebaseRemoteConfig
) : UpdateRepository {

    override suspend fun fetchAndActivate() {
        remoteConfig.fetchAndActivate().await()
    }

    override fun isForceUpdateRequired(): Boolean {
        return remoteConfig.getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)
    }
    
    override fun getForceUpdateVersion(): String {
        return remoteConfig.getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)
    }
    
    override fun getStoreAppVersion(): String {
        return remoteConfig.getString(RemoteConfigConstants.STORE_APP_VERSION)
    }
}