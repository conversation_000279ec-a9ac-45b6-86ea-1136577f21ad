package soly.lyricsgenerator.features.update.domain.usecase

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import soly.lyricsgenerator.utils.VersionUtils
import javax.inject.Inject

class CheckForUpdateUseCase @Inject constructor(
    private val updateRepository: UpdateRepository,
    @ApplicationContext private val context: Context
) {
    suspend operator fun invoke(): UpdateState {
        try {
            updateRepository.fetchAndActivate()

            val currentAppVersion = VersionUtils.getCurrentAppVersion(context)
            val forceUpdateRequired = updateRepository.isForceUpdateRequired()
            val forceUpdateVersion = updateRepository.getForceUpdateVersion()
            val storeAppVersion = updateRepository.getStoreAppVersion()

            return when {
                forceUpdateRequired && VersionUtils.isUpdateNeeded(currentAppVersion, forceUpdateVersion) -> {
                    UpdateState.ForceUpdate(forceUpdateVersion)
                }
                VersionUtils.isUpdateNeeded(currentAppVersion, storeAppVersion) -> {
                    UpdateState.SoftUpdate(storeAppVersion)
                }
                else -> {
                    UpdateState.NoUpdate
                }
            }
        } catch (e: Exception) {
            return UpdateState.NoUpdate
        }
    }
}