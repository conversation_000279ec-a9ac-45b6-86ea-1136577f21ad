package soly.lyricsgenerator.features.update.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.domain.usecase.CheckForUpdateUseCase
import javax.inject.Inject

@HiltViewModel
class UpdateViewModel @Inject constructor(
    private val checkForUpdateUseCase: CheckForUpdateUseCase
) : ViewModel() {

    private val _updateState = MutableStateFlow<UpdateState>(UpdateState.Loading)
    val updateState: StateFlow<UpdateState> = _updateState

    init {
        checkForUpdates()
    }

    private fun checkForUpdates() {
        viewModelScope.launch {
            try {
                val result = checkForUpdateUseCase()
                _updateState.value = result
            } catch (e: Exception) {
                // In case of error (e.g., no network), proceed to the app
                _updateState.value = UpdateState.NoUpdate
            }
        }
    }

    fun dismissSoftUpdate() {
        _updateState.value = UpdateState.NoUpdate
    }
}