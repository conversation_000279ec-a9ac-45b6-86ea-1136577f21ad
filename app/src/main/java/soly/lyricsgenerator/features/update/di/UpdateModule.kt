package soly.lyricsgenerator.features.update.di

import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.R
import soly.lyricsgenerator.features.update.data.repository.UpdateRepositoryImpl
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object UpdateModule {

    @Provides
    @Singleton
    fun provideRemoteConfig(): FirebaseRemoteConfig {
        val remoteConfig = Firebase.remoteConfig
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = if (soly.lyricsgenerator.BuildConfig.DEBUG) 0 else 3600 // Use a longer interval for release builds
        }
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        return remoteConfig
    }

    @Provides
    @Singleton
    fun provideUpdateRepository(remoteConfig: FirebaseRemoteConfig): UpdateRepository {
        return UpdateRepositoryImpl(remoteConfig)
    }
}