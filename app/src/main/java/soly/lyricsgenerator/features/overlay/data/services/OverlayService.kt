package soly.lyricsgenerator.features.overlay.data.services

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.Typeface
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.FilledTonalIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import soly.lyricsgenerator.R
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.usecase.audiotag.GetEmbeddedLyricsUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.GetFileContentForSongUseCase
import soly.lyricsgenerator.features.overlay.domain.models.OverlaySettings
import soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionUseCase
import soly.lyricsgenerator.ui.screens.components.LyricsDisplayConfig
import soly.lyricsgenerator.ui.screens.components.LyricsDisplayMode
import soly.lyricsgenerator.ui.screens.components.SharedLyricsDisplay
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import javax.inject.Inject

/**
 * Service for managing floating lyrics overlay window
 * Following existing service patterns from MusicPlayerService
 */
@AndroidEntryPoint
class OverlayService : Service() {

    private var overlayView: View? = null
    private var windowManager: WindowManager? = null
    private var isOverlayShown = false
    private var lifecycleOwner: ServiceLifecycleOwner? = null
    @Inject
    lateinit var overlayPermissionUseCase: OverlayPermissionUseCase
    
    @Inject
    lateinit var getFileContentForSongUseCase: GetFileContentForSongUseCase
    
    @Inject
    lateinit var getEmbeddedLyricsUseCase: GetEmbeddedLyricsUseCase
    
    // State flows for lyrics display
    internal val _currentSong = MutableStateFlow<Song?>(null)
    val currentSong: StateFlow<Song?> = _currentSong.asStateFlow()
    
    internal val _currentLyrics = MutableStateFlow<Map<Int, String>>(emptyMap())
    val currentLyrics: StateFlow<Map<Int, String>> = _currentLyrics.asStateFlow()
    
    internal val _currentPosition = MutableStateFlow(0L)
    val currentPosition: StateFlow<Long> = _currentPosition.asStateFlow()

    // Flag to prevent height recalculation during same song playback
    private var heightCalculatedForSongId: Long? = null

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    

    // Broadcast receiver for music player events
    private val musicPlayerReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            intent?.let { handleMusicPlayerBroadcast(it) }
        }
    }

    companion object {
        const val ACTION_SHOW_OVERLAY = "soly.lyricsgenerator.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "soly.lyricsgenerator.HIDE_OVERLAY"
        const val ACTION_UPDATE_OVERLAY = "soly.lyricsgenerator.UPDATE_OVERLAY"

        private val OVERLAY_WINDOW_FLAGS = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH

        private const val DEFAULT_OVERLAY_Y_POSITION = 200

        private var isServiceRunning = false

        fun showOverlay(context: Context) {
            if (isServiceRunning) {
                return
            }

            val intent = Intent(context, OverlayService::class.java).apply {
                action = ACTION_SHOW_OVERLAY
            }
            context.startService(intent)
        }

        fun hideOverlay(context: Context) {
            val intent = Intent(context, OverlayService::class.java).apply {
                action = ACTION_HIDE_OVERLAY
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        isServiceRunning = true

        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager

        // Register broadcast receiver for music player events
        registerMusicPlayerReceiver()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.action?.let { action ->
            // Log analytics event for overlay service action
            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.OVERLAY_SERVICE_ACTION) {
                param(AnalyticsConstants.Params.ACTION, action)
            }
            
            handleServiceAction(action)
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        hideOverlayWindow()
        unregisterMusicPlayerReceiver()
        serviceScope.cancel()
        isServiceRunning = false
        super.onDestroy()
    }

    private fun handleServiceAction(action: String) {
        val actionHandler = OverlayServiceAction.fromString(action)
        actionHandler.execute(this)
    }

    internal fun showOverlayWindow() {
        if (isOverlayShown || overlayView != null) {
            return
        }
        
        // Reset height calculation flag for new overlay session to allow recalculation
        // This ensures the overlay height is properly calculated for the first song played
        // after the overlay is shown, preventing stale height values from previous sessions
        heightCalculatedForSongId = null

        // Check overlay permission directly (ViewModel not created yet)
        val permissionResult = overlayPermissionUseCase.checkOverlayPermission()
        if (permissionResult !is soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionResult.Granted) {
            Toast.makeText(this, getString(R.string.overlay_permission_denied), Toast.LENGTH_SHORT).show()
            return
        }
        
        // Calculate song-specific height asynchronously - wait briefly for music service state
        serviceScope.launch {
            try {
                // Request current state first to ensure we have latest song info
                requestCurrentMusicState()
                
                // Wait briefly for music service to respond with current song
                var currentSong = _currentSong.value
                var attempts = 0
                val maxAttempts = 10 // 500ms max wait
                
                while (currentSong == null && attempts < maxAttempts) {
                    kotlinx.coroutines.delay(50)
                    currentSong = _currentSong.value
                    attempts++
                }
                
                // Calculate optimal height based on available song data
                val optimalHeight = if (currentSong != null) {
                    val height = calculateSongSpecificOverlayHeight(currentSong)
                    // Mark height as calculated for this song to prevent recalculation during playback
                    heightCalculatedForSongId = currentSong.id
                    height
                } else {
                    calculateNoLyricsOverlayHeight()
                }
                
                // Create overlay with the calculated height
                createOverlayWithHeight(optimalHeight)
                
            } catch (e: Exception) {
                // Fallback to no-lyrics height if calculation fails
                val fallbackHeight = calculateNoLyricsOverlayHeight()
                createOverlayWithHeight(fallbackHeight)
            }
        }
    }
    
    /**
     * Create the overlay window with the specified height
     */
    private fun createOverlayWithHeight(height: Int) {
        try {
            val layoutParams = createOverlayLayoutParams(height)
            
            overlayView = createOverlayView()
            
            windowManager?.addView(overlayView, layoutParams)
            isOverlayShown = true
            
        } catch (e: Exception) {
            Toast.makeText(this, "Error showing overlay: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    internal fun hideOverlayWindow() {
        if (!isOverlayShown || overlayView == null) {
            return
        }

        try {
            windowManager?.removeView(overlayView)
            lifecycleOwner?.onDestroy()
            overlayView = null
            lifecycleOwner = null
            isOverlayShown = false
            
        } catch (e: Exception) {
        }
    }

    /**
     * Pre-load lyrics and calculate song-specific optimal height
     * This analyzes the actual lyrics content to determine precise height needed
     */
    private suspend fun calculateSongSpecificOverlayHeight(song: Song?): Int {
        if (song == null) {
            return calculateNoLyricsOverlayHeight()
        }
        
        val lyricsResult = loadLyricsForHeightCalculation(song)
            ?: return calculateMaxOverlayHeight() // Fallback to static height
        
        val lyrics = lyricsResult.extractLrcLines()

        if (lyrics.isEmpty()) {
            return calculateNoLyricsOverlayHeight()
        }
        
        // Analyze lyrics content to determine optimal height
        return calculateOptimalHeightForLyrics(lyrics)
    }
    
    /**
     * Load lyrics for a specific song using multiple fallback strategies for height calculation
     * @param song The song to load lyrics for
     * @return FileContentState if successful, null if failed or no lyrics found
     */
    private suspend fun loadLyricsForHeightCalculation(song: Song): soly.lyricsgenerator.domain.model.FileContentState? {
        return try {
            // First try to get content from linked files
            var result = getFileContentForSongUseCase(song.id)
            
            // If no linked files found, try embedded lyrics as fallback
            if (result.isEmpty()) {
                result = getEmbeddedLyricsUseCase(song)
            }
            
            result
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Calculate optimal height based on actual lyrics content using real rendering simulation
     * Analyzes all possible visible line combinations during playback considering text wrapping
     */
    private fun calculateOptimalHeightForLyrics(lyrics: Map<Int, String>): Int {
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.density
        val scaledDensity = displayMetrics.scaledDensity
        
        val settings = loadOverlaySettings()
        
        // Calculate base components height (everything except lyrics section)
        val baseHeight = calculateBaseOverlayHeight()
        
        // Get overlay width for accurate text measurement
        val screenWidth = windowManager?.defaultDisplay?.width ?: 1080
        val overlayWidth = (screenWidth * 0.85f).toInt()
        
        // Create LyricsDisplayConfig matching the one used in UI
        val config = LyricsDisplayConfig(
            mode = LyricsDisplayMode.OVERLAY,
            maxVisibleLines = 3,
            fontSize = settings.fontSize.sp,
            lineHeight = (settings.fontSize + 8).sp,
            activeColor = androidx.compose.ui.graphics.Color.White,
            inactiveColor = androidx.compose.ui.graphics.Color.White,
            activeAlpha = 1f,
            inactiveAlpha = 0.75f,
            horizontalPadding = 8.dp,
            verticalSpacing = 8.dp,
            enableAutoScroll = false,
            showPlaceholder = false
        )
        
        // Test all possible active line positions to find maximum height
        val lyricsList = lyrics.toList().sortedBy { it.first }
        var maxLyricsHeight = 0
        
        for (activeIndex in lyricsList.indices) {
            val lyricsHeight = soly.lyricsgenerator.ui.screens.components.calculateOverlayLyricsHeight(
                allLyrics = lyrics,
                activeLineIndex = activeIndex,
                config = config,
                overlayWidth = overlayWidth,
                density = density,
                scaledDensity = scaledDensity
            )
            
            if (lyricsHeight > maxLyricsHeight) {
                maxLyricsHeight = lyricsHeight
            }
        }
        
        val totalHeight = baseHeight + maxLyricsHeight

        return totalHeight
    }
    
    /**
     * Calculate height for specific lyrics lines using proper text measurement
     */
    private fun calculateLyricsSectionHeight(lines: List<String>, fontSize: Float, scaledDensity: Float): Int {
        fun spToPx(sp: Float): Int = (sp * scaledDensity).toInt()
        fun dpToPx(dp: Float): Int = (dp * resources.displayMetrics.density).toInt()
        
        val lineHeight = spToPx(fontSize + 8f) // fontSize + 8sp for line height
        val verticalSpacing = dpToPx(8f) // 8dp between lines
        
        // Get overlay width for text measurement (85% of screen width - padding)
        val screenWidth = windowManager?.defaultDisplay?.width ?: 1080
        val overlayWidth = (screenWidth * 0.85f).toInt()
        val textWidth = overlayWidth - dpToPx(32f) // Subtract horizontal padding (16dp each side)
        
        // Create Paint object for text measurement
        val textPaint = Paint().apply {
            textSize = spToPx(fontSize).toFloat()
            typeface = Typeface.DEFAULT
            isAntiAlias = true
        }
        
        // Calculate height considering actual text wrapping
        var totalHeight = 0
        lines.forEachIndexed { index, line ->
            // Measure actual text width and determine line wrapping
            val textWidthActual = textPaint.measureText(line)
            val wrappedLines = kotlin.math.ceil(textWidthActual / textWidth).toInt().coerceAtLeast(1)
            
            totalHeight += lineHeight * wrappedLines
            if (index < lines.size - 1) {
                totalHeight += verticalSpacing
            }
            
        }

        return totalHeight
    }
    
    /**
     * Calculate base height for overlay (everything except lyrics section)
     * This accounts for ALL padding layers in the actual UI structure
     */
    private fun calculateBaseOverlayHeight(): Int {
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.density
        
        fun dpToPx(dp: Float): Int = (dp * density).toInt()
        
        // ACTUAL UI structure padding analysis:
        // 1. Surface padding: 12dp (line 691)
        // 2. Column padding: 16dp (line 744) 
        // 3. Column spacedBy: 8dp (line 745)
        // 4. Header row height: ~32dp (close button)
        // 5. Divider + padding: ~8dp 
        // 6. Extra spacing for drag indicator area: ~8dp
        // 7. Surface elevation/shadow space: ~4dp
        // 8. Safety margin for rounding/measurement errors: +10dp
        
        val surfacePadding = dpToPx(12f)        // Surface modifier padding  
        val columnPadding = dpToPx(32f)         // Column padding: 16dp top + 16dp bottom
        val columnSpacing = dpToPx(8f)          // Column spacedBy arrangement
        val headerHeight = dpToPx(32f)          // Header row (close button height)
        val dividerHeight = dpToPx(8f)          // Divider area
        val dragIndicatorHeight = dpToPx(8f)    // Drag indicator space
        val elevationSpace = dpToPx(4f)         // Surface elevation/shadow
        val safetyMargin = dpToPx(10f)          // Safety margin for measurement accuracy
        
        val totalBase = surfacePadding + columnPadding + columnSpacing + headerHeight + dividerHeight + dragIndicatorHeight + elevationSpace + safetyMargin
        
        return totalBase
    }
    
    /**
     * Calculate height for "no lyrics" case - deliberately smaller to show difference
     */
    private fun calculateNoLyricsOverlayHeight(): Int {
        val baseHeight = calculateBaseOverlayHeight()
        val noLyricsMessageHeight = (resources.displayMetrics.scaledDensity * 32f).toInt() // Single line height for "no lyrics" text
        
        val totalHeight = baseHeight + noLyricsMessageHeight

        return totalHeight
    }

    /**
     * Calculate the maximum height needed for the overlay to prevent jumping
     * This pre-calculates the height for the worst-case scenario (maximum content)
     * @deprecated Use calculateSongSpecificOverlayHeight() for better accuracy
     */
    private fun calculateMaxOverlayHeight(): Int {
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.density
        val scaledDensity = displayMetrics.scaledDensity
        
        // Convert dp/sp to pixels for calculation
        fun dpToPx(dp: Float): Int = (dp * density).toInt()
        fun spToPx(sp: Float): Int = (sp * scaledDensity).toInt()
        
        val settings = loadOverlaySettings()

        // Overlay components height calculation:
        // 1. Main container padding (16dp top + 16dp bottom)
        val containerPadding = dpToPx(32f)

        // 2. Header section (Row with song info + close button)
        //    - Close button: 32dp (dominant height in Row)
        val headerHeight = dpToPx(32f)

        // 3. Vertical spacing after header
        val headerSpacing = dpToPx(8f)

        // 4. Divider section (1dp + padding)
        val dividerHeight = dpToPx(8f)

        // 5. Lyrics section (maximum case: 3 lines)
        //    - Font size: configurable (default 22sp)
        //    - Line height: fontSize + 8sp
        //    - 3 lines total
        //    - Vertical spacing between lines: 8dp × 2 = 16dp
        val lyricsLineHeight = spToPx(settings.fontSize + 8f)
        val maxLyricsLines = 3
        val lyricsVerticalSpacing = dpToPx(8f) * (maxLyricsLines - 1) // spacing between lines
        val maxLyricsHeight = (lyricsLineHeight * maxLyricsLines) + lyricsVerticalSpacing
        
        // 6. Drag indicator (when active): 4dp height + 4dp top padding
        val dragIndicatorHeight = dpToPx(8f)

        // 7. Surface elevation/shadow space
        val elevationSpace = dpToPx(4f)

        // Calculate total maximum height
        val totalHeight = containerPadding + 
                         headerHeight + 
                         headerSpacing + 
                         dividerHeight + 
                         maxLyricsHeight + 
                         dragIndicatorHeight + 
                         elevationSpace
        
        return totalHeight
    }

    private fun createOverlayLayoutParams(height: Int): WindowManager.LayoutParams {
        val settings = loadOverlaySettings()
        
        // Use fixed width (85% of screen width)
        val screenWidth = windowManager?.defaultDisplay?.width ?: 0
        val width = (screenWidth * 0.85f).toInt().takeIf { it > 0 } 
            ?: WindowManager.LayoutParams.MATCH_PARENT
        
        val layoutParams = WindowManager.LayoutParams(
            width,
            height,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            OVERLAY_WINDOW_FLAGS,
            PixelFormat.TRANSLUCENT
        )

        // Use absolute positioning for drag functionality
        layoutParams.gravity = Gravity.TOP or Gravity.START
        
        // Set initial position at bottom of screen
        val screenHeight = windowManager?.defaultDisplay?.height ?: 1000
        layoutParams.x = 0
        layoutParams.y = screenHeight - 400 // Bottom with margin
        
        // Apply transparency setting
        layoutParams.alpha = settings.transparency
        
        return layoutParams
    }

    private fun createOverlayView(): View {
        val composeView = ComposeView(this)
        lifecycleOwner = ServiceLifecycleOwner()
        lifecycleOwner?.onCreate()
        
        composeView.setViewTreeLifecycleOwner(lifecycleOwner)
        composeView.setViewTreeViewModelStoreOwner(lifecycleOwner)
        composeView.setViewTreeSavedStateRegistryOwner(lifecycleOwner)

        lifecycleOwner?.onResume()
        
        composeView.setContent {
            val song by currentSong.collectAsState()
            val lyrics by currentLyrics.collectAsState()
            val position by currentPosition.collectAsState()
            
            val overlaySettings = loadOverlaySettings()

            // State for drag feedback
            var isDragging by remember { mutableStateOf(false) }
            
            // Use the standard app gradient for consistency with other screens
            
            // Entrance animation
            AnimatedVisibility(
                visible = true,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300)
                ) + fadeIn(animationSpec = tween(300)),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(200)
                ) + fadeOut(animationSpec = tween(200))
            ) {
                // Modern overlay design with solid background
                Box {
                    // Background blur effect simulation
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp)
                            .pointerInput(Unit) {
                                detectDragGestures(
                                    onDragStart = { 
                                        isDragging = true
                                    },
                                    onDragEnd = { 
                                        isDragging = false
                                    }
                                ) { _, dragAmount ->
                                    // Update overlay position during drag
                                    overlayView?.let { overlay ->
                                        val layoutParams = overlay.layoutParams as? WindowManager.LayoutParams
                                        layoutParams?.let { params ->
                                            val oldX = params.x
                                            val oldY = params.y
                                            params.x += dragAmount.x.toInt()
                                            params.y += dragAmount.y.toInt()
                                            windowManager?.updateViewLayout(overlay, params)
                                        }
                                    }
                                }
                            },
                        shape = RoundedCornerShape(20.dp),
                        tonalElevation = 1.dp,
                        shadowElevation = 1.dp,
                        color = Color.Transparent
                    ) {
                        // Layered background for solid effect
                        Box {
                            // Solid gradient background (using standard app gradient)
                            Box(
                                modifier = Modifier
                                    .matchParentSize()
                                    .clip(RoundedCornerShape(20.dp))
                                    .background(brush = AppGradientBrush)
                            )
                            
                            // Content with enhanced spacing and typography
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // Modern header with enhanced typography
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column(
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        Text(
                                            text = song?.title ?: stringResource(R.string.no_song_currently_playing),
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontWeight = FontWeight.Normal
                                            ),
                                            color = Color.White.copy(alpha = 0.7f),
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                        song?.artist?.let { artist ->
                                            Text(
                                                text = artist,
                                                style = MaterialTheme.typography.labelSmall,
                                                color = Color.White.copy(alpha = 0.6f),
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis
                                            )
                                        }
                                    }
                                    
                                    // Enhanced close button with modern Material 3 styling
                                    FilledTonalIconButton(
                                        onClick = {
                                            hideOverlay(this@OverlayService)
                                        },
                                        colors = IconButtonDefaults.filledTonalIconButtonColors(
                                            containerColor = Color.White.copy(alpha = 0.2f),
                                            contentColor = Color.White
                                        ),
                                        modifier = Modifier.size(32.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = stringResource(R.string.overlay_close_button_desc),
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }

                                // Subtle divider (always shown for consistent layout)
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = 8.dp)
                                        .background(
                                            Color.White.copy(alpha = 0.2f),
                                            RoundedCornerShape(1.dp)
                                        )
                                        .size(width = 0.dp, height = 1.dp)
                                )
                                
                                // Lyrics section - FIXED HEIGHT to prevent Compose intrinsic resizing
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight(), // FORCE full height usage - no shrinking!
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (lyrics.isNotEmpty()) {
                                        SharedLyricsDisplay(
                                            lrcLines = lyrics,
                                            currentTimeMs = position.toInt(),
                                            config = LyricsDisplayConfig(
                                                mode = LyricsDisplayMode.OVERLAY,
                                                maxVisibleLines = 3,
                                                fontSize = overlaySettings.fontSize.sp,
                                                lineHeight = (overlaySettings.fontSize + 8).sp,
                                                activeColor = Color.White,
                                                inactiveColor = Color.White,
                                                activeAlpha = 1f,
                                                inactiveAlpha = 0.75f,
                                                horizontalPadding = 8.dp,
                                                verticalSpacing = 8.dp,
                                                enableAutoScroll = false,
                                                showPlaceholder = false
                                            ),
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    } else {
                                        // No lyrics message (centered in fixed space)
                                        Text(
                                            text = stringResource(R.string.no_lyrics_available),
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontWeight = FontWeight.Medium
                                            ),
                                            color = Color.White.copy(alpha = 0.8f),
                                            textAlign = TextAlign.Center
                                        )
                                    }
                                }
                                
                                // Fixed-height drag indicator area (always reserves space)
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(8.dp), // Fixed height to always reserve space
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (isDragging) {
                                        Box(
                                            modifier = Modifier
                                                .size(width = 32.dp, height = 4.dp)
                                                .background(
                                                    Color.White.copy(alpha = 0.6f),
                                                    RoundedCornerShape(2.dp)
                                                )
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return composeView
    }

    
    /**
     * Request current music state from MusicPlayerService
     */
    private fun requestCurrentMusicState() {
        try {
            val intent = Intent(this, MusicPlayerService::class.java).apply {
                action = MusicPlayerService.ACTION_GET_CURRENT_STATE
            }
            startService(intent)
        } catch (e: Exception) {
        }
    }

    private fun registerMusicPlayerReceiver() {
        val intentFilter = IntentFilter().apply {
            addAction(MusicPlayerService.ACTION_SONG_CHANGED)
            addAction(MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED)
            addAction(MusicPlayerService.ACTION_POSITION_UPDATED)
            addAction(MusicPlayerService.ACTION_STATE_UPDATED)
        }
        ContextCompat.registerReceiver(this, musicPlayerReceiver, intentFilter, ContextCompat.RECEIVER_NOT_EXPORTED)
    }

    private fun unregisterMusicPlayerReceiver() {
        unregisterReceiver(musicPlayerReceiver)
    }

    private fun handleMusicPlayerBroadcast(intent: Intent) {
        val action = intent.action ?: return
        val handler = MusicPlayerActionMatcher.fromString(action).getHandler()
        handler.handle(intent, this)
    }
    
    /**
     * Load lyrics for the given song with embedded lyrics fallback
     */
    internal fun loadLyricsForSong(song: Song) {
        serviceScope.launch {
            try {
                // First try to get content from linked files
                var lyricsResult = getFileContentForSongUseCase(song.id)
                
                // If no linked files found, try embedded lyrics as fallback
                if (lyricsResult.isEmpty()) {
                    lyricsResult = getEmbeddedLyricsUseCase(song)
                }
                
                val lyrics = lyricsResult.extractLrcLines()
                _currentLyrics.value = lyrics
            } catch (e: Exception) {
                _currentLyrics.value = emptyMap()
            }
        }
    }
    
    /**
     * Update overlay height dynamically when song changes
     * This prevents height jumping by recalculating optimal height for the new song
     * CALCULATES HEIGHT ONLY ONCE PER SONG - NEVER DURING PLAYBACK OF SAME SONG
     */
    internal fun updateOverlayHeightForNewSong(song: Song) {
        if (!isOverlayShown || overlayView == null) {
            return
        }
        
        // CRITICAL: Check if height already calculated for this song - PREVENT RECALCULATION DURING PLAYBACK
        if (heightCalculatedForSongId == song.id) {
            return
        }
        
        serviceScope.launch {
            try {
                // Calculate optimal height for the new song
                val newOptimalHeight = calculateSongSpecificOverlayHeight(song)
                
                // Update the overlay layout parameters with the new height
                updateOverlayHeight(newOptimalHeight)
                
                // Mark height as calculated for this song - PREVENTS FURTHER RECALCULATIONS
                heightCalculatedForSongId = song.id

            } catch (e: Exception) {
            }
        }
    }
    
    /**
     * Update the overlay window height dynamically
     */
    private fun updateOverlayHeight(newHeight: Int) {
        overlayView?.let { overlay ->
            val layoutParams = overlay.layoutParams as? WindowManager.LayoutParams
            layoutParams?.let { params ->
                val oldHeight = params.height
                
                if (oldHeight != newHeight) {
                    params.height = newHeight
                    
                    // Update the window layout
                    windowManager?.updateViewLayout(overlay, params)
                    
                }
            }
        }
    }
    
    private fun loadOverlaySettings(): OverlaySettings {
        return OverlaySettings(
            transparency = 0.9f,
            fontSize = 22f,
            fontColor = 0xFFFFFFFF,
            backgroundColor = 0xFF000000
        )
    }
    
}

sealed class OverlayServiceAction {
    abstract fun execute(service: OverlayService)

    data object ShowOverlay : OverlayServiceAction() {
        override fun execute(service: OverlayService) = service.showOverlayWindow()
    }

    data object HideOverlay : OverlayServiceAction() {
        override fun execute(service: OverlayService) {
            service.hideOverlayWindow()
            service.stopSelf()
        }
    }

    data object UpdateOverlay : OverlayServiceAction() {
        override fun execute(service: OverlayService) {
        }
    }

    data object Unknown : OverlayServiceAction() {
        override fun execute(service: OverlayService) {
        }
    }

    companion object {
        fun fromString(action: String): OverlayServiceAction {
            return when (action) {
                OverlayService.ACTION_SHOW_OVERLAY -> ShowOverlay
                OverlayService.ACTION_HIDE_OVERLAY -> HideOverlay
                OverlayService.ACTION_UPDATE_OVERLAY -> UpdateOverlay
                else -> Unknown
            }
        }
    }
}

sealed class MusicPlayerActionHandler {
    abstract fun handle(intent: Intent, service: OverlayService)

    data object SongChangedHandler : MusicPlayerActionHandler() {
        override fun handle(intent: Intent, service: OverlayService) {
            val song = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG, Song::class.java)
            } else {
                @Suppress("DEPRECATION")
                intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG) as? Song
            }
            service._currentSong.value = song
            song?.let { 
                service.loadLyricsForSong(it)
                // Update overlay height for the new song to prevent height jumping
                service.updateOverlayHeightForNewSong(it)
            }
        }
    }

    data object PlaybackStateChangedHandler : MusicPlayerActionHandler() {
        override fun handle(intent: Intent, service: OverlayService) {
            val position = intent.getLongExtra(MusicPlayerService.EXTRA_CURRENT_POSITION, 0L)
            service._currentPosition.value = position
        }
    }

    data object PositionUpdatedHandler : MusicPlayerActionHandler() {
        override fun handle(intent: Intent, service: OverlayService) {
            val position = intent.getLongExtra(MusicPlayerService.EXTRA_CURRENT_POSITION, 0L)
            service._currentPosition.value = position
        }
    }

    data object StateUpdatedHandler : MusicPlayerActionHandler() {
        override fun handle(intent: Intent, service: OverlayService) {
            val song = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG, Song::class.java)
            } else {
                @Suppress("DEPRECATION")
                intent.getSerializableExtra(MusicPlayerService.EXTRA_CURRENT_SONG) as? Song
            }
            val position = intent.getLongExtra(MusicPlayerService.EXTRA_CURRENT_POSITION, 0L)
            
            // Check if this is a different song to avoid unnecessary height recalculations
            val currentSong = service._currentSong.value
            val isSongChange = currentSong?.id != song?.id
            
            service._currentSong.value = song
            song?.let { 
                // Only load lyrics and update height if this is a new song
                if (isSongChange) {
                    service.loadLyricsForSong(it)
                    service.updateOverlayHeightForNewSong(it)
                }
            }
            service._currentPosition.value = position
            
        }
    }

    data object UnknownHandler : MusicPlayerActionHandler() {
        override fun handle(intent: Intent, service: OverlayService) {
        }
    }
}

sealed class MusicPlayerActionMatcher {
    abstract fun getHandler(): MusicPlayerActionHandler

    data object SongChangedMatcher : MusicPlayerActionMatcher() {
        override fun getHandler(): MusicPlayerActionHandler = MusicPlayerActionHandler.SongChangedHandler
    }

    data object PlaybackStateChangedMatcher : MusicPlayerActionMatcher() {
        override fun getHandler(): MusicPlayerActionHandler = MusicPlayerActionHandler.PlaybackStateChangedHandler
    }

    data object PositionUpdatedMatcher : MusicPlayerActionMatcher() {
        override fun getHandler(): MusicPlayerActionHandler = MusicPlayerActionHandler.PositionUpdatedHandler
    }

    data object StateUpdatedMatcher : MusicPlayerActionMatcher() {
        override fun getHandler(): MusicPlayerActionHandler = MusicPlayerActionHandler.StateUpdatedHandler
    }

    data object UnknownMatcher : MusicPlayerActionMatcher() {
        override fun getHandler(): MusicPlayerActionHandler = MusicPlayerActionHandler.UnknownHandler
    }

    companion object {
        fun fromString(action: String): MusicPlayerActionMatcher {
            return when (action) {
                MusicPlayerService.ACTION_SONG_CHANGED -> SongChangedMatcher
                MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED -> PlaybackStateChangedMatcher
                MusicPlayerService.ACTION_POSITION_UPDATED -> PositionUpdatedMatcher
                MusicPlayerService.ACTION_STATE_UPDATED -> StateUpdatedMatcher
                else -> UnknownMatcher
            }
        }
    }
}
