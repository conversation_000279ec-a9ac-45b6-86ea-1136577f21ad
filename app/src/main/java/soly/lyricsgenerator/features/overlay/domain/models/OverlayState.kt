package soly.lyricsgenerator.features.overlay.domain.models

import soly.lyricsgenerator.domain.model.Song

/**
 * Sealed class representing different states of the floating lyrics overlay
 * Using polymorphism instead of when expressions as per architecture guidelines
 */
sealed class OverlayState {
    abstract fun canDisplay(): Boolean
    abstract fun getDisplayMessage(): String
    
    data object PermissionRequired : OverlayState() {
        override fun canDisplay(): Boolean = false
        override fun getDisplayMessage(): String = "Overlay permission required"
    }
    
    data object NoMusic : OverlayState() {
        override fun canDisplay(): Boolean = false
        override fun getDisplayMessage(): String = "No music playing"
    }
    
    data object NoLyrics : OverlayState() {
        override fun canDisplay(): Boolean = false
        override fun getDisplayMessage(): String = "No lyrics available"
    }
    
    data class Active(
        val currentSong: Song?,
        val lyrics: Map<Int, String>,
        val currentPosition: Long,
        val isPlaying: Boolean
    ) : OverlayState() {
        override fun canDisplay(): Boolean = true
        override fun getDisplayMessage(): String = "Overlay active"
    }
}

/**
 * Data class for overlay settings
 */
data class OverlaySettings(
    val transparency: Float = 1.0f,
    val fontSize: Float = 16f,
    val fontColor: Long = 0xFFFFFFFF,
    val backgroundColor: Long = 0xFF000000
)
