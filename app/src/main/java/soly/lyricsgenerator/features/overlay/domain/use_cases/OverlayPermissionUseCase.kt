package soly.lyricsgenerator.features.overlay.domain.use_cases

import android.content.Context
import android.provider.Settings
import dagger.hilt.android.qualifiers.ApplicationContext
import soly.lyricsgenerator.features.overlay.domain.models.OverlayState
import soly.lyricsgenerator.domain.model.Song
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UseCase for managing overlay permission and state
 * Following Clean Architecture and SOLID principles
 */
@Singleton
class OverlayPermissionUseCase @Inject constructor(
    @ApplicationContext private val context: Context
) {
    /**
     * Check if overlay permission is granted using sealed class pattern
     */
    fun checkOverlayPermission(): OverlayPermissionResult {
        return if (Settings.canDrawOverlays(context)) {
            OverlayPermissionResult.Granted
        } else {
            OverlayPermissionResult.Denied
        }
    }
}

/**
 * Sealed class for overlay permission results using polymorphism
 */
sealed class OverlayPermissionResult {
    abstract fun processState(
        currentSong: Song?,
        lyrics: Map<Int, String>?,
        currentPosition: Long,
        isPlaying: Boolean
    ): OverlayState
    
    abstract fun execute(onGranted: () -> Unit, onDenied: () -> Unit)
    
    data object Granted : OverlayPermissionResult() {
        override fun processState(
            currentSong: Song?,
            lyrics: Map<Int, String>?,
            currentPosition: Long,
            isPlaying: Boolean
        ): OverlayState {
            return if (currentSong == null) {
                OverlayState.NoMusic
            } else if (lyrics.isNullOrEmpty()) {
                OverlayState.NoLyrics
            } else {
                OverlayState.Active(currentSong, lyrics, currentPosition, isPlaying)
            }
        }
        
        override fun execute(onGranted: () -> Unit, onDenied: () -> Unit) {
            onGranted()
        }
    }
    
    data object Denied : OverlayPermissionResult() {
        override fun processState(
            currentSong: Song?,
            lyrics: Map<Int, String>?,
            currentPosition: Long,
            isPlaying: Boolean
        ): OverlayState = OverlayState.PermissionRequired
        
        override fun execute(onGranted: () -> Unit, onDenied: () -> Unit) {
            onDenied()
        }
    }
}
