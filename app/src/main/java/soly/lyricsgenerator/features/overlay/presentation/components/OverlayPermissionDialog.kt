package soly.lyricsgenerator.features.overlay.presentation.components

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import soly.lyricsgenerator.R
import soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionUseCase
import timber.log.Timber

/**
 * Composable for requesting overlay permission
 * Following existing permission request patterns
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OverlayPermissionDialog(
    onPermissionGranted: () -> Unit,
    onPermissionDenied: () -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text(
                text = stringResource(R.string.overlay_permission_required),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Text(
                text = stringResource(R.string.overlay_permission_message),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Start
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    requestOverlayPermission(context)
                    onDismissRequest()
                }
            ) {
                Text(stringResource(R.string.grant_overlay_permission))
            }
        },
        dismissButton = {
            TextButton(onClick = onPermissionDenied) {
                Text(stringResource(R.string.cancel))
            }
        },
        modifier = modifier
    )
}

/**
 * Function to request overlay permission
 */
private fun requestOverlayPermission(context: Context) {
    try {
        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
            data = Uri.parse("package:${context.packageName}")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        context.startActivity(intent)
        Timber.tag("DEBUG_FLOW").d("OverlayPermissionDialog: Overlay permission request launched")
    } catch (e: Exception) {
        Timber.tag("DEBUG_FLOW").e(e, "OverlayPermissionDialog: Error launching overlay permission request")
        // Fallback to general settings
        try {
            val settingsIntent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(settingsIntent)
        } catch (fallbackException: Exception) {
            Timber.tag("DEBUG_FLOW").e(fallbackException, "OverlayPermissionDialog: Error launching fallback settings")
        }
    }
}

/**
 * Composable to check and handle overlay permission
 */
@Composable
fun OverlayPermissionHandler(
    overlayPermissionUseCase: OverlayPermissionUseCase,
    onPermissionGranted: () -> Unit,
    onPermissionRequired: () -> Unit,
    content: @Composable () -> Unit
) {
    val permissionResult = remember { overlayPermissionUseCase.checkOverlayPermission() }
    
    LaunchedEffect(Unit) {
        permissionResult.handlePermissionResult(
            onGranted = onPermissionGranted,
            onDenied = onPermissionRequired
        )
    }
    
    content()
}

/**
 * Extension function for permission result handling using polymorphism
 */
private fun soly.lyricsgenerator.features.overlay.domain.use_cases.OverlayPermissionResult.handlePermissionResult(
    onGranted: () -> Unit,
    onDenied: () -> Unit
) {
    // Use polymorphic dispatch instead of when expression
    this.execute(onGranted, onDenied)
}
