package soly.lyricsgenerator

import android.Manifest.permission.POST_NOTIFICATIONS
import android.Manifest.permission.READ_EXTERNAL_STORAGE
import android.Manifest.permission.READ_MEDIA_AUDIO
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions
import androidx.activity.result.contract.ActivityResultContracts.RequestPermission
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Sort
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Create
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults.topAppBarColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.constants.IntentConstants
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.presentation.ForceUpdateScreen
import soly.lyricsgenerator.features.update.presentation.SoftUpdateDialog
import soly.lyricsgenerator.features.update.presentation.UpdateViewModel
import soly.lyricsgenerator.features.shared_audio.presentation.viewmodels.SharedAudioViewModel
import soly.lyricsgenerator.ui.constants.UITestTags
import soly.lyricsgenerator.ui.navigation.NavGraph
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.screens.create_screen.ScreenStateHolder
import soly.lyricsgenerator.ui.screens.tag_editor.TagEditorViewModel
import soly.lyricsgenerator.ui.theme.AppGradientBrush
import soly.lyricsgenerator.ui.theme.DefaultNavigationBarItemColors
import soly.lyricsgenerator.ui.theme.InteractiveAccent
import soly.lyricsgenerator.ui.theme.LyricsGeneratorTheme
import soly.lyricsgenerator.ui.theme.Purple
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.ui.viewmodel.FilesViewModel
import soly.lyricsgenerator.ui.viewmodel.MusicViewModel
import soly.lyricsgenerator.ui.viewmodel.SharedViewModel
import soly.lyricsgenerator.ui.viewmodel.PreferenceKeys
import timber.log.Timber
import java.io.File

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private lateinit var requestPermissionLauncher: ActivityResultLauncher<String>
    private lateinit var requestMultiplePermissionsLauncher: ActivityResultLauncher<Array<String>>
    lateinit var musicViewModel: MusicViewModel
    
    private lateinit var analytics: FirebaseAnalytics
    private val updateViewModel: UpdateViewModel by viewModels()
    private val sharedAudioViewModel: SharedAudioViewModel by viewModels()

    // BroadcastReceiver for ACTION_OPEN_APP (legacy approach)
    private val openAppReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            
            try {
                // Create an explicit intent directly to MainActivity with proper flags - same method as in music service
                val uniqueAction = "broadcast_receiver_${System.currentTimeMillis()}"
                val launchIntent = Intent(context, MainActivity::class.java).apply {
                    action = uniqueAction
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    putExtra("FROM_NOTIFICATION", true)
                    putExtra("TIMESTAMP", System.currentTimeMillis())
                    putExtra("FROM_BROADCAST_RECEIVER", true) // Add unique identifier
                }
                
                context?.startActivity(launchIntent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Configure window for proper keyboard handling with edge-to-edge
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Set up screen state change callback to manage screen timeout
        ScreenStateHolder.setOnStateChangedCallback {
            updateScreenTimeout()
        }

        // Obtain the FirebaseAnalytics instance.
        analytics = Firebase.analytics

        // Check if app was launched from notification
        if (intent != null) {
            
            if (intent.getBooleanExtra("FROM_NOTIFICATION", false)) {
                // Force app to front
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        setLocusContext(null, null)
                    }
                    // Make sure we're in the foreground
                    moveTaskToFront()

                } catch (e: Exception) {

                }
            }
            
            // Handle shared audio intents
            handleIntent(intent)
        }

        // Register broadcast receiver for ACTION_OPEN_APP (still keeping for backward compatibility)
        registerReceiver(
            openAppReceiver,
            IntentFilter(MusicPlayerService.ACTION_OPEN_APP),
            RECEIVER_NOT_EXPORTED
        )


        // Initialize single permission launcher
        requestPermissionLauncher = registerForActivityResult(
            RequestPermission()
        ) { isGranted: Boolean ->
            if (isGranted) {

                // Now, check and request audio permissions
                checkAndRequestAudioPermissionsIfNeeded()
            } else {

                // Permission denied, notify the user
                Toast.makeText(
                    this,
                    getString(R.string.permission_denied), // Ideally, a more specific string like R.string.notification_permission_denied
                    Toast.LENGTH_SHORT
                ).show()
                // If notification permission is denied, we will not proceed to request audio permission automatically.
                // Audio permission can be requested later, e.g., when the user navigates to the music tab.
            }
        }

        // Initialize multiple permissions launcher
        requestMultiplePermissionsLauncher = registerForActivityResult(
            RequestMultiplePermissions()
        ) { permissions: Map<String, Boolean> ->
            val audioPermissionToCheck = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                READ_MEDIA_AUDIO
            } else {
                READ_EXTERNAL_STORAGE
            }

            if (permissions[audioPermissionToCheck] == true) {

                // All permissions granted, load songs
                loadSongs(true)
            } else {

                // Some permissions denied, notify the user
                Toast.makeText(
                    this,
                    getString(R.string.audio_permission_denied),
                    Toast.LENGTH_SHORT
                ).show()
                loadSongs(false)
            }
        }

        // Perform permission checks
        val notificationPermissionRequested = checkAndRequestNotificationPermission()
        if (!notificationPermissionRequested) {
            // If notification permission was not launched (e.g., already granted or SDK < Tiramisu),
            // then directly check for audio permissions.

            checkAndRequestAudioPermissionsIfNeeded()
        }
        // The original call to checkAndRequestAudioPermissions() is removed from here.

        setContent {
            LyricsGeneratorTheme {
                val updateState by updateViewModel.updateState.collectAsState()
                var showSoftUpdateDialog by remember(updateState) {
                    mutableStateOf(updateState is UpdateState.SoftUpdate)
                }

                when (updateState) {
                    is UpdateState.Loading -> {
                        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                            CircularProgressIndicator()
                        }
                    }
                    is UpdateState.ForceUpdate -> {
                        ForceUpdateScreen(onUpdateClick = ::openPlayStore)
                    }
                    else -> {
                        // For NoUpdate or SoftUpdate, show the main app
                        MainScreen(analytics = analytics, sharedAudioViewModel = sharedAudioViewModel)

                        if (showSoftUpdateDialog) {
                            SoftUpdateDialog(
                                onUpdateClick = {
                                    openPlayStore()
                                    showSoftUpdateDialog = false // Dismiss dialog
                                },
                                onDismiss = { 
                                    updateViewModel.dismissSoftUpdate()
                                    showSoftUpdateDialog = false 
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    private fun openPlayStore() {

        val updateUrl = "https://play.google.com/store/apps/details?id=$packageName"
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(updateUrl)).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

        } catch (e: Exception) {

            Toast.makeText(this, R.string.error_play_store, Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * Manages screen timeout based on current app state.
     * Keeps screen on when user is in Create Screen or Preview mode.
     */
    private fun updateScreenTimeout() {
        if (ScreenStateHolder.shouldKeepScreenOn()) {
            // Keep screen on - add the flag to prevent screen timeout
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        } else {
            // Allow normal screen timeout - remove the flag
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        }
    }

    override fun onResume() {
        super.onResume()

        
        // Update screen timeout based on current state
        updateScreenTimeout()
        
        // Check if permissions are granted now (e.g., after returning from Settings)
        val hasPermission = areAudioPermissionsGranted()
        
        if (hasPermission) {

            loadSongs(true)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        
        // Update the activity's intent
        setIntent(intent)
        
        // Check if this new intent is from our notification
        if (intent != null && intent.getBooleanExtra("FROM_NOTIFICATION", false)) {
            
            try {
                // Ensure activity is moved to front of task
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    this.setLocusContext(null, null)
                }
                
                // If MusicViewModel is already initialized, ensure it's connected
                if (::musicViewModel.isInitialized) {

                    // Reconnect to service to ensure up-to-date state
                    musicViewModel.onEnterScreen()
                    musicViewModel.loadSongsIfEmpty()
                    
                    // Ensure service knows about current state
                    MusicPlayerService.startService(this, MusicPlayerService.ACTION_GET_CURRENT_STATE)

                }
                
                // Make sure we're in foreground
                moveTaskToFront()

            } catch (e: Exception) {

                e.printStackTrace()
            }
        }
        
        // Handle shared audio intents
        intent?.let { handleIntent(it) }
    }

    /**
     * Handles incoming intents, including shared audio content
     */
    private fun handleIntent(intent: Intent) {
        Timber.tag("DEBUG_FLOW").d("MainActivity: Handling intent action=${intent.action}")
        
        when (intent.action) {
            IntentConstants.Actions.ACTION_SHARE_AUDIO, 
            IntentConstants.Actions.ACTION_SHARE_MULTIPLE_AUDIO,
            IntentConstants.Actions.ACTION_VIEW_AUDIO -> {
                // Check if it's an audio file being shared or opened
                val type = intent.type
                if (intent.action == IntentConstants.Actions.ACTION_VIEW_AUDIO) {
                    // For VIEW intents, check if we have data URI
                    if (intent.data != null) {
                        Timber.tag("DEBUG_FLOW").d("MainActivity: Processing view audio intent with URI: ${intent.data}")
                        sharedAudioViewModel.processIntent(intent)
                    } else {
                        Timber.tag("DEBUG_FLOW").d("MainActivity: VIEW intent has no data URI, ignoring")
                    }
                } else {
                    // For SHARE intents, check MIME type
                    if (type?.startsWith("audio/") == true || type == "application/octet-stream") {
                        Timber.tag("DEBUG_FLOW").d("MainActivity: Processing shared audio intent")
                        sharedAudioViewModel.processIntent(intent)
                    } else {
                        Timber.tag("DEBUG_FLOW").d("MainActivity: Shared content is not audio, ignoring")
                    }
                }
            }
            else -> {
                Timber.tag("DEBUG_FLOW").d("MainActivity: Intent action ${intent.action} not handled")
            }
        }
    }

    private fun checkAndRequestNotificationPermission(): Boolean { // Returns true if permission request was launched
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {

                requestPermissionLauncher.launch(POST_NOTIFICATIONS)
                return true // Request was launched
            }
        }
        return false // Request was not launched
    }

    fun requestAudioPermissions() {

        
        val permissionToCheck = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            READ_MEDIA_AUDIO
        } else {
            READ_EXTERNAL_STORAGE
        }
        
        // Check current permission status
        val isPermissionGranted = ContextCompat.checkSelfPermission(this, permissionToCheck) == PackageManager.PERMISSION_GRANTED
        
        // Log permission status analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MEDIA_PERMISSION_STATUS) {
            param(AnalyticsConstants.Params.ANDROID_VERSION, Build.VERSION.SDK_INT.toLong())
            param(AnalyticsConstants.Params.PERMISSION_TYPE, permissionToCheck)
            param(AnalyticsConstants.Params.PERMISSION_STATUS, 
                if (isPermissionGranted) AnalyticsConstants.Values.PERMISSION_STATUS_GRANTED 
                else AnalyticsConstants.Values.PERMISSION_STATUS_NOT_GRANTED)
        }
        
        // Check if we should show rationale for permission
        val shouldShowRationale = shouldShowRequestPermissionRationale(permissionToCheck)
        
        // Check if permission is already permanently denied (don't ask again)
        val isPermanentlyDenied = !shouldShowRationale && !isPermissionGranted
        
        if (isPermanentlyDenied) {
            // If permission was permanently denied, take user to settings

            Toast.makeText(
                this,
                getString(R.string.open_settings_message),
                Toast.LENGTH_LONG
            ).show()
            
            // Open app settings
            val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = android.net.Uri.fromParts("package", packageName, null)
            intent.data = uri
            startActivity(intent)
            return
        }
        
        if (shouldShowRationale) {
            // User previously denied, show a dialog explaining why we need permission

            Toast.makeText(
                this,
                getString(R.string.permission_rationale),
                Toast.LENGTH_LONG
            ).show()
        }
        
        // Request permissions after showing rationale
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {

            requestMultiplePermissionsLauncher.launch(arrayOf(READ_MEDIA_AUDIO))
        } else {

            requestMultiplePermissionsLauncher.launch(arrayOf(READ_EXTERNAL_STORAGE))
        }
    }
    
    private fun checkAndRequestAudioPermissionsIfNeeded() {

        if (!areAudioPermissionsGranted()) {
            val permissionsToRequestArray = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                arrayOf(READ_MEDIA_AUDIO)
            } else {
                arrayOf(READ_EXTERNAL_STORAGE)
            }

            requestMultiplePermissionsLauncher.launch(permissionsToRequestArray)
        } else {

            loadSongs(true)
        }
    }
    
    private fun areAudioPermissionsGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(this, READ_MEDIA_AUDIO) == 
                    PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(this, READ_EXTERNAL_STORAGE) == 
                    PackageManager.PERMISSION_GRANTED
        }
    }
    
    private fun loadSongs(hasPermission: Boolean) {
        // Here we would trigger loading songs from the device
        // This will be called after permissions are granted

        
        // Update MusicViewModel with permission state
        if (::musicViewModel.isInitialized) {
            musicViewModel.setHasAudioPermission(hasPermission)
        }
        
        // Only attempt to load songs if permission is granted
        if (hasPermission) {
            MusicPlayerService.startService(this, MusicPlayerService.ACTION_LOAD_SONGS)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // Clean up screen state callback
        ScreenStateHolder.setOnStateChangedCallback(null)
        
        // Deregistrujemo broadcast receiver
        try {
            unregisterReceiver(openAppReceiver)

        } catch (e: Exception) {

        }
    }

    // Helper function to move task to front
    private fun moveTaskToFront() {
        try {
            val am = getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val taskList = am.appTasks
            if (taskList.isNotEmpty()) {
                taskList[0].moveToFront()

            }
        } catch (e: Exception) {

        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(analytics: FirebaseAnalytics, sharedAudioViewModel: SharedAudioViewModel) {
    val navController = rememberNavController()
    var selectedItem by remember { mutableStateOf(0) }
    val currentDestination = navController.currentBackStackEntryAsState().value?.destination?.route

    val activity = LocalContext.current as ComponentActivity
    val sharedViewModel: SharedViewModel = hiltViewModel(activity)

    // Get the MusicViewModel scoped to the Activity lifecycle
    val musicViewModel: MusicViewModel = hiltViewModel()
    
    // Get the FilesViewModel scoped to the Activity lifecycle
    val filesViewModel: FilesViewModel = hiltViewModel()
    
    // Get the CreateViewModel scoped to the Activity lifecycle
    val createViewModel: CreateViewModel = hiltViewModel()
    
    // Collect keyboard visibility state from SharedViewModel
    val isKeyboardVisible by sharedViewModel.isKeyboardVisible
    
    // Store reference to MusicViewModel in MainActivity
    LaunchedEffect(musicViewModel) {
        (activity as MainActivity).musicViewModel = musicViewModel
    }

    // Search state variables
    var isSearchActive by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    var pendingTabSelection by remember { mutableStateOf<Int?>(null) }
    val showConfirmationDialog = remember { mutableStateOf(false) }

    // Handle navigation events & screen view logging
    LaunchedEffect(navController) {
        navController.currentBackStackEntryFlow.collect { backStackEntry ->
            val currentRoute = backStackEntry.destination.route
            val previousRoute = navController.previousBackStackEntry?.destination?.route
            
            // Log screen view
            currentRoute?.let {

                GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
                    param(FirebaseAnalytics.Param.SCREEN_NAME, getScreenName(it))
                    param(FirebaseAnalytics.Param.SCREEN_CLASS, it) // Using route as class identifier for now
                }
            }

            // Track screen transitions for music continuity
            if (previousRoute != null && currentRoute != null) {

                // Notify service of screen transition for music continuity
                MusicPlayerService.startService(
                    context = activity,
                    action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                    sourceScreen = previousRoute,
                    destinationScreen = currentRoute
                )
            }
            
            // Clear search when navigating away from Music or Files screen
            if (isSearchActive && currentRoute != NavRoutes.Music.route && currentRoute != NavRoutes.Files.route) {
                isSearchActive = false
                searchQuery = ""
                musicViewModel.updateSearchQuery("")
                // Also clear files search if needed
                filesViewModel.updateSearchQuery("")

            }
            
            // Update selectedItem based on the current route when backstack changes
            // This is important to handle system back button presses

            when {
                currentRoute == NavRoutes.Music.route -> {
                    if (selectedItem != 0) {

                        selectedItem = 0
                    }
                }
                currentRoute == NavRoutes.Create.route || currentRoute?.startsWith("create/") == true -> {
                    if (selectedItem != 1) {

                        selectedItem = 1
                    }
                }
                currentRoute == NavRoutes.Files.route -> {
                    if (selectedItem != 2) {

                        selectedItem = 2
                    }
                }
                else -> {

                }
            }
        }
    }

    if (showConfirmationDialog.value) {
        AlertDialog(
            onDismissRequest = { showConfirmationDialog.value = false },
            confirmButton = {
                TextButton(onClick = {
                    showConfirmationDialog.value = false
                    sharedViewModel.setUnsavedChanges(false)
                    pendingTabSelection?.let {
                        selectedItem = it
                        when (it) {
                            0 -> {
                                // Notify service of screen transition for Music continuity
                                val currentRoute = navController.currentBackStackEntry?.destination?.route
                                if (currentRoute != null) {

                                    MusicPlayerService.startService(
                                        context = activity,
                                        action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                                        sourceScreen = currentRoute,
                                        destinationScreen = NavRoutes.Music.route
                                    )
                                }
                                
                                navController.navigate(NavRoutes.Music.route) {
                                    popUpTo(NavRoutes.Music.route) { inclusive = true }
                                }
                            }

                            2 -> {
                                // Notify service of screen transition for Files continuity
                                val currentRoute = navController.currentBackStackEntry?.destination?.route
                                if (currentRoute != null) {

                                    MusicPlayerService.startService(
                                        context = activity,
                                        action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                                        sourceScreen = currentRoute,
                                        destinationScreen = NavRoutes.Files.route
                                    )
                                }
                                
                                navController.navigate(NavRoutes.Files.route) {
                                    popUpTo(NavRoutes.Files.route) { inclusive = true }
                                }
                            }
                        }
                    }
                    pendingTabSelection = null
                }) {
                    Text(stringResource(R.string.yes_leave))
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    showConfirmationDialog.value = false
                }) {
                    Text(stringResource(R.string.cancel))
                }
            },
            title = {
                Text(stringResource(R.string.unsaved_changes))
            },
            text = {
                Text(stringResource(R.string.unsaved_changes_message))
            }
        )
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            when {
                currentDestination == NavRoutes.SongDetails.route || 
                currentDestination == NavRoutes.Create.route ||
                currentDestination?.startsWith("create/") == true -> { 
                    /* No TopAppBar for these routes - they manage their own TopAppBar */ 
                }
                currentDestination?.startsWith("tag_editor") == true -> {

                    // Get ViewModel scoped to the current NavBackStackEntry so it's the same instance as TagEditorScreen
                    val backStackEntry = navController.currentBackStackEntry
                    val tagEditorViewModel: TagEditorViewModel = hiltViewModel(backStackEntry!!)
                    val scope = rememberCoroutineScope()
                    val context = LocalContext.current
                    val songData = navController.previousBackStackEntry?.savedStateHandle?.get<soly.lyricsgenerator.domain.model.Song>(soly.lyricsgenerator.ui.navigation.NavigationConstants.KEY_SONG_DATA)



                    
                    // State for MediaStore permission handling
                    var pendingCacheFiles by remember { mutableStateOf<List<File>?>(null) }
                    var pendingFilePaths by remember { mutableStateOf<List<String>?>(null) }
                    

                    // MediaStore write permission launcher for Android R+
                    val writeRequestLauncher = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        rememberLauncherForActivityResult(
                            contract = ActivityResultContracts.StartIntentSenderForResult()
                        ) { result ->
                            if (result.resultCode == ComponentActivity.RESULT_OK) {

                                pendingCacheFiles?.let { cacheFiles ->
                                    pendingFilePaths?.let { filePaths ->
                                        scope.launch {
                                            try {
                                                // Use MediaStore URI instead of file URI
                                                if (songData != null) {
                                                    tagEditorViewModel.persistTagChanges(
                                                        context = context,
                                                        filePaths = filePaths,
                                                        uris = listOf(songData.mediaStoreUri),
                                                        cacheFiles = cacheFiles
                                                    )
                                                }
                                            } catch (e: Exception) {

                                            }
                                        }
                                    }
                                }
                                pendingCacheFiles = null
                                pendingFilePaths = null
                            } else {

                                // Clean up cache files
                                pendingCacheFiles?.forEach { it.delete() }
                                pendingCacheFiles = null
                                pendingFilePaths = null
                            }
                        }
                    } else null
                    
                    TopAppBar(
                        title = { },
                        colors = topAppBarColors(
                            containerColor = Purple,
                            titleContentColor = Color.White,
                            navigationIconContentColor = Color.White,
                            actionIconContentColor = Color.White
                        ),
                        navigationIcon = {
                            IconButton(onClick = { navController.navigateUp() }) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                    contentDescription = stringResource(R.string.back),
                                    tint = Color.White
                                )
                            }
                        },
                        actions = {
                            // Save button for in-place file modification
                            IconButton(onClick = { 

                                val currentState = tagEditorViewModel.uiState.value
                                if (currentState is soly.lyricsgenerator.ui.screens.tag_editor.TagEditorUiState.Loaded) {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                                        // For Android R+, prepare cache files and request permission
                                        tagEditorViewModel.saveTags(context) { cacheFiles ->

                                            pendingCacheFiles = cacheFiles
                                            pendingFilePaths = listOf(currentState.filePath)
                                            
                                            // Create MediaStore write request using proper MediaStore URI
                                            try {
                                                if (songData != null) {
                                                    val pendingIntent = MediaStore.createWriteRequest(
                                                        context.contentResolver,
                                                        listOf(songData.mediaStoreUri)
                                                    )
                                                    writeRequestLauncher?.launch(
                                                        IntentSenderRequest.Builder(pendingIntent).build()
                                                    )
                                                } else {

                                                    // Clean up cache files
                                                    cacheFiles.forEach { it.delete() }
                                                }
                                            } catch (e: Exception) {

                                                // Clean up cache files
                                                cacheFiles.forEach { it.delete() }
                                            }
                                        }
                                    } else {
                                        // For older Android versions, save directly
                                        tagEditorViewModel.saveTags(context)
                                    }
                                }
                            }) {
                                Icon(
                                    imageVector = Icons.Default.Save,
                                    contentDescription = "Save tags to original file",
                                    tint = Color.White
                                )
                            }

                        }
                    )
                }
                currentDestination == NavRoutes.PasteLyricsScreen.route -> {
                    // Show TopAppBar for PasteLyricsScreen only when keyboard is not visible
                    if (!isKeyboardVisible) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(brush = AppGradientBrush)
                        ) {
                            TopAppBar(
                                title = { Text(stringResource(R.string.paste_lyrics_hint)) },
                                colors = topAppBarColors(
                                    containerColor = Color.Transparent,
                                    titleContentColor = Color.White,
                                    navigationIconContentColor = Color.White
                                ),
                                navigationIcon = {
                                    IconButton(onClick = { 
                                        // Try to use the PasteLyricsScreen's back handler first
                                        if (!sharedViewModel.handlePasteLyricsBack()) {
                                            // Fallback to normal navigation if no handler is set
                                            navController.navigateUp()
                                        }
                                    }) {
                                        Icon(
                                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                            contentDescription = stringResource(R.string.back),
                                            tint = Color.White
                                        )
                                    }
                                }
                            )
                        }
                    }
                }
                else -> {
                    if (isSearchActive && (currentDestination == NavRoutes.Music.route || currentDestination == NavRoutes.Files.route || currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true)) {
                        // Custom search bar instead of TopAppBar when search is active
                        val placeholderText = when {
                            currentDestination == NavRoutes.Music.route -> stringResource(id = R.string.search_songs)
                            currentDestination == NavRoutes.Files.route -> stringResource(id = R.string.search_files)
                            currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true -> stringResource(id = R.string.search_songs)
                            else -> stringResource(id = R.string.search_songs)
                        }
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(brush = AppGradientBrush)
                        ) {
                            TopAppBar(
                                title = {
                                    TextField(
                                        value = searchQuery,
                                        onValueChange = {
                                            searchQuery = it
                                             // Update the appropriate ViewModel's query state
                                             when {
                                                 currentDestination == NavRoutes.Music.route -> {
                                                     musicViewModel.updateSearchQuery(it)

                                                 }
                                                 currentDestination == NavRoutes.Files.route -> {
                                                     filesViewModel.updateSearchQuery(it)

                                                 }
                                                 currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true -> {
                                                     createViewModel.updateSearchQuery(it)

                                                 }
                                             }
                                          },
                                          modifier = Modifier
                                            .fillMaxWidth()
                                            .focusRequester(focusRequester),
                                        placeholder = { Text(placeholderText, color = Color.White.copy(alpha = 0.7f)) },
                                        singleLine = true,
                                        colors = TextFieldDefaults.colors(
                                            focusedIndicatorColor = Color.Transparent,
                                            unfocusedIndicatorColor = Color.Transparent,
                                            disabledIndicatorColor = Color.Transparent,
                                            focusedContainerColor = Color.Transparent,
                                            unfocusedContainerColor = Color.Transparent,
                                            cursorColor = Color.White,
                                            focusedTextColor = Color.White,
                                            unfocusedTextColor = Color.White
                                        ),
                                        keyboardOptions = KeyboardOptions.Default.copy(
                                            imeAction = ImeAction.Done
                                        ),
                                        keyboardActions = KeyboardActions(onDone = { 
                                            focusManager.clearFocus() 
                                            // Log search performed event
                                            if (searchQuery.isNotBlank()) {
                                                GlobalAnalyticsHook.logEvent(FirebaseAnalytics.Event.SEARCH) {
                                                    param(FirebaseAnalytics.Param.SEARCH_TERM, searchQuery)
                                                    param(AnalyticsConstants.Params.SEARCH_SCREEN, currentDestination ?: AnalyticsConstants.Params.UNKNOWN)
                                                }

                                            }
                                        }),
                                        leadingIcon = {
                                            IconButton(onClick = {
                                                isSearchActive = false
                                                searchQuery = ""
                                                 // Clear the appropriate ViewModel's query state
                                                 when {
                                                     currentDestination == NavRoutes.Music.route -> {
                                                         musicViewModel.updateSearchQuery("")

                                                     }
                                                     currentDestination == NavRoutes.Files.route -> {
                                                         filesViewModel.updateSearchQuery("")

                                                     }
                                                     currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true -> {
                                                         createViewModel.updateSearchQuery("")

                                                     }
                                                 }
                                              }) {
                                                Icon(
                                                    imageVector = Icons.Default.Close,
                                                    contentDescription = stringResource(R.string.content_desc_close_search),
                                                    tint = Color.White
                                                )
                                            }
                                        }
                                    )
                                },
                                colors = topAppBarColors(
                                    containerColor = Color.Transparent,
                                    titleContentColor = Color.White,
                                    actionIconContentColor = Color.White
                                )
                            )
                        }
                        
                        // Request focus when search is activated
                        LaunchedEffect(isSearchActive) {
                            if (isSearchActive) {
                                focusRequester.requestFocus()

                            }
                        }
                    } else {
                        // Regular TopAppBar (Handles Music, Files, and Settings screen when search is inactive)
                        val titleText = when {
                            currentDestination == NavRoutes.Music.route -> ""
                            currentDestination == NavRoutes.Files.route -> stringResource(id = R.string.files)
                            currentDestination == NavRoutes.Settings.route -> stringResource(id = R.string.settings_title)
                            currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true -> stringResource(id = R.string.song_list)
                            else -> "Lyrics Generator" // Default title
                        }
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(brush = AppGradientBrush)
                        ) {
                            TopAppBar(
                                title = { Text(titleText) },
                                colors = topAppBarColors(
                                    containerColor = Color.Transparent,
                                    titleContentColor = Color.White,
                                    navigationIconContentColor = Color.White, // Ensure nav icon is white
                                    actionIconContentColor = Color.White
                                ),
                                navigationIcon = {
                                    when (currentDestination) {
                                        NavRoutes.Music.route, NavRoutes.Files.route -> {
                                            // Show hamburger on Music and Files screens when search inactive
                                            IconButton(onClick = {

                                                navController.navigate(NavRoutes.Settings.route)
                                            }) {
                                                Icon(
                                                    imageVector = Icons.Default.Menu,
                                                    contentDescription = stringResource(id = R.string.menu_description),
                                                    tint = Color.White // Explicitly set tint
                                                )
                                            }
                                        }
                                        NavRoutes.Settings.route -> {
                                            // Show back arrow on Settings screen
                                            IconButton(onClick = {

                                                navController.popBackStack() // Or navigateUp()
                                            }) {
                                                Icon(
                                                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                                    contentDescription = stringResource(id = R.string.back_button_desc),
                                                    tint = Color.White // Explicitly set tint
                                                )
                                            }
                                        }
                                        else -> {
                                            // Show back arrow for SongPickerScreen and other screens
                                            if (currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true) {
                                                IconButton(onClick = {

                                                    navController.popBackStack()
                                                }) {
                                                    Icon(
                                                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                                        contentDescription = stringResource(id = R.string.back),
                                                        tint = Color.White
                                                    )
                                                }
                                            }
                                        }
                                    }
                                },
                                actions = {
                                    Box(
                                        modifier = Modifier.offset(x = (-12).dp)
                                    ) {
                                        Row {
                                            // Show search button on Music, Files, and SongPicker screens
                                    if (currentDestination == NavRoutes.Music.route || currentDestination == NavRoutes.Files.route || currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true) {
                                        IconButton(onClick = {
                                            isSearchActive = true

                                            // Log search started event
                                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SEARCH_STARTED) {
                                                param(AnalyticsConstants.Params.SCREEN, currentDestination ?: AnalyticsConstants.Params.UNKNOWN)
                                            }
                                        }) {
                                            Icon(
                                                imageVector = Icons.Default.Search,
                                                contentDescription = when {
                                                    currentDestination == NavRoutes.Music.route -> stringResource(R.string.search_songs)
                                                    currentDestination == NavRoutes.Files.route -> stringResource(R.string.search_files)
                                                    currentDestination?.startsWith(NavRoutes.SongPickerScreen.route) == true -> stringResource(R.string.search_songs)
                                                    else -> stringResource(R.string.search_songs)
                                                },
                                                tint = Color.White
                                            )
                                        }
                                    }

                                    if (currentDestination == NavRoutes.Music.route) {
                                        val showFavoritesOnly by musicViewModel.showFavoritesOnly.collectAsState()
                                        IconButton(onClick = { musicViewModel.toggleShowFavoritesOnly() }) {
                                            Icon(
                                                imageVector = if (showFavoritesOnly) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                                                contentDescription = if (showFavoritesOnly) stringResource(R.string.filter_all_songs) else stringResource(R.string.filter_favorites),
                                                tint = if (showFavoritesOnly) InteractiveAccent else Color.White
                                            )
                                        }

                                        // 3-dot menu for filter and sort options
                                        val artistFilter by musicViewModel.artistFilter.collectAsState()
                                        var showOptionsMenu by remember { mutableStateOf(false) }
                                        
                                        Box {
                                            IconButton(
                                                onClick = { showOptionsMenu = true },
                                                modifier = Modifier.testTag(UITestTags.APP_BAR_MORE_OPTIONS)
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Default.MoreVert,
                                                    contentDescription = stringResource(R.string.content_desc_more_options),
                                                    tint = Color.White
                                                )
                                            }
                                            
                                            DropdownMenu(
                                                expanded = showOptionsMenu,
                                                onDismissRequest = { showOptionsMenu = false },
                                                offset = DpOffset(x = 0.dp, y = 4.dp)
                                            ) {
                                                DropdownMenuItem(
                                                    text = { Text(stringResource(R.string.filter_by_artist)) },
                                                    leadingIcon = {
                                                        Icon(
                                                            imageVector = Icons.Default.FilterList,
                                                            contentDescription = null,
                                                            tint = if (artistFilter != PreferenceKeys.DEFAULT_ARTIST_FILTER) InteractiveAccent else MaterialTheme.colorScheme.onSurface
                                                        )
                                                    },
                                                    onClick = {
                                                        showOptionsMenu = false
                                                        musicViewModel.showArtistFilterBottomSheet()
                                                    }
                                                )
                                                DropdownMenuItem(
                                                    text = { Text(stringResource(R.string.sort_by)) },
                                                    leadingIcon = {
                                                        Icon(
                                                            imageVector = Icons.AutoMirrored.Filled.Sort,
                                                            contentDescription = null,
                                                            tint = MaterialTheme.colorScheme.onSurface
                                                        )
                                                    },
                                                    onClick = {
                                                        showOptionsMenu = false
                                                        musicViewModel.showSortBottomSheet()
                                                    }
                                                )
                                            }
                                        }
                                    }
                                    
                                    // Show add button on Files screen
                                    if (currentDestination == NavRoutes.Files.route) {
                                        IconButton(onClick = {

                                            sharedViewModel.importLrcFile()
                                        }) {
                                            Icon(
                                                imageVector = Icons.Default.Add,
                                                contentDescription = stringResource(R.string.import_lrc_file),
                                                tint = Color.White
                                            )
                                        }
                                    }
                                        } // Close Row
                                    }
                                }
                            )
                        }
                    }
                }
            }
        },
        bottomBar = {
            // Log current destination for navigation bar visibility tracking

            if (currentDestination != NavRoutes.SongDetails.route &&
                currentDestination != NavRoutes.PasteLyricsScreen.route &&
                currentDestination != NavRoutes.Settings.route) {

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(brush = AppGradientBrush)
                ) {
                    NavigationBar(
                        modifier = Modifier.testTag(UITestTags.BOTTOM_NAVIGATION_BAR),
                        containerColor = Color.Transparent
                    ) {
                    NavigationBarItem(
                        selected = selectedItem == 0,
                        onClick = {

                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BOTTOM_NAV_ITEM_SELECTED) {
                                param(AnalyticsConstants.Params.ITEM_NAME, AnalyticsConstants.ScreenNames.MUSIC)
                            }
                            // Ignore click if already on this tab
                            if (selectedItem != 0) {
                                if (sharedViewModel.hasUnsavedChanges.value) {
                                    pendingTabSelection = 0
                                    showConfirmationDialog.value = true
                                } else {
                                    // Notify service of screen transition for music continuity
                                    val currentRoute = navController.currentBackStackEntry?.destination?.route
                                    if (currentRoute != null) {

                                        MusicPlayerService.startService(
                                            context = activity,
                                            action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                                            sourceScreen = currentRoute,
                                            destinationScreen = NavRoutes.Music.route
                                        )
                                    }
                                    
                                    selectedItem = 0
                                    navController.navigate(NavRoutes.Music.route) {
                                        popUpTo(NavRoutes.Music.route) { inclusive = true }
                                    }
                                }
                            }
                        },
                        icon = { Icon(Icons.Default.MusicNote, contentDescription = null) },
                        label = { Text(stringResource(R.string.music)) },
                        colors = DefaultNavigationBarItemColors
                    )
                    NavigationBarItem(
                        selected = selectedItem == 1,
                        onClick = {

                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BOTTOM_NAV_ITEM_SELECTED) {
                                param(AnalyticsConstants.Params.ITEM_NAME, AnalyticsConstants.ScreenNames.CREATE)
                            }
                            // Ignore click if already on this tab
                            if (selectedItem != 1) {
                                // Notify service of screen transition for music continuity
                                val currentRoute = navController.currentBackStackEntry?.destination?.route
                                if (currentRoute != null) {

                                    MusicPlayerService.startService(
                                        context = activity,
                                        action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                                        sourceScreen = currentRoute,
                                        destinationScreen = NavRoutes.Create.route
                                    )
                                }
                                
                                selectedItem = 1
                                navController.navigate(NavRoutes.Create.route) {
                                    popUpTo(NavRoutes.Create.route) { inclusive = true }
                                }
                            }
                        },
                        icon = { Icon(Icons.Default.Create, contentDescription = null) },
                        label = { Text(stringResource(R.string.create)) },
                        colors = DefaultNavigationBarItemColors
                    )
                    NavigationBarItem(
                        selected = selectedItem == 2,
                        onClick = {

                            GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.BOTTOM_NAV_ITEM_SELECTED) {
                                param(AnalyticsConstants.Params.ITEM_NAME, AnalyticsConstants.ScreenNames.FILES)
                            }
                            // Ignore click if already on this tab
                            if (selectedItem != 2) {
                                if (sharedViewModel.hasUnsavedChanges.value) {
                                    pendingTabSelection = 2
                                    showConfirmationDialog.value = true
                                } else {
                                    // Notify service of screen transition for music continuity
                                    val currentRoute = navController.currentBackStackEntry?.destination?.route
                                    if (currentRoute != null) {

                                        MusicPlayerService.startService(
                                            context = activity,
                                            action = MusicPlayerService.ACTION_SCREEN_TRANSITION,
                                            sourceScreen = currentRoute,
                                            destinationScreen = NavRoutes.Files.route
                                        )
                                    }
                                    
                                    selectedItem = 2
                                    navController.navigate(NavRoutes.Files.route) {
                                        popUpTo(NavRoutes.Files.route) { inclusive = true }
                                    }
                                }
                            }
                        },
                        icon = { Icon(Icons.Default.Description, contentDescription = null) },
                        label = { Text(stringResource(R.string.files)) },
                        colors = DefaultNavigationBarItemColors
                    )
                    }
                }
            }
        }
     ) { innerPadding ->
         // Observe shared audio navigation events
         val sharedAudioNavigationEvent by sharedAudioViewModel.navigationEvent.collectAsState()
         
         LaunchedEffect(sharedAudioNavigationEvent) {
             sharedAudioNavigationEvent?.let { event ->
                 Timber.tag("DEBUG_FLOW").d("MainActivity: Navigating with shared audio")
                 
                 // Use polymorphic dispatch instead of when expression
                 event.execute(navController)
                 
                 // Clear the navigation event
                 sharedAudioViewModel.clearNavigationEvent()
             }
         }
         
         // Pass the hoisted musicViewModel and filesViewModel instances down
         NavGraph(
             navController = navController,
             modifier = Modifier.padding(innerPadding),
             musicViewModel = musicViewModel, // Pass the instance
             filesViewModel = filesViewModel // Pass the instance
         )
     }
 }

// Helper function to get a more user-friendly screen name from the route
private fun getScreenName(route: String?): String {
    return when (route) {
        NavRoutes.Music.route -> "MusicScreen"
        NavRoutes.Create.route -> "CreateScreen"
        NavRoutes.Files.route -> "FilesScreen"
        NavRoutes.SongDetails.route -> "SongDetailsScreen"
        NavRoutes.PasteLyricsScreen.route -> "PasteLyricsScreen"
        NavRoutes.Settings.route -> "SettingsScreen"
        else -> route ?: "UnknownScreen"
    }
}
