package soly.lyricsgenerator.domain

import android.content.ContentUris
import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import soly.lyricsgenerator.domain.model.Song
import timber.log.Timber
import javax.inject.Inject

class MusicPlayer @Inject constructor(
    private val context: Context
) {
    private var mediaPlayer: MediaPlayer? = null
    private var currentlyPlayingSong: Song? = null
    private var currentPosition: Long = 0L
    var onSongCompletion: (() -> Unit)? = null
    var onError: ((String) -> Unit)? = null

    fun play(song: Song) {
        Timber.d("DEBUG_FLOW: MusicPlayer - play() called for song: ${song.title} by ${song.artist}")
        Timber.d("DEBUG_FLOW: MusicPlayer - Song ID: ${song.id}, Path: ${song.data}")
        
        stop()
        currentlyPlayingSong = song
        
        // Check if file exists before attempting to play
        val file = java.io.File(song.data)
        Timber.d("DEBUG_FLOW: MusicPlayer - checking file existence at: ${file.absolutePath}")
        Timber.d("DEBUG_FLOW: MusicPlayer - file exists: ${file.exists()}, isFile: ${file.isFile}, length: ${file.length()}")
        
        if (!file.exists()) {
            Timber.e("DEBUG_FLOW: MusicPlayer - FILE NOT FOUND: ${song.data}")
            Timber.e("DEBUG_FLOW: MusicPlayer - absolute path: ${file.absolutePath}")
            Timber.e("DEBUG_FLOW: MusicPlayer - parent directory: ${file.parent}, parent exists: ${file.parentFile?.exists()}")
            onError?.invoke("File not found: ${file.name}")
            return
        }
        
        // Check if file is readable
        if (!file.canRead()) {
            Timber.e("DEBUG_FLOW: MusicPlayer - FILE NOT READABLE: ${song.data}")
            onError?.invoke("Cannot read file: ${file.name}")
            return
        }
        
        Timber.d("DEBUG_FLOW: MusicPlayer - file checks passed, creating MediaPlayer")
        
        mediaPlayer = MediaPlayer().apply {
            try {
                // First try to use the direct file path (backward compatibility)
                setDataSource(song.data)
                Timber.d("DEBUG_FLOW: MusicPlayer - Successfully set data source using direct path: ${song.data}")
            } catch (e: Exception) {
                // If direct path fails (likely due to scoped storage), try content URI
                Timber.w("DEBUG_FLOW: MusicPlayer - Direct path failed, trying content URI: ${e.message}")
                try {
                    val contentUri = ContentUris.withAppendedId(
                        MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                        song.id
                    )
                    setDataSource(context, contentUri)
                    Timber.d("DEBUG_FLOW: MusicPlayer - Successfully set data source using content URI: $contentUri")
                } catch (e2: Exception) {
                    // If both fail, log the error and throw to trigger the error listener
                    Timber.e("DEBUG_FLOW: MusicPlayer - Failed to set data source with both path and URI: ${e2.message}")
                    throw SecurityException("Unable to access audio file: ${song.title}. Permissions may be required.", e2)
                }
            }
            
            setOnPreparedListener {
                start()
                Timber.d("DEBUG_FLOW: MusicPlayer - Successfully started playback for: ${song.title}")
            }
            setOnCompletionListener {
                stop()
                onSongCompletion?.invoke()
            }
            setOnErrorListener { _, what, extra ->
                Timber.e("DEBUG_FLOW: MusicPlayer - MediaPlayer error: what=$what, extra=$extra for song: ${song.title}")
                // Return true to indicate we handled the error
                true
            }
            
            try {
                prepareAsync()
            } catch (e: Exception) {
                Timber.e("DEBUG_FLOW: MusicPlayer - Failed to prepare MediaPlayer: ${e.message}")
                throw e
            }
        }
    }

    fun stop() {
        val songBeingReleased = currentlyPlayingSong?.title
        Timber.tag("DEBUG_FLOW").d("MusicPlayer: Stopping and releasing media player for song: $songBeingReleased")
        mediaPlayer?.release()
        mediaPlayer = null
        currentlyPlayingSong = null
        currentPosition = 0L
    }

    fun pause() {
        mediaPlayer?.pause()
        currentPosition = mediaPlayer?.currentPosition?.toLong() ?: 0L
    }

    fun resume() {
        mediaPlayer?.start()
    }

    fun getCurrentPosition(): Long {
        return mediaPlayer?.currentPosition?.toLong() ?: currentPosition
    }

    fun seekTo(position: Long) {
        mediaPlayer?.seekTo(position.toInt())
        currentPosition = position
    }

    fun getCurrentlyPlayingSong(): Song? {
        return currentlyPlayingSong
    }
    
    /**
     * Updates the currently playing song data with fresh metadata.
     * This preserves the playback state while updating the song information.
     */
    fun updateCurrentlyPlayingSong(updatedSong: Song) {
        if (currentlyPlayingSong?.id == updatedSong.id) {
            currentlyPlayingSong = updatedSong
            Timber.d("DEBUG_FLOW: MusicPlayer - Updated currently playing song metadata: ${updatedSong.title} by ${updatedSong.artist}")
        }
    }

    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying ?: false
    }
}
