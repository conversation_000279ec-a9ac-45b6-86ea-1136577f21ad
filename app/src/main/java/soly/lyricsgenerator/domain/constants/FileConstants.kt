package soly.lyricsgenerator.domain.constants

/**
 * Constants for file handling operations.
 * This centralizes all file-related constants to prevent hardcoding and ensure consistency.
 */
object FileConstants {

    /**
     * MIME Types for supported file formats
     */
    object MimeTypes {
        const val TEXT_PLAIN = "text/plain"           // .txt files
        const val TEXT_WILDCARD = "text/*"            // Other text files
        const val APPLICATION_RTF = "application/rtf" // .rtf files (standard)
        const val TEXT_RTF = "text/rtf"               // .rtf files (alternative)
        const val APPLICATION_LRC = "application/lrc" // .lrc files
        
        /**
         * Array of all supported MIME types for lyrics files
         */
        val SUPPORTED_LYRICS_MIME_TYPES = arrayOf(
            TEXT_PLAIN,
            TEXT_WILDCARD,
            APPLICATION_RTF,
            TEXT_RTF,
            APPLICATION_LRC
        )
    }

    /**
     * MIME Types for supported audio formats
     */
    object AudioMimeTypes {
        const val AUDIO_MPEG = "audio/mpeg"          // .mp3 files
        const val AUDIO_MP4 = "audio/mp4"            // .m4a files  
        const val AUDIO_M4A = "audio/x-m4a"          // .m4a files (alternative)
        const val AUDIO_FLAC = "audio/flac"          // .flac files
        const val AUDIO_OGG = "audio/ogg"            // .ogg files
        const val AUDIO_WAV = "audio/x-wav"          // .wav files
        const val AUDIO_WILDCARD = "audio/*"         // All audio files
        const val OCTET_STREAM = "application/octet-stream" // Binary stream
        
        /**
         * Array of all supported MIME types for shared audio files
         */
        val SUPPORTED_AUDIO_MIME_TYPES = arrayOf(
            AUDIO_MPEG,
            AUDIO_MP4,
            AUDIO_M4A,
            AUDIO_FLAC,
            AUDIO_OGG,
            AUDIO_WAV,
            AUDIO_WILDCARD,
            OCTET_STREAM
        )
    }

    /**
     * File Extensions
     */
    object Extensions {
        const val LRC = ".lrc"
        const val TXT = ".txt"
        const val RTF = ".rtf"
    }
}