package soly.lyricsgenerator.domain.constants

/**
 * Constants for music playback operations.
 * This centralizes all playback-related constants to prevent hardcoding and ensure consistency.
 */
object PlaybackConstants {
    
    /**
     * Seek duration constants in milliseconds
     */
    object SeekDuration {
        /** Duration for 5-second forward/backward seek operations */
        const val FIVE_SECONDS_MS = 5000L
    }
}