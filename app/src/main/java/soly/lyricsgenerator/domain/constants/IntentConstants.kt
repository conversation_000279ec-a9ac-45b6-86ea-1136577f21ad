package soly.lyricsgenerator.domain.constants

/**
 * Constants for intent handling operations.
 * This centralizes all intent-related constants to prevent hardcoding and ensure consistency.
 */
object IntentConstants {

    /**
     * Intent Actions for Audio Functionality
     */
    object Actions {
        const val ACTION_SHARE_AUDIO = "android.intent.action.SEND"
        const val ACTION_SHARE_MULTIPLE_AUDIO = "android.intent.action.SEND_MULTIPLE"
        const val ACTION_VIEW_AUDIO = "android.intent.action.VIEW"
    }

    /**
     * Intent Extras for Audio Content
     */
    object Extras {
        const val EXTRA_SHARED_AUDIO_URI = "shared_audio_uri"
        const val EXTRA_SHARED_AUDIO_URIS = "shared_audio_uris"
        const val EXTRA_SHARED_AUDIO_COUNT = "shared_audio_count"
        const val EXTRA_SHARED_AUDIO_SOURCE = "shared_audio_source"
        const val EXTRA_VIEW_AUDIO_URI = "view_audio_uri"
    }

    /**
     * Intent Categories
     */
    object Categories {
        const val CATEGORY_DEFAULT = "android.intent.category.DEFAULT"
    }

    /**
     * Intent Types for Audio Content
     */
    object Types {
        const val TYPE_AUDIO_ALL = "audio/*"
        const val TYPE_OCTET_STREAM = "application/octet-stream"
        const val TYPE_AUDIO_MP3 = "audio/mp3"
        const val TYPE_AUDIO_MPEG = "audio/mpeg"
        const val TYPE_AUDIO_WAV = "audio/wav"
        const val TYPE_AUDIO_FLAC = "audio/flac"
        const val TYPE_AUDIO_M4A = "audio/mp4"
        const val TYPE_AUDIO_AAC = "audio/aac"
        const val TYPE_AUDIO_OGG = "audio/ogg"
    }
    
    /**
     * Intent Sources for Analytics
     */
    object Sources {
        const val SOURCE_SHARE_INTENT = "share_intent"
        const val SOURCE_VIEW_INTENT = "view_intent"
        const val SOURCE_FILE_MANAGER = "file_manager"
        const val SOURCE_BROWSER = "browser"
        const val SOURCE_UNKNOWN = "unknown"
    }
}