package soly.lyricsgenerator.domain.constants

/**
 * Constants for service-related operations.
 * This centralizes all service action and extra constants to prevent hardcoding.
 */
object ServiceConstants {
    
    /**
     * Service action constants
     */
    object Actions {
        const val UPDATE_SORTING = "soly.lyricsgenerator.action.UPDATE_SORTING"
    }
    
    /**
     * Intent extra key constants
     */
    object Extras {
        const val SORT_TYPE = "EXTRA_SORT_TYPE"
        const val SORT_ORDER = "EXTRA_SORT_ORDER"
    }
}