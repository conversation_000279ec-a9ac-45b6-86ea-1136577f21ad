package soly.lyricsgenerator.domain.di

import android.content.Context
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.data.repository.ContentResolverRepositoryImpl
import soly.lyricsgenerator.data.repository.PreferencesRepositoryImpl
import soly.lyricsgenerator.domain.repository.ContentResolverRepository
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.repository.MusicRepository
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindMusicRepository(
        musicRepository: MusicRepository
    ): IMusicRepository
    
    @Binds
    @Singleton
    abstract fun bindPreferencesRepository(
        preferencesRepositoryImpl: PreferencesRepositoryImpl
    ): PreferencesRepository
    
    @Binds
    @Singleton
    abstract fun bindContentResolverRepository(
        contentResolverRepositoryImpl: ContentResolverRepositoryImpl
    ): ContentResolverRepository
} 