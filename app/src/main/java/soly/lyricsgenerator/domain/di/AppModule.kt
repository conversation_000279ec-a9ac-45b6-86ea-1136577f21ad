package soly.lyricsgenerator.domain.di

import android.content.Context
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.data.repository.FilesRepositoryImpl
import soly.lyricsgenerator.data.repository.changelog.ChangelogRepositoryImpl
import soly.lyricsgenerator.domain.MusicPlayer
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import soly.lyricsgenerator.domain.repository.AudioTagRepositoryImpl
import soly.lyricsgenerator.domain.repository.FilesRepository
import soly.lyricsgenerator.domain.repository.ChangelogRepository
import soly.lyricsgenerator.domain.repository.LrcFileRepository
import soly.lyricsgenerator.domain.repository.LrcFileRepositoryImpl
import soly.lyricsgenerator.domain.repository.MusicRepository
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.service.MusicPlayerService
import soly.lyricsgenerator.domain.service.MusicService
import soly.lyricsgenerator.domain.usecase.MusicUseCase
import soly.lyricsgenerator.domain.usecase.database.song.SaveSongsUseCase
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AppModule {

    @Binds
    @Singleton
    abstract fun bindFilesRepository(impl: FilesRepositoryImpl): FilesRepository

    @Binds
    @Singleton
    abstract fun bindAudioTagRepository(impl: AudioTagRepositoryImpl): AudioTagRepository
    
    @Binds
    @Singleton
    abstract fun bindChangelogRepository(impl: ChangelogRepositoryImpl): ChangelogRepository

    companion object {
        @Singleton
        @Provides
        fun provideMusicService(musicRepository: IMusicRepository, saveSongsUseCase: SaveSongsUseCase): MusicService {
            return MusicService(musicRepository, saveSongsUseCase)
        }

        @Singleton
        @Provides
        fun provideMusicUseCase(musicService: MusicService): MusicUseCase {
            return MusicUseCase(musicService)
        }

        @Singleton
        @Provides
        fun provideMusicPlayer(@ApplicationContext context: Context): MusicPlayer = MusicPlayer(context)

        @Singleton
        @Provides
        fun provideMusicPlayerService() = MusicPlayerService()

        @Provides
        fun provideContext(@ApplicationContext context: Context): Context {
            return context
        }
        
        @Singleton
        @Provides
        fun provideLrcFileRepository(): LrcFileRepository {
            return LrcFileRepositoryImpl()
        }
        
        @Singleton
        @Provides
        fun provideContentResolver(@ApplicationContext context: Context): android.content.ContentResolver {
            return context.contentResolver
        }
    }
}