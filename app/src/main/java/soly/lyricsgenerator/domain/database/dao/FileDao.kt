package soly.lyricsgenerator.domain.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import soly.lyricsgenerator.domain.database.model.File

@Dao
interface FileDao {
    @Insert
    fun insert(file: File)

    @Query("SELECT * FROM files WHERE songId = :songId")
    fun getFilesForSong(songId: Long): List<File>

    // Get all files
    @Query("SELECT * FROM files")
    fun getAllFiles(): List<File>

    @Query("SELECT * FROM files WHERE songId = :songId")
    fun getFileById(songId: Long): File?

    @Query("SELECT * FROM files WHERE id = :id")
    fun getFileByFileId(id: Long): File?

    // Delete file by id
    @Query("DELETE FROM files WHERE id = :id")
    fun deleteFileById(id: Long)

    // Delete all files
    @Query("DELETE FROM files")
    fun deleteAllFiles()
}