package soly.lyricsgenerator.domain.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import soly.lyricsgenerator.domain.database.converters.FileTypeConverter
import soly.lyricsgenerator.domain.database.dao.FileDao
import soly.lyricsgenerator.domain.database.dao.SongDao
import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.model.Song

@Database(entities = [Song::class, File::class], version = 4)
@TypeConverters(FileTypeConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun songDao(): SongDao
    abstract fun fileDao(): FileDao
    
    companion object {
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE songs ADD COLUMN isFavorite INTEGER NOT NULL DEFAULT 0")
            }
        }
        
        val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Clear the songs table since we're changing the ID strategy
                // Songs will be repopulated from MediaStore with correct IDs
                database.execSQL("DELETE FROM songs")
            }
        }
        
        val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add new columns for sorting functionality
                database.execSQL("ALTER TABLE songs ADD COLUMN album TEXT NOT NULL DEFAULT ''")
                database.execSQL("ALTER TABLE songs ADD COLUMN dateAdded INTEGER NOT NULL DEFAULT 0")
            }
        }
    }
}