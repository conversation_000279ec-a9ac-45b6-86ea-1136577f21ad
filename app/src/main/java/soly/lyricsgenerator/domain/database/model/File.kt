package soly.lyricsgenerator.domain.database.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "files")
data class File(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val songId: Long,
    val filePath: String,
    val fileType: FileType,
    val fileName: String
)

sealed class FileType {
    abstract fun getStringResourceId(): Int
    abstract fun getDatabaseValue(): String
    abstract fun getIcon(): ImageVector
    
    data object LRC : FileType() {
        override fun getStringResourceId(): Int = soly.lyricsgenerator.R.string.file_type_lrc
        override fun getDatabaseValue(): String = "LRC"
        override fun getIcon(): ImageVector = Icons.Default.MusicNote
    }
    
    data object TXT : FileType() {
        override fun getStringResourceId(): Int = soly.lyricsgenerator.R.string.file_type_txt
        override fun getDatabaseValue(): String = "TXT"
        override fun getIcon(): ImageVector = Icons.Default.Description
    }
    
    data object RTF : FileType() {
        override fun getStringResourceId(): Int = soly.lyricsgenerator.R.string.file_type_rtf
        override fun getDatabaseValue(): String = "RTF"
        override fun getIcon(): ImageVector = Icons.Default.Description
    }
    
    companion object {
        fun fromDatabaseValue(value: String): FileType {
            return listOf(LRC, TXT, RTF).find { it.getDatabaseValue() == value } ?: TXT
        }
    }
}