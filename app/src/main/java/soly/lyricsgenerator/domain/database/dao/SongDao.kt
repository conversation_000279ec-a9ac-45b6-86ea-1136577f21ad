package soly.lyricsgenerator.domain.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.MapInfo
import androidx.room.OnConflictStrategy
import androidx.room.Query
import soly.lyricsgenerator.domain.model.Song

@Dao
interface SongDao {

    @Insert
    fun insert(song: Song)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertAll(songs: List<Song>)

    @Query("SELECT * FROM songs")
    fun getAllSongs(): List<Song>?

    @Query("SELECT * FROM songs WHERE id = :id")
    fun getSongById(id: Long): Song?

    @Query("DELETE FROM songs WHERE id = :id")
    fun deleteSongById(id: Long)

    @Query("DELETE FROM songs")
    fun deleteAllSongs()

    @Query("UPDATE songs SET isFavorite = :isFavorite WHERE id = :songId")
    fun updateFavoriteStatus(songId: Long, isFavorite: Boolean)

    @Query("UPDATE songs SET isFavorite = NOT isFavorite WHERE id = :songId")
    fun toggleFavoriteStatus(songId: Long)

    @Query("SELECT * FROM songs WHERE isFavorite = 1")
    fun getFavoriteSongs(): List<Song>?

    @Query("SELECT isFavorite FROM songs WHERE id = :songId")
    fun isSongFavorite(songId: Long): Boolean?

    @MapInfo(keyColumn = "id", valueColumn = "isFavorite")
    @Query("SELECT id, isFavorite FROM songs WHERE id IN (:songIds)")
    fun getFavoriteStatusForSongs(songIds: List<Long>): Map<Long, Boolean>
}