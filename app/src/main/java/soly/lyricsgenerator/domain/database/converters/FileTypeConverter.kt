package soly.lyricsgenerator.domain.database.converters

import androidx.room.TypeConverter
import soly.lyricsgenerator.domain.database.model.FileType

class FileTypeConverter {
    
    @TypeConverter
    fun fromFileType(fileType: FileType): String {
        return fileType.getDatabaseValue()
    }
    
    @TypeConverter
    fun toFileType(value: String): FileType {
        return FileType.fromDatabaseValue(value)
    }
}