package soly.lyricsgenerator.domain.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.database.dao.FileDao
import soly.lyricsgenerator.domain.database.dao.SongDao
import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.model.Song
import timber.log.Timber
import javax.inject.Inject

class DatabaseRepository @Inject constructor(
    private val songDao: SongDao,
    private val fileDao: FileDao
) {

    suspend fun getAllSongs() = withContext(Dispatchers.IO) { 
        songDao.getAllSongs() 
    }

    suspend fun insertAllSongs(songs: List<Song>) = withContext(Dispatchers.IO) { 
        songDao.insertAll(songs) 
    }

    suspend fun insertSong(song: Song) = withContext(Dispatchers.IO) { 
        songDao.insert(song) 
    }

    suspend fun getSongById(id: Long) = withContext(Dispatchers.IO) { 
        songDao.getSongById(id) 
    }

    suspend fun deleteSongById(id: Long) = withContext(Dispatchers.IO) { 
        songDao.deleteSongById(id) 
    }

    suspend fun deleteAllSongs() = withContext(Dispatchers.IO) { 
        songDao.deleteAllSongs() 
    }

    suspend fun getFilesForSong(songId: Long) = withContext(Dispatchers.IO) { 
        fileDao.getFilesForSong(songId) 
    }

    suspend fun insertFile(file: File) = withContext(Dispatchers.IO) {
        try {
            val existingFiles = fileDao.getFilesForSong(file.songId)
            if (existingFiles.isNotEmpty()) {
                fileDao.deleteFileById(existingFiles.first().id)
            }
            fileDao.insert(file)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "Error inserting file: ${file.fileName}")
            throw e
        }
    }

    suspend fun getAllFiles() = withContext(Dispatchers.IO) {
        try {
            fileDao.getAllFiles()
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "Error getting all files")
            throw e
        }
    }

    suspend fun deleteFileById(id: Long) = withContext(Dispatchers.IO) { 
        fileDao.deleteFileById(id) 
    }

    suspend fun deleteAllFiles() = withContext(Dispatchers.IO) { 
        fileDao.deleteAllFiles() 
    }

    suspend fun getFileById(id: Long) = withContext(Dispatchers.IO) { 
        fileDao.getFileById(id) 
    }

    suspend fun getFileByFileId(id: Long) = withContext(Dispatchers.IO) { 
        fileDao.getFileByFileId(id) 
    }

    suspend fun toggleSongFavorite(songId: Long) = withContext(Dispatchers.IO) {
        try {
            songDao.toggleFavoriteStatus(songId)
        } catch (e: Exception) {
            throw e
        }
    }

    suspend fun isSongFavorite(songId: Long) = withContext(Dispatchers.IO) {
        try {
            val result = songDao.isSongFavorite(songId)
            result
        } catch (e: Exception) {
            throw e
        }
    }

    suspend fun getFavoriteSongs() = withContext(Dispatchers.IO) {
        songDao.getFavoriteSongs()
    }

    fun getSongDao() = songDao
}