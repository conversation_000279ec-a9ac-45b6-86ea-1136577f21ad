package soly.lyricsgenerator.domain.repository

import Tuple
import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.lrc.LrcContentBuilder
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LrcFileRepositoryImpl @Inject constructor() : LrcFileRepository {
    
    /**
     * Saves an LRC file to the internal storage
     * @param context The application context
     * @param lrcKeyValuePairs Map containing line index, timestamp and text
     * @param fileName Name of the file to save
     * @return True if file was saved successfully, false otherwise
     */
    override suspend fun saveLrcFile(
        context: Context,
        lrcKeyValuePairs: Map<Int, Tuple<Long, String>>,
        fileName: String
    ): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val lrcContent = LrcContentBuilder.buildLrcContent(lrcKeyValuePairs)
            val filePath = "${context.filesDir}/$fileName"
            val file = File(filePath)
            Timber.tag("DEBUG_FLOW").d("LrcFileRepositoryImpl: File path - $filePath")

            file.writeText(lrcContent)
            Timber.tag("DEBUG_FLOW").d("LrcFileRepositoryImpl: File saved successfully")
            true
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("LrcFileRepositoryImpl: Error saving file - ${e.message}")
            e.printStackTrace()
            false
        }
    }
} 