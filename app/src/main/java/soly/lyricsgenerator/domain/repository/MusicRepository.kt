package soly.lyricsgenerator.domain.repository

import android.content.Context
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.database.dao.SongDao
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import timber.log.Timber
import java.io.File
import javax.inject.Inject

class MusicRepository @Inject constructor(
    private val context: Context,
    private val songDao: SongDao,
    private val audioTagRepository: AudioTagRepository
) : IMusicRepository {
    
    companion object {
        private const val BATCH_SIZE = 500 // Safe batch size well below SQLite's 999 limit
        
        // File extension constants
        private const val EXTENSION_WAV = "wav"
        private const val EXTENSION_AIFF = "aiff"
        private const val EXTENSION_AIF = "aif"
        
        // Metadata constants
        private const val UNKNOWN_METADATA = "<unknown>"
        private const val EMPTY_ALBUM = ""
        private const val PATH_SEPARATOR_UNIX = "/"
        private const val PATH_SEPARATOR_WINDOWS = "\\"
        private const val FILE_EXTENSION_DOT = "."
        
    }
    override suspend fun getAllSongs(): List<Song> {
        return querySongs(
            selection = "${MediaStore.Audio.Media.IS_MUSIC} != 0",
            selectionArgs = null
        )
    }

    override suspend fun getSongsByArtist(artist: String): List<Song> {
        return querySongs(
            selection = "${MediaStore.Audio.Media.IS_MUSIC} != 0 AND ${MediaStore.Audio.Media.ARTIST} = ?",
            selectionArgs = arrayOf(artist)
        )
    }

    private suspend fun querySongs(selection: String, selectionArgs: Array<String>?): List<Song> {
        val songs = mutableListOf<Song>()

        val projection = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.TITLE,
            MediaStore.Audio.Media.ARTIST,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.DURATION,
            MediaStore.Audio.Media.ALBUM,
            MediaStore.Audio.Media.DATE_ADDED
        )
        val cursor = context.contentResolver.query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            selectionArgs,
            null
        )
        
        val cursorCount = cursor?.count ?: 0
        val cursorSuccess = cursor != null
        
        
        // Log MediaStore query analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MEDIASTORE_QUERY_EXECUTED) {
            param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
            param(AnalyticsConstants.Params.CURSOR_COUNT, cursorCount.toLong())
            param(AnalyticsConstants.Params.CURSOR_SUCCESS, cursorSuccess)
        }

        cursor?.use {
            val idColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
            val titleColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
            val artistColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
            val dataColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
            val durationColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
            val albumColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM)
            val dateAddedColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_ADDED)

            // First pass: collect all song IDs and basic data
            val songData = mutableListOf<Pair<Long, Pair<String, Pair<String, Pair<String, Pair<Long, Pair<String, Long>>>>>>>()
            val songIds = mutableListOf<Long>()
            
            while (it.moveToNext()) {
                val id = it.getLong(idColumn)
                val title = it.getString(titleColumn)
                val artist = it.getString(artistColumn)
                val data = it.getString(dataColumn)
                val duration = it.getLong(durationColumn)
                val album = it.getString(albumColumn) ?: EMPTY_ALBUM
                val dateAdded = it.getLong(dateAddedColumn)
                
                songIds.add(id)
                songData.add(Pair(id, Pair(title, Pair(artist, Pair(data, Pair(duration, Pair(album, dateAdded)))))))
            }

            // Batch database queries to avoid SQLite variable limit (999)
            val favoriteMap = if (songIds.isNotEmpty()) {
                withContext(Dispatchers.IO) {
                    val batchSize = BATCH_SIZE
                    val combinedResults = mutableMapOf<Long, Boolean>()
                    
                    for (i in songIds.indices step batchSize) {
                        val endIndex = minOf(i + batchSize, songIds.size)
                        val batch = songIds.subList(i, endIndex)
                        val batchResults = songDao.getFavoriteStatusForSongs(batch)
                        combinedResults.putAll(batchResults)
                    }
                    
                    combinedResults
                }
            } else {
                emptyMap()
            }

            // Second pass: build Song objects with cached favorite data and fallback tag reading
            songData.forEach { (id, songInfo) ->
                val (mediaStoreTitle, rest1) = songInfo
                val (mediaStoreArtist, rest2) = rest1
                val (data, rest3) = rest2
                val (duration, rest4) = rest3
                val (album, dateAdded) = rest4
                val isFavorite = favoriteMap[id] ?: false
                
                // Check if we need to fallback to direct file reading for missing metadata
                var finalTitle = mediaStoreTitle
                var finalArtist = mediaStoreArtist
                
                // Only fallback for specific file types that MediaStore can't handle properly
                val fileExtension = File(data).extension.lowercase()
                val problematicFormats = listOf(EXTENSION_WAV, EXTENSION_AIFF, EXTENSION_AIF) // Formats MediaStore struggles with
                
                // Check if MediaStore title looks like a filename instead of proper title
                val titleLooksLikeFilename = mediaStoreTitle.let { title ->
                    // Check if title contains file extension or looks like a path
                    title.endsWith(FILE_EXTENSION_DOT + fileExtension, ignoreCase = true) ||
                            title.contains(PATH_SEPARATOR_UNIX) || title.contains(PATH_SEPARATOR_WINDOWS) ||
                            // Check if title is just the filename without path
                            (File(data).nameWithoutExtension.equals(title, ignoreCase = true))
                } ?: false
                
                val hasProblematicMetadata = (mediaStoreTitle.isBlank() || mediaStoreTitle == UNKNOWN_METADATA || titleLooksLikeFilename) ||
                                           (mediaStoreArtist.isBlank() || mediaStoreArtist == UNKNOWN_METADATA)
                
                val needsFallback = hasProblematicMetadata && fileExtension in problematicFormats
                
                if (needsFallback) {

                    try {
                        // Use AudioTagRepository to read tags directly from file (same as TagEditor)
                        val tagResult = withContext(Dispatchers.IO) {
                            audioTagRepository.readTags(data)
                        }
                        
                        val fileTags = tagResult.process()
                        
                        // Use file tags if they're not empty and MediaStore values are missing/unknown/filename-like
                        if ((mediaStoreTitle.isBlank() || mediaStoreTitle == UNKNOWN_METADATA || titleLooksLikeFilename) && !fileTags.title.isNullOrBlank()) {
                            finalTitle = fileTags.title ?: mediaStoreTitle
                        }
                        
                        if ((mediaStoreArtist.isBlank() || mediaStoreArtist == UNKNOWN_METADATA) && !fileTags.artist.isNullOrBlank()) {
                            finalArtist = fileTags.artist ?: mediaStoreArtist
                        }
                        
                    } catch (e: Exception) {
                        // Continue with MediaStore values even if they're <unknown>
                    }
                }
                
                songs.add(Song(id, finalTitle, finalArtist, data, duration, isFavorite, album, dateAdded))
            }
        }
        
        // Log songs loading result analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SONGS_LOADING_RESULT) {
            param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
            param(AnalyticsConstants.Params.SONGS_COUNT, songs.size.toLong())
            param(AnalyticsConstants.Params.CURSOR_COUNT, cursorCount.toLong())
            param(AnalyticsConstants.Params.LOADING_METHOD, AnalyticsConstants.Values.LOADING_METHOD_MEDIASTORE_QUERY)
        }
        
        return songs
    }
}
