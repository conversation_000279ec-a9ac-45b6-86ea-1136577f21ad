package soly.lyricsgenerator.domain.repository

import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult

/**
 * Repository interface for audio tag operations.
 * Follows Clean Architecture principles with abstract methods for polymorphic dispatch.
 */
interface AudioTagRepository {
    
    /**
     * Reads audio metadata tags from the specified file.
     * @param filePath Absolute path to the audio file
     * @return AudioTagResult containing the read tags or error information
     */
    suspend fun readTags(filePath: String): AudioTagResult<AudioTag>
    
    /**
     * Writes audio metadata tags to the specified file.
     * @param filePath Absolute path to the audio file
     * @param audioTag The tags to write
     * @param createBackup Whether to create a backup copy instead of overwriting
     * @return AudioTagResult containing the written tags or error information
     */
    suspend fun writeTags(
        filePath: String, 
        audioTag: AudioTag, 
        createBackup: Boolean = true
    ): AudioTagResult<AudioTag>
    
    /**
     * Embeds LRC lyrics content directly into the audio file.
     * @param filePath Absolute path to the audio file
     * @param lrcContent The LRC content to embed
     * @param embedSyncedLyrics Whether to embed synchronized lyrics or plain text
     * @return AudioTagResult containing the updated tags or error information
     */
    suspend fun embedLyrics(
        filePath: String,
        lrcContent: String,
        embedSyncedLyrics: Boolean = true
    ): AudioTagResult<AudioTag>
    
    /**
     * Checks if the audio format supports embedded lyrics.
     * @param filePath Absolute path to the audio file
     * @return True if the format supports embedded lyrics
     */
    fun supportsEmbeddedLyrics(filePath: String): Boolean
    
    /**
     * Gets the supported audio formats.
     * @return List of supported file extensions
     */
    fun getSupportedFormats(): List<String>
}