package soly.lyricsgenerator.domain.repository

import Tuple
import android.content.Context

interface LrcFileRepository {
    /**
     * Saves an LRC file to the internal storage
     * @param context The application context
     * @param lrcKeyValuePairs Map containing line index, timestamp and text
     * @param fileName Name of the file to save
     * @return True if file was saved successfully, false otherwise
     */
    suspend fun saveLrcFile(
        context: Context,
        lrcKeyValuePairs: Map<Int, Tuple<Long, String>>,
        fileName: String
    ): Boolean
} 