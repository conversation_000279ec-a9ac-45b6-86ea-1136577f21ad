package soly.lyricsgenerator.domain.repository

import android.content.Context
import soly.lyricsgenerator.domain.model.AudioFormat
import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.utils.AudioTagger
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * Implementation of AudioTagRepository using AudioTagger utility.
 * Follows Clean Architecture and SOLID principles.
 * Injected with Hilt for dependency management.
 */
class AudioTagRepositoryImpl @Inject constructor(
    private val context: Context
) : AudioTagRepository {
    
    companion object {
        private const val TAG = "DEBUG_FLOW_AudioTagRepository"
        
        private val SUPPORTED_FORMATS = listOf(
            "mp3", "flac", "ogg", "oga", "m4a", "mp4", "aac", "wav", "aiff", "aif", "wma",
            "3gp", "amr", "mid", "xmf", "mxmf", "rtttl", "rtx", "ota", "imy", "mp4a", "m4p"
        )
    }
    
    override suspend fun readTags(filePath: String): AudioTagResult<AudioTag> {
        Timber.tag(TAG).d("Reading tags from: $filePath")
        
        // Decode URL encoded characters in file path
        val decodedPath = try {
            java.net.URLDecoder.decode(filePath, "UTF-8")
        } catch (e: Exception) {
            // If URL decoding fails, try manual replacements for common encodings
            val manualDecoded = filePath
                .replace("%20", " ")
                .replace("+", " ")
            Timber.tag(TAG).w("URL decode failed, trying manual: original='$filePath', manual='$manualDecoded'")
            if (manualDecoded != filePath) {
                manualDecoded
            } else {
                filePath
            }
        }
        
        Timber.tag(TAG).d("Decoded path: $decodedPath")
        
        val file = File(decodedPath)
        if (!file.exists()) {
            Timber.tag(TAG).w("File does not exist: $decodedPath")
            
            // Try original path as fallback
            val originalFile = File(filePath)
            if (!originalFile.exists()) {
                return AudioTagResult.Error(
                    IllegalArgumentException("File not found"),
                    "File does not exist at: $decodedPath or $filePath"
                )
            } else {
                Timber.tag(TAG).d("Using original path: $filePath")
                return AudioTagger.readAudioTags(context, filePath)
            }
        }
        
        val extension = file.extension.lowercase()
        Timber.tag(TAG).d("File extension: '$extension' for file: $decodedPath")
        
        // Try to read tags regardless of extension for debugging
        return AudioTagger.readAudioTags(context, decodedPath)
    }
    
    override suspend fun writeTags(
        filePath: String,
        audioTag: AudioTag,
        createBackup: Boolean
    ): AudioTagResult<AudioTag> {
        Timber.tag(TAG).d("Writing tags to: $filePath, backup: $createBackup")
        
        // Decode URL encoded characters in file path
        val decodedPath = try {
            java.net.URLDecoder.decode(filePath, "UTF-8")
        } catch (e: Exception) {
            // If URL decoding fails, try manual replacements for common encodings
            val manualDecoded = filePath
                .replace("%20", " ")
                .replace("+", " ")
            Timber.tag(TAG).w("URL decode failed, trying manual: original='$filePath', manual='$manualDecoded'")
            if (manualDecoded != filePath) {
                manualDecoded
            } else {
                filePath
            }
        }
        
        val file = File(decodedPath)
        if (!file.exists()) {
            // Try original path as fallback
            val originalFile = File(filePath)
            if (!originalFile.exists()) {
                return AudioTagResult.Error(
                    IllegalArgumentException("File not found"),
                    "File does not exist at: $decodedPath or $filePath"
                )
            } else {
                // This method no longer supports direct writing - return error to encourage using new save functionality
                return AudioTagResult.Error(
                    UnsupportedOperationException("Direct file writing not supported"),
                    "Use the new save functionality in TagEditorViewModel instead"
                )
            }
        }
        
        // This method no longer supports direct writing - return error to encourage using new save functionality
        return AudioTagResult.Error(
            UnsupportedOperationException("Direct file writing not supported"),
            "Use the new save functionality in TagEditorViewModel instead"
        )
    }
    
    override suspend fun embedLyrics(
        filePath: String,
        lrcContent: String,
        embedSyncedLyrics: Boolean
    ): AudioTagResult<AudioTag> {
        Timber.tag(TAG).d("Embedding lyrics into: $filePath, synced: $embedSyncedLyrics")
        
        // Decode URL encoded characters in file path
        val decodedPath = try {
            java.net.URLDecoder.decode(filePath, "UTF-8")
        } catch (e: Exception) {
            // If URL decoding fails, try manual replacements for common encodings
            val manualDecoded = filePath
                .replace("%20", " ")
                .replace("+", " ")
            Timber.tag(TAG).w("URL decode failed, trying manual: original='$filePath', manual='$manualDecoded'")
            if (manualDecoded != filePath) {
                manualDecoded
            } else {
                filePath
            }
        }
        
        val file = File(decodedPath)
        if (!file.exists()) {
            // Try original path as fallback
            val originalFile = File(filePath)
            if (!originalFile.exists()) {
                return AudioTagResult.Error(
                    IllegalArgumentException("File not found"),
                    "File does not exist at: $decodedPath or $filePath"
                )
            } else {
                return AudioTagger.embedLyrics(context, filePath, lrcContent, embedSyncedLyrics)
            }
        }
        
        return AudioTagger.embedLyrics(context, decodedPath, lrcContent, embedSyncedLyrics)
    }
    
    override fun supportsEmbeddedLyrics(filePath: String): Boolean {
        val format = detectAudioFormat(filePath)
        return format.supportsEmbeddedLyrics()
    }
    
    override fun getSupportedFormats(): List<String> {
        return SUPPORTED_FORMATS.toList()
    }
    
    /**
     * Validates if the file is a supported audio format.
     */
    private fun isValidAudioFile(filePath: String): Boolean {
        val file = File(filePath)
        if (!file.exists()) {
            Timber.tag(TAG).w("File does not exist: $filePath")
            return false
        }
        
        val extension = file.extension.lowercase()
        val isSupported = extension in SUPPORTED_FORMATS
        
        Timber.tag(TAG).d("Checking file: $filePath")
        Timber.tag(TAG).d("File extension: '$extension'")
        Timber.tag(TAG).d("Supported formats: $SUPPORTED_FORMATS")
        Timber.tag(TAG).d("Is supported: $isSupported")
        
        if (!isSupported) {
            Timber.tag(TAG).w("Unsupported format: '$extension' for file: $filePath")
        }
        
        return isSupported
    }
    
    /**
     * Detects the audio format based on file extension.
     * Uses polymorphic dispatch with sealed classes.
     */
    private fun detectAudioFormat(filePath: String): AudioFormat {
        val extension = File(filePath).extension.lowercase()
        return when (extension) {
            "mp3" -> AudioFormat.MP3
            "flac" -> AudioFormat.FLAC
            "ogg", "oga" -> AudioFormat.OGG
            "m4a", "mp4", "aac" -> AudioFormat.M4A
            "wav" -> AudioFormat.WAV
            "aiff", "aif" -> AudioFormat.AIFF
            "wma" -> AudioFormat.WMA
            else -> AudioFormat.MP3 // Default fallback
        }
    }
}