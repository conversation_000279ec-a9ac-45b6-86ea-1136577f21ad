package soly.lyricsgenerator.domain.repository

import soly.lyricsgenerator.domain.model.ChangelogEntry

/**
 * Repository interface for changelog data
 * Following Clean Architecture principles with domain layer defining the contract
 */
interface ChangelogRepository {
    /**
     * Get all changelog entries
     * @return List of changelog entries ordered by version (newest first)
     */
    suspend fun getChangelogEntries(): List<ChangelogEntry>
    
    /**
     * Get changelog entries for a specific version
     * @param version The version to get changelog for
     * @return ChangelogEntry if found, null otherwise
     */
    suspend fun getChangelogForVersion(version: String): ChangelogEntry?
}