package soly.lyricsgenerator.domain.repository

import android.net.Uri

/**
 * Repository interface for ContentResolver operations.
 * Follows Clean Architecture - domain layer defines interface, data layer implements.
 */
interface ContentResolverRepository {
    
    /**
     * Resolves a URI to its actual file path
     * @param uri The URI to resolve (e.g., Documents Provider URI)
     * @return The resolved file path or null if cannot be resolved
     */
    suspend fun resolveUriToPath(uri: Uri): String?
    
    /**
     * Gets the display name for a URI
     * @param uri The URI to get display name for
     * @return The display name or null if not available
     */
    suspend fun getDisplayName(uri: Uri): String?
}