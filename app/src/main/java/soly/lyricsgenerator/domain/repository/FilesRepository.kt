package soly.lyricsgenerator.domain.repository

/**
 * Repository interface for handling file-related data operations.
 */
interface FilesRepository {

    /**
     * Retrieves the content of a file specified by its ID.
     *
     * @param fileId The ID of the file whose content is needed.
     * @return The file content as a String, or null if not found or an error occurs.
     */
    suspend fun getFileContentById(fileId: Long): String?

    // TODO: Add other necessary file-related functions (e.g., getFiles, deleteFile, saveFile, etc.)
} 