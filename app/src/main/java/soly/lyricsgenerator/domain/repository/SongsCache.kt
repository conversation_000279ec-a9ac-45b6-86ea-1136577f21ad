package soly.lyricsgenerator.domain.repository

import soly.lyricsgenerator.domain.model.Song
import java.util.concurrent.ConcurrentHashMap
import timber.log.Timber

/**
 * Thread-safe singleton cache for storing songs list in memory.
 * This avoids passing large lists through Intent extras which can cause TransactionTooLargeException.
 */
object SongsCache {
    private val songsMap = ConcurrentHashMap<String, List<Song>>()
    private const val MAIN_SONGS_KEY = "main_songs_list"
    
    /**
     * Store the main songs list
     */
    fun setSongs(songs: List<Song>) {
        Timber.tag("DEBUG_FLOW").d("SongsCache: Storing ${songs.size} songs in cache")
        songsMap[MAIN_SONGS_KEY] = songs.toList() // Create a defensive copy
    }
    
    /**
     * Retrieve the main songs list
     */
    fun getSongs(): List<Song> {
        val songs = songsMap[MAIN_SONGS_KEY] ?: emptyList()
        Timber.tag("DEBUG_FLOW").d("SongsCache: Retrieved ${songs.size} songs from cache")
        return songs
    }
    
    /**
     * Clear the cache
     */
    fun clear() {
        Timber.tag("DEBUG_FLOW").d("SongsCache: Clearing cache")
        songsMap.clear()
    }
    
    /**
     * Check if songs are cached
     */
    fun hasSongs(): Boolean {
        return songsMap.containsKey(MAIN_SONGS_KEY) && !songsMap[MAIN_SONGS_KEY].isNullOrEmpty()
    }
}