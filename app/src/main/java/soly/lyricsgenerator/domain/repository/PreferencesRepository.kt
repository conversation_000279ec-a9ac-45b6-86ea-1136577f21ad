package soly.lyricsgenerator.domain.repository

import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.model.SortOrder

interface PreferencesRepository {
    suspend fun getSortType(): SortType
    suspend fun saveSortType(sortType: SortType)
    
    suspend fun getSortOrder(): SortOrder
    suspend fun saveSortOrder(sortOrder: SortOrder)
    
    suspend fun getShuffleEnabled(): Boolean
    suspend fun saveShuffleEnabled(enabled: Boolean)
    
    suspend fun getShowFavoritesOnly(): Boolean
    suspend fun saveShowFavoritesOnly(showFavoritesOnly: Boolean)

    suspend fun getArtistFilter(): String
    suspend fun saveArtistFilter(artist: String)
}