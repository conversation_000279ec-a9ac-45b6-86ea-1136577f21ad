package soly.lyricsgenerator.domain.model

import android.content.ContentUris
import android.net.Uri
import android.provider.MediaStore
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

@Entity(tableName = "songs")
data class Song(
    @PrimaryKey(autoGenerate = false) val id: Long,
    val title: String,
    val artist: String,
    val data: String,
    val duration: Long,
    val isFavorite: Boolean = false,
    val album: String = "",
    val dateAdded: Long = 0L
) : Serializable {
    
    /**
     * Creates a MediaStore URI for this song that can be used with MediaStore.createWriteRequest()
     */
    val mediaStoreUri: Uri
        get() = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id)
}