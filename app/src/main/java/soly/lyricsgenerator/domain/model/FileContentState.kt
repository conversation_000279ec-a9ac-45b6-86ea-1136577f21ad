package soly.lyricsgenerator.domain.model

/**
 * Sealed class representing different types of file content that can be displayed
 * Follows Clean Architecture with polymorphic dispatch instead of when expressions
 */
sealed class FileContentState {
    abstract fun isEmpty(): Boolean
    abstract fun extractLrcLines(): Map<Int, String>
    abstract fun <T> accept(visitor: FileContentVisitor<T>): T
    
    /**
     * LRC file content with timestamps and synchronized lyrics
     */
    data class LrcContent(val lrcLines: Map<Int, String>) : FileContentState() {
        override fun isEmpty(): Boolean = lrcLines.isEmpty()
        override fun extractLrcLines(): Map<Int, String> = lrcLines
        override fun <T> accept(visitor: FileContentVisitor<T>): T = visitor.visitLrcContent(this)
    }
    
    /**
     * Plain text file content with lines of text
     */
    data class TxtContent(val textLines: List<String>) : FileContentState() {
        override fun isEmpty(): Boolean = textLines.isEmpty()
        override fun extractLrcLines(): Map<Int, String> = emptyMap() // TXT has no timestamps
        override fun <T> accept(visitor: FileContentVisitor<T>): T = visitor.visitTxtContent(this)
    }

    /**
     * RTF (Rich Text Format) document content
     */
    data class RtfContent(val textLines: List<String>) : FileContentState() {
        override fun isEmpty(): Boolean = textLines.isEmpty()
        override fun extractLrcLines(): Map<Int, String> = emptyMap()
        override fun <T> accept(visitor: FileContentVisitor<T>): T = visitor.visitRtfContent(this)
    }
    
    /**
     * Empty state when no content is available
     */
    data object Empty : FileContentState() {
        override fun isEmpty(): Boolean = true
        override fun extractLrcLines(): Map<Int, String> = emptyMap()
        override fun <T> accept(visitor: FileContentVisitor<T>): T = visitor.visitEmpty(this)
    }
}

/**
 * Visitor interface for FileContentState - allows polymorphic dispatch without when expressions
 */
interface FileContentVisitor<T> {
    fun visitLrcContent(content: FileContentState.LrcContent): T
    fun visitTxtContent(content: FileContentState.TxtContent): T
    fun visitRtfContent(content: FileContentState.RtfContent): T
    fun visitEmpty(content: FileContentState.Empty): T
}