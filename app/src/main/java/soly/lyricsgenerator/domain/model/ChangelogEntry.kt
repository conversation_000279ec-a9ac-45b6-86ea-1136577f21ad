package soly.lyricsgenerator.domain.model

import android.content.Context
import soly.lyricsgenerator.R

/**
 * Domain model representing a changelog entry
 */
data class ChangelogEntry(
    val version: String,
    val date: String,
    val changes: List<ChangelogItem>
)

/**
 * Individual changelog item with category and description
 */
data class ChangelogItem(
    val category: ChangelogCategory,
    val description: String
)

/**
 * Sealed class representing different types of changelog categories
 * Following the project's pattern of using sealed classes with polymorphism instead of when expressions
 * Uses string resources for proper internationalization and follows zero hardcoded strings policy
 */
sealed class ChangelogCategory {
    abstract fun getDisplayName(context: Context): String
    abstract val iconResource: String
    abstract val emoji: String
    
    data object NewFeatures : ChangelogCategory() {
        override fun getDisplayName(context: Context): String = context.getString(R.string.changelog_category_new_features)
        override val iconResource: String = "new_features"
        override val emoji: String = "✨"
    }
    
    data object Improvements : ChangelogCategory() {
        override fun getDisplayName(context: Context): String = context.getString(R.string.changelog_category_improvements)
        override val iconResource: String = "improvements"
        override val emoji: String = "🔧"
    }
    
    data object BugFixes : ChangelogCategory() {
        override fun getDisplayName(context: Context): String = context.getString(R.string.changelog_category_bug_fixes)
        override val iconResource: String = "bug_fixes"
        override val emoji: String = "🐛"
    }
    
    data object Performance : ChangelogCategory() {
        override fun getDisplayName(context: Context): String = context.getString(R.string.changelog_category_performance)
        override val iconResource: String = "performance"
        override val emoji: String = "⚡"
    }
}