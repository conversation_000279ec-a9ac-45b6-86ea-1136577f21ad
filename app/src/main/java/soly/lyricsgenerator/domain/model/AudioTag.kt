package soly.lyricsgenerator.domain.model

/**
 * Domain model representing audio metadata tags.
 * Supports all major audio formats: MP3, FLAC, OGG, M4A, WAV, AIFF, WMA
 */
data class AudioTag(
    val title: String? = null,
    val artist: String? = null,
    val album: String? = null,
    val track: String? = null,
    val genre: String? = null,
    val year: String? = null,
    val comment: String? = null,
    val lyrics: String? = null,
    val coverArt: ByteArray? = null,
    val isrc: String? = null,
    val musicBrainzId: String? = null,
    val replayGain: String? = null,
    val albumArtist: String? = null,
    val composer: String? = null,
    val discNumber: String? = null,
    val duration: Long? = null
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AudioTag

        if (title != other.title) return false
        if (artist != other.artist) return false
        if (album != other.album) return false
        if (track != other.track) return false
        if (genre != other.genre) return false
        if (year != other.year) return false
        if (comment != other.comment) return false
        if (lyrics != other.lyrics) return false
        if (coverArt != null) {
            if (other.coverArt == null) return false
            if (!coverArt.contentEquals(other.coverArt)) return false
        } else if (other.coverArt != null) return false
        if (isrc != other.isrc) return false
        if (musicBrainzId != other.musicBrainzId) return false
        if (replayGain != other.replayGain) return false
        if (albumArtist != other.albumArtist) return false
        if (composer != other.composer) return false
        if (discNumber != other.discNumber) return false
        if (duration != other.duration) return false

        return true
    }

    override fun hashCode(): Int {
        var result = title?.hashCode() ?: 0
        result = 31 * result + (artist?.hashCode() ?: 0)
        result = 31 * result + (album?.hashCode() ?: 0)
        result = 31 * result + (track?.hashCode() ?: 0)
        result = 31 * result + (genre?.hashCode() ?: 0)
        result = 31 * result + (year?.hashCode() ?: 0)
        result = 31 * result + (comment?.hashCode() ?: 0)
        result = 31 * result + (lyrics?.hashCode() ?: 0)
        result = 31 * result + (coverArt?.contentHashCode() ?: 0)
        result = 31 * result + (isrc?.hashCode() ?: 0)
        result = 31 * result + (musicBrainzId?.hashCode() ?: 0)
        result = 31 * result + (replayGain?.hashCode() ?: 0)
        result = 31 * result + (albumArtist?.hashCode() ?: 0)
        result = 31 * result + (composer?.hashCode() ?: 0)
        result = 31 * result + (discNumber?.hashCode() ?: 0)
        result = 31 * result + (duration?.hashCode() ?: 0)
        return result
    }
}

/**
 * Sealed class representing different audio file formats with their specific tag support.
 * Uses polymorphic dispatch instead of when expressions.
 */
sealed class AudioFormat {
    abstract fun getTagMapping(): Map<String, String>
    abstract fun supportsEmbeddedLyrics(): Boolean
    abstract fun getLyricsTagName(): String
    
    data object MP3 : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "TIT2",
            "artist" to "TPE1", 
            "album" to "TALB",
            "track" to "TRCK",
            "genre" to "TCON",
            "year" to "TYER",
            "comment" to "COMM",
            "lyrics" to "USLT",
            "albumArtist" to "TPE2",
            "composer" to "TCOM",
            "discNumber" to "TPOS",
            "isrc" to "TSRC"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = true
        override fun getLyricsTagName(): String = "USLT"
    }
    
    data object FLAC : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "TITLE",
            "artist" to "ARTIST",
            "album" to "ALBUM", 
            "track" to "TRACKNUMBER",
            "genre" to "GENRE",
            "year" to "DATE",
            "comment" to "COMMENT",
            "lyrics" to "LYRICS",
            "albumArtist" to "ALBUMARTIST",
            "composer" to "COMPOSER",
            "discNumber" to "DISCNUMBER",
            "isrc" to "ISRC"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = true
        override fun getLyricsTagName(): String = "LYRICS"
    }
    
    data object OGG : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "TITLE",
            "artist" to "ARTIST", 
            "album" to "ALBUM",
            "track" to "TRACKNUMBER",
            "genre" to "GENRE",
            "year" to "DATE",
            "comment" to "COMMENT",
            "lyrics" to "LYRICS",
            "albumArtist" to "ALBUMARTIST",
            "composer" to "COMPOSER",
            "discNumber" to "DISCNUMBER"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = true
        override fun getLyricsTagName(): String = "LYRICS"
    }
    
    data object M4A : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "©nam",
            "artist" to "©ART",
            "album" to "©alb",
            "track" to "trkn", 
            "genre" to "©gen",
            "year" to "©day",
            "comment" to "©cmt",
            "lyrics" to "©lyr",
            "albumArtist" to "aART",
            "composer" to "©wrt",
            "discNumber" to "disk"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = true
        override fun getLyricsTagName(): String = "©lyr"
    }
    
    data object WAV : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "INAM",
            "artist" to "IART",
            "album" to "IPRD",
            "track" to "ITRK",
            "genre" to "IGNR", 
            "year" to "ICRD",
            "comment" to "ICMT",
            "lyrics" to "ILYR"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = false
        override fun getLyricsTagName(): String = "ILYR"
    }
    
    data object AIFF : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "INAM",
            "artist" to "IART",
            "album" to "IPRD",
            "track" to "ITRK",
            "genre" to "IGNR",
            "year" to "ICRD", 
            "comment" to "ICMT"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = false
        override fun getLyricsTagName(): String = ""
    }
    
    data object WMA : AudioFormat() {
        override fun getTagMapping(): Map<String, String> = mapOf(
            "title" to "Title",
            "artist" to "Author",
            "album" to "WM/AlbumTitle", 
            "track" to "WM/Track",
            "genre" to "WM/Genre",
            "year" to "WM/Year",
            "comment" to "Description",
            "lyrics" to "WM/Lyrics",
            "albumArtist" to "WM/AlbumArtist",
            "composer" to "WM/Composer"
        )
        
        override fun supportsEmbeddedLyrics(): Boolean = true
        override fun getLyricsTagName(): String = "WM/Lyrics"
    }
}

/**
 * Sealed class representing different audio tag operation results.
 * Uses polymorphic dispatch instead of when expressions.
 */
sealed class AudioTagResult<T> {
    abstract fun process(): T
    abstract fun getResultMessage(): String
    
    data class Success<T>(val data: T, val successMessage: String = "") : AudioTagResult<T>() {
        override fun process(): T = data
        override fun getResultMessage(): String = successMessage
    }
    
    data class Error<T>(val exception: Exception, val errorMessage: String) : AudioTagResult<T>() {
        override fun process(): T = throw exception
        override fun getResultMessage(): String = errorMessage
    }
    
    data class Loading<T>(val loadingMessage: String = "Processing...") : AudioTagResult<T>() {
        override fun process(): T = throw IllegalStateException("Operation still in progress")
        override fun getResultMessage(): String = loadingMessage
    }
}

/**
 * Sealed class representing different audio tag operations.
 * Uses polymorphic dispatch instead of when expressions.
 */
sealed class AudioTagOperation {
    abstract suspend fun execute(filePath: String, audioTag: AudioTag? = null): AudioTagResult<AudioTag>
    
    data object ReadTags : AudioTagOperation() {
        override suspend fun execute(filePath: String, audioTag: AudioTag?): AudioTagResult<AudioTag> {
            // Implementation will be in AudioTagger
            throw NotImplementedError("Implementation delegated to AudioTagger")
        }
    }
    
    data class WriteTags(val tags: AudioTag) : AudioTagOperation() {
        override suspend fun execute(filePath: String, audioTag: AudioTag?): AudioTagResult<AudioTag> {
            // Implementation will be in AudioTagger
            throw NotImplementedError("Implementation delegated to AudioTagger")
        }
    }
    
    data class EmbedLyrics(val lrcContent: String) : AudioTagOperation() {
        override suspend fun execute(filePath: String, audioTag: AudioTag?): AudioTagResult<AudioTag> {
            // Implementation will be in AudioTagger
            throw NotImplementedError("Implementation delegated to AudioTagger")
        }
    }
    
    data object ClearTags : AudioTagOperation() {
        override suspend fun execute(filePath: String, audioTag: AudioTag?): AudioTagResult<AudioTag> {
            // Implementation will be in AudioTagger
            throw NotImplementedError("Implementation delegated to AudioTagger")
        }
    }
}