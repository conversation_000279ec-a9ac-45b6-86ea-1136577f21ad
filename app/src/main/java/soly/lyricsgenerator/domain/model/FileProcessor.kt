package soly.lyricsgenerator.domain.model

import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.FileContentState
import soly.lyricsgenerator.domain.usecase.lyrics.ParseLyricsUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ReadTxtFileUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ReadRtfFileUseCase

/**
 * Sealed class for file processing strategies using polymorphic dispatch
 * Follows Clean Architecture with no when expressions
 */
sealed class FileProcessor {
    abstract suspend fun processContent(
        filePath: String,
        parseLyricsUseCase: ParseLyricsUseCase,
        readTxtFileUseCase: ReadTxtFileUseCase,
        readRtfFileUseCase: ReadRtfFileUseCase
    ): FileContentState
    
    /**
     * Processor for LRC files with synchronized lyrics
     */
    data object LrcProcessor : FileProcessor() {
        override suspend fun processContent(
            filePath: String,
            parseLyricsUseCase: ParseLyricsUseCase,
            readTxtFileUseCase: ReadTxtFileUseCase,
            readRtfFileUseCase: ReadRtfFileUseCase
        ): FileContentState {
            val lrcLines = parseLyricsUseCase.parseLrcFile(filePath)
            return FileContentState.LrcContent(lrcLines)
        }
    }
    
    /**
     * Processor for TXT files with plain text
     */
    data object TxtProcessor : FileProcessor() {
        override suspend fun processContent(
            filePath: String,
            parseLyricsUseCase: ParseLyricsUseCase,
            readTxtFileUseCase: ReadTxtFileUseCase,
            readRtfFileUseCase: ReadRtfFileUseCase
        ): FileContentState {
            val textLines = readTxtFileUseCase.readTxtFile(filePath)
            return FileContentState.TxtContent(textLines)
        }
    }

    /**
     * Processor for RTF (Rich Text Format) files
     */
    data object RtfProcessor : FileProcessor() {
        override suspend fun processContent(
            filePath: String,
            parseLyricsUseCase: ParseLyricsUseCase,
            readTxtFileUseCase: ReadTxtFileUseCase,
            readRtfFileUseCase: ReadRtfFileUseCase
        ): FileContentState {
            val textLines = readRtfFileUseCase.readRtfFile(filePath)
            return FileContentState.RtfContent(textLines)
        }
    }
    
    companion object {
        /**
         * Factory method to get processor for file type
         */
        fun fromFileType(fileType: FileType): FileProcessor {
            return fileType.toProcessor()
        }
    }
}

/**
 * Extension function to convert FileType to FileProcessor
 * This avoids when expressions by using exhaustive matching
 */
private fun FileType.toProcessor(): FileProcessor {
    val processors = mapOf(
        FileType.LRC to FileProcessor.LrcProcessor,
        FileType.TXT to FileProcessor.TxtProcessor,
        FileType.RTF to FileProcessor.RtfProcessor
    )
    return processors[this] ?: FileProcessor.TxtProcessor // Default fallback
}