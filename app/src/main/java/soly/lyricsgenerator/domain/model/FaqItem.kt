package soly.lyricsgenerator.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Storage
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import soly.lyricsgenerator.R

/**
 * Sealed class representing FAQ items with polymorphic behavior
 * Following project's architecture of using sealed classes instead of when expressions
 */
sealed class FaqItem {
    abstract val id: String
    abstract val icon: ImageVector
    abstract fun getQuestion(): @Composable () -> String
    abstract fun getAnswer(): @Composable () -> String

    data object HowToCreateLyrics : FaqItem() {
        override val id = "how_to_create_lyrics"
        override val icon = Icons.Default.MusicNote
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_create_lyrics_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_create_lyrics_answer)
        }
    }

    data object WhatIsLrcFile : FaqItem() {
        override val id = "what_is_lrc_file"
        override val icon = Icons.Default.Help
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_what_is_lrc_file_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_what_is_lrc_file_answer)
        }
    }

    data object HowToImportLyrics : FaqItem() {
        override val id = "how_to_import_lyrics"
        override val icon = Icons.Default.Storage
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_import_lyrics_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_import_lyrics_answer)
        }
    }

    data object HowToSaveLyrics : FaqItem() {
        override val id = "how_to_save_lyrics"
        override val icon = Icons.Default.Save
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_save_lyrics_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_save_lyrics_answer)
        }
    }

    data object HowToShareLyrics : FaqItem() {
        override val id = "how_to_share_lyrics"
        override val icon = Icons.Default.Share
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_share_lyrics_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_how_to_share_lyrics_answer)
        }
    }

    data object TroubleshootingPermissions : FaqItem() {
        override val id = "troubleshooting_permissions"
        override val icon = Icons.Default.Settings
        override fun getQuestion(): @Composable () -> String = {
            stringResource(R.string.faq_troubleshooting_permissions_question)
        }
        override fun getAnswer(): @Composable () -> String = {
            stringResource(R.string.faq_troubleshooting_permissions_answer)
        }
    }

    companion object {
        fun getAllFaqItems(): List<FaqItem> = listOf(
            HowToCreateLyrics,
            WhatIsLrcFile,
            HowToImportLyrics,
            HowToSaveLyrics,
            HowToShareLyrics,
            TroubleshootingPermissions
        )
    }
}