package soly.lyricsgenerator.domain.model

import android.graphics.Bitmap
import org.jaudiotagger.tag.FieldKey

/**
 * Data class containing information needed to write audio tags to files.
 * Based on BoomingMusic's implementation for compatibility with MediaStore API.
 */
data class WriteInfo(
    val paths: List<String>,
    val values: Map<FieldKey, String?>? = null,
    val artworkInfo: ArtworkInfo? = null
)

/**
 * Data class containing artwork information for audio files.
 */
data class ArtworkInfo(
    val albumId: Long,
    val artwork: Bitmap?
)

/**
 * Result of tag saving operation.
 */
data class SaveTagsResult(
    val isLoading: Boolean,
    val isSuccess: Boolean,
    val cacheFiles: List<java.io.File>? = null
)