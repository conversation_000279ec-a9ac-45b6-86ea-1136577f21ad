package soly.lyricsgenerator.domain.model

import android.net.Uri

/**
 * Represents resolved information about a shared audio URI.
 * Contains both the original URI and resolved metadata for matching.
 */
data class ResolvedAudioInfo(
    val originalUri: Uri,
    val resolvedPath: String?,
    val displayName: String?,
    val fileName: String?
) {
    /**
     * Checks if this resolved info has usable data for song matching
     */
    fun hasMatchableData(): Boolean {
        return resolvedPath != null || displayName != null || fileName != null
    }
}