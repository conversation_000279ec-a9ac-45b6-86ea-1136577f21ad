package soly.lyricsgenerator.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDownward
import androidx.compose.material.icons.filled.ArrowUpward
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * Sealed class representing different sort types for songs.
 * Following project guidelines with polymorphism instead of when expressions.
 */
sealed class SortType {
    abstract fun compare(song1: Song, song2: Song, order: SortOrder): Int
    abstract fun getDisplayName(): Int // Returns string resource ID
    
    companion object {
        fun fromName(name: String): SortType {
            return name.let { typeName ->
                listOf(Title, Artist, Album, RecentlyAdded)
                    .find { it::class.simpleName == typeName } ?: Title
            }
        }
    }
    
    data object Title : SortType() {
        override fun compare(song1: Song, song2: Song, order: SortOrder): Int {
            return order.apply(song1.title.compareTo(song2.title, ignoreCase = true))
        }
        
        override fun getDisplayName(): Int = soly.lyricsgenerator.R.string.tag_title
    }
    
    data object Artist : SortType() {
        override fun compare(song1: Song, song2: Song, order: SortOrder): Int {
            return order.apply(song1.artist.compareTo(song2.artist, ignoreCase = true))
        }
        
        override fun getDisplayName(): Int = soly.lyricsgenerator.R.string.tag_artist
    }
    
    data object Album : SortType() {
        override fun compare(song1: Song, song2: Song, order: SortOrder): Int {
            return order.apply(song1.album.compareTo(song2.album, ignoreCase = true))
        }
        
        override fun getDisplayName(): Int = soly.lyricsgenerator.R.string.tag_album
    }
    
    data object RecentlyAdded : SortType() {
        override fun compare(song1: Song, song2: Song, order: SortOrder): Int {
            return order.apply(song1.dateAdded.compareTo(song2.dateAdded))
        }
        
        override fun getDisplayName(): Int = soly.lyricsgenerator.R.string.sort_recently_added
    }
}

/**
 * Sealed class representing sort order (ascending/descending).
 * Following project guidelines with polymorphism instead of when expressions.
 */
sealed class SortOrder {
    abstract fun apply(comparison: Int): Int
    abstract fun getDisplayIcon(): ImageVector
    
    companion object {
        fun fromName(name: String): SortOrder {
            return name.let { orderName ->
                listOf(Ascending, Descending)
                    .find { it::class.simpleName == orderName } ?: Ascending
            }
        }
    }
    
    data object Ascending : SortOrder() {
        override fun apply(comparison: Int): Int = comparison
        
        override fun getDisplayIcon(): ImageVector = Icons.Default.ArrowUpward
    }
    
    data object Descending : SortOrder() {
        override fun apply(comparison: Int): Int = -comparison
        
        override fun getDisplayIcon(): ImageVector = Icons.Default.ArrowDownward
    }
}