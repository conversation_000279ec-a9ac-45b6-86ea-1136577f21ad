package soly.lyricsgenerator.domain.usecase.lyrics

import Tuple
import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.database.model.FileType
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case that handles parsing text files and preparing them for LRC format.
 * This consolidates file type checking, parsing, and LRC data preparation.
 */
class ParseAndPrepareTextFileUseCase @Inject constructor(
    private val parseTextFileUseCase: ParseTextFileUseCase
) {
    
    /**
     * Result sealed class following the project's no-when-expression policy
     */
    sealed class ParseResult {
        abstract fun getLrcData(): Map<Int, Tuple<Long, String>>?
        abstract fun getFileName(): String?
        abstract fun getLineCount(): Int
        abstract fun isSuccess(): Boolean
        abstract fun logResult()
        
        data class Success(
            private val lrcDataValue: Map<Int, Tuple<Long, String>>,
            private val fileNameValue: String,
            val fileType: FileType
        ) : ParseResult() {
            override fun getLrcData() = lrcDataValue
            override fun getFileName() = fileNameValue
            override fun getLineCount() = lrcDataValue.size
            override fun isSuccess() = true
            override fun logResult() {
                Timber.tag("DEBUG_FLOW").d("ParseAndPrepareTextFileUseCase: Loaded ${getLineCount()} lines from $fileType")
                Timber.tag("DEBUG_FLOW").d("ParseAndPrepareTextFileUseCase: lrcKeyValuePairs size: ${lrcDataValue.size}")
            }
        }
        
        data class EmptyFile(private val fileNameValue: String) : ParseResult() {
            override fun getLrcData() = null
            override fun getFileName() = fileNameValue
            override fun getLineCount() = 0
            override fun isSuccess() = false
            override fun logResult() {
                Timber.tag("DEBUG_FLOW").e("ParseAndPrepareTextFileUseCase: File is empty or could not be read")
            }
        }
        
        data class InvalidFile(private val fileIdValue: Long) : ParseResult() {
            override fun getLrcData() = null
            override fun getFileName() = null
            override fun getLineCount() = 0
            override fun isSuccess() = false
            override fun logResult() {
                Timber.tag("DEBUG_FLOW").e("ParseAndPrepareTextFileUseCase: Failed to load text file with id $fileIdValue")
            }
        }
    }
    
    /**
     * Parse a text file and prepare it for LRC format
     * @param file The file to parse (can be null)
     * @param fileId The file ID for logging purposes
     * @return ParseResult with the outcome
     */
    suspend operator fun invoke(file: File?, fileId: Long): ParseResult {
        if (file == null) {
            return ParseResult.InvalidFile(fileId)
        }
        
        if (file.fileType != FileType.TXT && file.fileType != FileType.RTF) {
            return ParseResult.InvalidFile(fileId)
        }
        
        val lines = parseTextFileUseCase.parseTextFile(file)
        
        if (lines.isEmpty()) {
            return ParseResult.EmptyFile(file.fileName)
        }
        
        val lrcData = lines.mapIndexed { index, line ->
            index to Tuple(0L, line)
        }.toMap()
        
        return ParseResult.Success(
            lrcDataValue = lrcData,
            fileNameValue = file.fileName,
            fileType = file.fileType
        )
    }
}