package soly.lyricsgenerator.domain.usecase.lrc

import Tuple
import android.content.Context
import android.widget.Toast
import soly.lyricsgenerator.domain.repository.LrcFileRepository
import javax.inject.Inject

/**
 * Use case for saving LRC files
 */
class SaveLrcFileUseCase @Inject constructor(
    private val lrcFileRepository: LrcFileRepository
) {
    /**
     * Save an LRC file to internal storage and show a toast message on success/failure
     * @param context Application context
     * @param lrcKeyValuePairs Map of line indices to timestamp and text tuples
     * @param fileName Name of the file to save
     * @return True if file was saved successfully, false otherwise
     */
    suspend operator fun invoke(
        context: Context,
        lrcKeyValuePairs: Map<Int, Tuple<Long, String>>,
        fileName: String
    ): Boolean {
        val isFileSaved = lrcFileRepository.saveLrcFile(context, lrcKeyValuePairs, fileName)
        
        // Show toast message based on result
        val message = if (isFileSaved) {
            "File saved successfully."
        } else {
            "An error occurred while saving the file."
        }
        
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        
        return isFileSaved
    }
} 