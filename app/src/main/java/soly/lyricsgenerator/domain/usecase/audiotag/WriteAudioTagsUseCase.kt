package soly.lyricsgenerator.domain.usecase.audiotag

import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import javax.inject.Inject

/**
 * Use case for writing audio metadata tags.
 * Follows Clean Architecture principles with single responsibility.
 */
class WriteAudioTagsUseCase @Inject constructor(
    private val audioTagRepository: AudioTagRepository
) {
    /**
     * Writes audio metadata tags to the specified file.
     * @param filePath Absolute path to the audio file
     * @param audioTag The tags to write
     * @param createBackup Whether to create a backup copy instead of overwriting
     * @return AudioTagResult containing the written tags or error information
     */
    suspend operator fun invoke(
        filePath: String,
        audioTag: AudioTag,
        createBackup: Boolean = true
    ): AudioTagResult<AudioTag> {
        return audioTagRepository.writeTags(filePath, audioTag, createBackup)
    }
}