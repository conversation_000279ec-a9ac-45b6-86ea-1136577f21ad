package soly.lyricsgenerator.domain.usecase.preferences

import soly.lyricsgenerator.domain.model.SortOrder
import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import javax.inject.Inject

data class SortPreferences(
    val sortType: SortType,
    val sortOrder: SortOrder
)

class LoadSortPreferencesUseCase @Inject constructor(
    private val preferencesRepository: PreferencesRepository
) {
    suspend operator fun invoke(): SortPreferences {
        return SortPreferences(
            sortType = preferencesRepository.getSortType(),
            sortOrder = preferencesRepository.getSortOrder()
        )
    }
}