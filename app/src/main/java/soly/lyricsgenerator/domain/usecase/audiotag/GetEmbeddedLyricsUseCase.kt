package soly.lyricsgenerator.domain.usecase.audiotag

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.lrc.flip
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.model.FileContentState
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * Use case for extracting embedded lyrics from audio files and converting them to FileContentState.
 * Follows Clean Architecture principles with single responsibility.
 * Uses polymorphic dispatch with sealed classes instead of when expressions.
 */
class GetEmbeddedLyricsUseCase @Inject constructor(
    private val audioTagRepository: AudioTagRepository
) {
    
    companion object {
        private const val TAG = "DEBUG_FLOW_GetEmbeddedLyrics"
    }
    
    /**
     * Extracts embedded lyrics from audio file and returns appropriate FileContentState.
     * @param song The song to extract lyrics from
     * @return FileContentState containing LRC or TXT content, or Empty if no lyrics found
     */
    suspend operator fun invoke(song: Song): FileContentState = withContext(Dispatchers.IO) {
        try {
            Timber.tag(TAG).d("Attempting to extract embedded lyrics for song: ${song.title} (${song.data})")
            
            // Read audio tags to get embedded lyrics
            val audioTagResult = audioTagRepository.readTags(song.data)
            
            return@withContext audioTagResult.processLyricsExtraction(song)
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error extracting embedded lyrics for song: ${song.title}")
            FileContentState.Empty
        }
    }
    
    /**
     * Extension function to process AudioTagResult and extract lyrics using polymorphic dispatch
     */
    private suspend fun AudioTagResult<*>.processLyricsExtraction(song: Song): FileContentState {
        return this.extractLyricsContent(song)
    }
}

/**
 * Extension function to extract lyrics content from AudioTagResult using polymorphic dispatch.
 * Avoids when expressions by using sealed class behavior.
 */
private suspend fun AudioTagResult<*>.extractLyricsContent(song: Song): FileContentState {
    return LyricsExtractionHandler.fromResult(this).extractLyrics(song)
}

/**
 * Sealed class for handling lyrics extraction using polymorphic dispatch.
 * Eliminates need for when expressions.
 */
private sealed class LyricsExtractionHandler {
    abstract suspend fun extractLyrics(song: Song): FileContentState
    
    data class SuccessHandler(val audioTag: soly.lyricsgenerator.domain.model.AudioTag) : LyricsExtractionHandler() {
        override suspend fun extractLyrics(song: Song): FileContentState {
            val lyrics = audioTag.lyrics
            
            if (lyrics.isNullOrBlank()) {
                Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").d("No embedded lyrics found for song: ${song.title}")
                return FileContentState.Empty
            }
            
            Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").d("Found embedded lyrics for song: ${song.title}, length: ${lyrics.length}")
            
            // Determine if lyrics are LRC format or plain text
            return if (lyrics.isLrcFormat()) {
                lyrics.parseLrcContent(song)
            } else {
                lyrics.createTxtContent(song)
            }
        }
    }
    
    data class ErrorHandler(val exception: Exception, val message: String) : LyricsExtractionHandler() {
        override suspend fun extractLyrics(song: Song): FileContentState {
            Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").e(exception, "Error reading audio tags for song: ${song.title} - $message")
            return FileContentState.Empty
        }
    }
    
    data object LoadingHandler : LyricsExtractionHandler() {
        override suspend fun extractLyrics(song: Song): FileContentState {
            Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").w("Audio tag reading still in progress for song: ${song.title}")
            return FileContentState.Empty
        }
    }
    
    companion object {
        fun fromResult(result: AudioTagResult<*>): LyricsExtractionHandler {
            return try {
                val data = result.process()
                if (data is soly.lyricsgenerator.domain.model.AudioTag) {
                    SuccessHandler(data)
                } else {
                    ErrorHandler(IllegalStateException("Unexpected data type"), "Invalid audio tag data")
                }
            } catch (e: Exception) {
                ErrorHandler(e, result.getResultMessage())
            }
        }
    }
}

/**
 * Extension function to detect if lyrics content is in LRC format
 */
private fun String.isLrcFormat(): Boolean {
    // LRC format contains timestamp patterns like [mm:ss.xx] or [mm:ss]
    val lrcPattern = Regex("\\[\\d{1,2}:\\d{2}(\\.\\d{2})?\\]")
    return this.contains(lrcPattern)
}

/**
 * Extension function to parse LRC content and create LrcContent state
 */
private suspend fun String.parseLrcContent(song: Song): FileContentState = withContext(Dispatchers.IO) {
    try {
        // Create temporary file for flip parser
        val tempFile = File.createTempFile("embedded_lyrics_${song.id}", ".lrc")
        tempFile.writeText(this@parseLrcContent)
        
        try {
            val lrcData = flip.Parse(tempFile)
            
            if (lrcData.Lines.isNotEmpty()) {
                Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").d("Successfully parsed embedded LRC lyrics for song: ${song.title}, ${lrcData.Lines.size} lines")
                FileContentState.LrcContent(lrcData.Lines)
            } else {
                Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").w("LRC parsing returned empty lines for song: ${song.title}, falling back to text")
                <EMAIL>(song)
            }
        } finally {
            tempFile.delete()
        }
        
    } catch (e: Exception) {
        Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").e(e, "Failed to parse embedded LRC lyrics for song: ${song.title}, falling back to text")
        <EMAIL>(song)
    }
}

/**
 * Extension function to create TXT content state from plain text lyrics
 */
private fun String.createTxtContent(song: Song): FileContentState {
    val textLines = this.lines().filter { it.isNotBlank() }
    
    return if (textLines.isNotEmpty()) {
        Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").d("Created text content for song: ${song.title}, ${textLines.size} lines")
        FileContentState.TxtContent(textLines)
    } else {
        Timber.tag("DEBUG_FLOW_GetEmbeddedLyrics").d("No valid text content found for song: ${song.title}")
        FileContentState.Empty
    }
}