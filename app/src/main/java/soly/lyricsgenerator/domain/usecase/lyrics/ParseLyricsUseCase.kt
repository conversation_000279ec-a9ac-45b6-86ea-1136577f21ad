package soly.lyricsgenerator.domain.usecase.lyrics

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.lrc.flip
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * Use case to handle parsing of LRC files
 */
class ParseLyricsUseCase @Inject constructor() {
    
    /**
     * Parse LRC file and return the lyrics lines
     * @param filePath Path to the LRC file
     * @return Map of timestamps to lyrics lines or empty map if parsing fails
     */
    suspend fun parseLrcFile(filePath: String): Map<Int, String> = withContext(Dispatchers.IO) {
        val lrcFile = File(filePath)
        
        if (!lrcFile.exists()) {
            Timber.tag("DEBUG_FLOW").e("ParseLyricsUseCase: LRC file does not exist at path $filePath")
            return@withContext emptyMap()
        }
        
        // Debug: Read raw LRC file content
        try {
            Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: DEBUG - Raw LRC file content:")
            lrcFile.readLines().forEachIndexed { index, line ->
                Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: DEBUG - Line $index: $line")
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("ParseLyricsUseCase: Error reading raw LRC file: ${e.message}")
        }
        
        Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: LRC file exists, parsing content")
        
        try {
            val lrcData = flip.Parse(lrcFile)
            Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: Successfully parsed LRC data with ${lrcData.Lines.size} lines")
            
            // Log the content of the LRC file
            if (lrcData.Lines.isNotEmpty()) {
                Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: LRC content overview:")
                lrcData.Lines.entries.take(Math.min(lrcData.Lines.size, 9)).forEach { (timeMs, line) ->
                    Timber.tag("DEBUG_FLOW").d("ParseLyricsUseCase: LRC line - Time ${timeMs}ms -> \"$line\"")
                }
                
                return@withContext lrcData.Lines
            } else {
                Timber.tag("DEBUG_FLOW").e("ParseLyricsUseCase: LRC file was parsed but contains no lines")
                return@withContext emptyMap()
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("ParseLyricsUseCase: Error parsing LRC file: ${e.message}")
            e.printStackTrace()
            return@withContext emptyMap()
        }
    }
} 