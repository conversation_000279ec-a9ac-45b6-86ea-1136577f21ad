package soly.lyricsgenerator.domain.usecase.database

import soly.lyricsgenerator.domain.model.FileWithSong
import soly.lyricsgenerator.domain.repository.DatabaseRepository
import timber.log.Timber
import javax.inject.Inject

class GetFilesWithSongsUseCase @Inject constructor(
    private val databaseRepository: DatabaseRepository
) {
    suspend operator fun invoke(): List<FileWithSong> {
        Timber.tag("DEBUG_FLOW").d("GetFilesWithSongsUseCase: Getting files with songs")
        val files = databaseRepository.getAllFiles()
        return files.mapNotNull { file ->
            val song = databaseRepository.getSongById(file.songId)
            if (song != null) {
                FileWithSong(file, song)
            } else {
                Timber.tag("DEBUG_FLOW").e("GetFilesWithSongsUseCase: Song not found for file ${file.id}")
                null
            }
        }
    }
} 