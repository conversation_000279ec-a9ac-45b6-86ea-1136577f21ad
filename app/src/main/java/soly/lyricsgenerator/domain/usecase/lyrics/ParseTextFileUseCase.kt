package soly.lyricsgenerator.domain.usecase.lyrics

import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.database.model.FileType
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case that handles parsing of text files (TXT and RTF) and returns parsed lines.
 * This consolidates the logic for reading different text file formats into a single use case.
 */
class ParseTextFileUseCase @Inject constructor(
    private val readTxtFileUseCase: ReadTxtFileUseCase,
    private val readRtfFileUseCase: ReadRtfFileUseCase
) {
    
    /**
     * Parse a text file based on its type and return the lines
     * @param file The file object containing file type and path
     * @return List of parsed text lines or empty list if parsing fails
     */
    suspend fun parseTextFile(file: File): List<String> {
        Timber.tag("DEBUG_FLOW").d("ParseTextFileUseCase: Parsing ${file.fileType} file: ${file.fileName}")
        
        val fileTypeResult = FileTypeParseResult.fromFileType(file.fileType)
        return fileTypeResult.parse(file, readTxtFileUseCase, readRtfFileUseCase)
    }
    
    /**
     * Sealed class for handling different file type parsing with polymorphism
     * Following the project's requirement to avoid when expressions
     */
    private sealed class FileTypeParseResult {
        abstract suspend fun parse(
            file: File,
            txtReader: ReadTxtFileUseCase,
            rtfReader: ReadRtfFileUseCase
        ): List<String>
        
        data object TxtParser : FileTypeParseResult() {
            override suspend fun parse(
                file: File,
                txtReader: ReadTxtFileUseCase,
                rtfReader: ReadRtfFileUseCase
            ): List<String> {
                return txtReader.readTxtFile(file.filePath)
            }
        }
        
        data object RtfParser : FileTypeParseResult() {
            override suspend fun parse(
                file: File,
                txtReader: ReadTxtFileUseCase,
                rtfReader: ReadRtfFileUseCase
            ): List<String> {
                return rtfReader.readRtfFile(file.filePath)
            }
        }
        
        data object UnsupportedParser : FileTypeParseResult() {
            override suspend fun parse(
                file: File,
                txtReader: ReadTxtFileUseCase,
                rtfReader: ReadRtfFileUseCase
            ): List<String> {
                Timber.tag("DEBUG_FLOW").e("ParseTextFileUseCase: Unsupported file type: ${file.fileType}")
                return emptyList()
            }
        }
        
        companion object {
            fun fromFileType(fileType: FileType): FileTypeParseResult {
                return if (fileType == FileType.TXT) {
                    TxtParser
                } else if (fileType == FileType.RTF) {
                    RtfParser
                } else {
                    UnsupportedParser
                }
            }
        }
    }
}