package soly.lyricsgenerator.domain.usecase.audiotag

import soly.lyricsgenerator.domain.database.model.File
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.usecase.database.file.GetFilesForSongUseCase
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for detecting linked LRC files for a given song.
 * Follows Clean Architecture principles with single responsibility.
 */
class GetLinkedLrcFilesUseCase @Inject constructor(
    private val getFilesForSongUseCase: GetFilesForSongUseCase
) {
    
    companion object {
        private const val TAG = "DEBUG_FLOW_GetLinkedLrcFiles"
    }
    
    /**
     * Gets linked LRC files for a song based on song data.
     * @param song The song to find linked LRC files for
     * @return List of linked LRC files
     */
    suspend operator fun invoke(song: Song): List<File> {
        return try {
            Timber.tag(TAG).d("Looking for linked LRC files for song: ${song.title} (ID: ${song.id})")
            
            val files = getFilesForSongUseCase(song.id)
            val lrcFiles = files.filter { file ->
                file.fileName.endsWith(".lrc", ignoreCase = true)
            }
            
            Timber.tag(TAG).d("Found ${lrcFiles.size} LRC files for song ${song.title}")
            lrcFiles.forEach { file ->
                Timber.tag(TAG).d("LRC file: ${file.fileName}")
            }
            
            lrcFiles
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error getting linked LRC files for song: ${song.title}")
            emptyList()
        }
    }
    
    /**
     * Gets linked LRC files for a song based on song ID.
     * @param songId The song ID to find linked LRC files for
     * @return List of linked LRC files
     */
    suspend operator fun invoke(songId: Long): List<File> {
        return try {
            Timber.tag(TAG).d("Looking for linked LRC files for song ID: $songId")
            
            val files = getFilesForSongUseCase(songId)
            val lrcFiles = files.filter { file ->
                file.fileName.endsWith(".lrc", ignoreCase = true)
            }
            
            Timber.tag(TAG).d("Found ${lrcFiles.size} LRC files for song ID $songId")
            
            lrcFiles
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error getting linked LRC files for song ID: $songId")
            emptyList()
        }
    }
}