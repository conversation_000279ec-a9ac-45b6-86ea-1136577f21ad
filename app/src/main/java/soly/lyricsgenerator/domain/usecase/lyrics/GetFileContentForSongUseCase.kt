package soly.lyricsgenerator.domain.usecase.lyrics

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.domain.model.FileContentState
import soly.lyricsgenerator.domain.model.FileProcessor
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByIdUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ReadTxtFileUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ReadRtfFileUseCase
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case to retrieve and parse file content for a song by ID
 * Handles both LRC and TXT file types using polymorphic dispatch
 */
class GetFileContentForSongUseCase @Inject constructor(
    private val getFileByIdUseCase: GetFileByIdUseCase,
    private val parseLyricsUseCase: ParseLyricsUseCase,
    private val readTxtFileUseCase: ReadTxtFileUseCase,
    private val readRtfFileUseCase: ReadRtfFileUseCase
) {
    /**
     * Load and parse file content for a song by ID
     * @param songId ID of the song to get file content for
     * @return FileContentState representing the content or Empty if no file found
     */
    suspend operator fun invoke(songId: Long): FileContentState = withContext(Dispatchers.IO) {

        val file = getFileByIdUseCase(songId) ?: return@withContext FileContentState.Empty


        val processor = FileProcessor.fromFileType(file.fileType)
        return@withContext processor.processContent(
            filePath = file.filePath,
            parseLyricsUseCase = parseLyricsUseCase,
            readTxtFileUseCase = readTxtFileUseCase,
            readRtfFileUseCase = readRtfFileUseCase
        )
    }
}