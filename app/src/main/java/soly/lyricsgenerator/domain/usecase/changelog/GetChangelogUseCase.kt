package soly.lyricsgenerator.domain.usecase.changelog

import soly.lyricsgenerator.domain.model.ChangelogEntry
import soly.lyricsgenerator.domain.repository.ChangelogRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for retrieving changelog entries
 * Following Clean Architecture pattern with business logic in domain layer
 */
@Singleton
class GetChangelogUseCase @Inject constructor(
    private val changelogRepository: ChangelogRepository
) {
    /**
     * Execute the use case to get all changelog entries
     * @return List of changelog entries ordered by version (newest first)
     */
    suspend operator fun invoke(): List<ChangelogEntry> {
        return changelogRepository.getChangelogEntries()
    }
    
    /**
     * Get changelog for a specific version
     * @param version The version to get changelog for
     * @return ChangelogEntry if found, null otherwise
     */
    suspend fun getForVersion(version: String): ChangelogEntry? {
        return changelogRepository.getChangelogForVersion(version)
    }
}