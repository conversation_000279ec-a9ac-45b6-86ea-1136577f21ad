package soly.lyricsgenerator.domain.usecase.lyrics

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByIdUseCase
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case to retrieve and parse lyrics for a song by ID
 */
class GetLyricsForSongUseCase @Inject constructor(
    private val getFileByIdUseCase: GetFileByIdUseCase,
    private val parseLyricsUseCase: ParseLyricsUseCase
) {
    /**
     * Load and parse lyrics for a song by ID
     * @param songId ID of the song to get lyrics for
     * @return Map of timestamps to lyrics lines or empty map if no lyrics found
     */
    suspend operator fun invoke(songId: Long): Map<Int, String> = withContext(Dispatchers.IO) {
        Timber.tag("DEBUG_FLOW").d("GetLyricsForSongUseCase: Loading lyrics for song ID $songId")
        
        val file = getFileByIdUseCase(songId)
        
        if (file == null) {
            Timber.tag("DEBUG_FLOW").d("GetLyricsForSongUseCase: No LRC file found for song ID $songId")
            return@withContext emptyMap()
        }
        
        Timber.tag("DEBUG_FLOW").d("GetLyricsForSongUseCase: Found LRC file ${file.fileName} at ${file.filePath}")
        return@withContext parseLyricsUseCase.parseLrcFile(file.filePath)
    }
} 