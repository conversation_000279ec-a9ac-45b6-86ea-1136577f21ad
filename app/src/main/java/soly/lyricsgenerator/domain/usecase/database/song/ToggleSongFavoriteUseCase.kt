package soly.lyricsgenerator.domain.usecase.database.song

import soly.lyricsgenerator.domain.repository.DatabaseRepository
import javax.inject.Inject

class ToggleSongFavoriteUseCase @Inject constructor(
    private val databaseRepository: DatabaseRepository
) {
    suspend operator fun invoke(songId: Long) {
        try {
            databaseRepository.toggleSongFavorite(songId)
        } catch (e: Exception) {
            throw e
        }
    }
}