package soly.lyricsgenerator.domain.usecase.lyrics

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

/**
 * Use case to handle reading of RTF (Rich Text Format) document files
 */
class ReadRtfFileUseCase @Inject constructor() {

    /**
     * Read RTF file and return the text lines
     * @param filePath Path to the RTF file
     * @return List of text lines or empty list if reading fails
     */
    suspend fun readRtfFile(filePath: String): List<String> = withContext(Dispatchers.IO) {
        val rtfFile = File(filePath)

        if (!rtfFile.exists()) {
            return@withContext emptyList()
        }

        try {
            // Read raw content first to see what we're dealing with
            val rawContent = rtfFile.readText()

            // Try to extract plain text from RTF
            val plainTextLines = extractPlainTextFromRtf(rawContent)
            return@withContext plainTextLines
        } catch (e: Exception) {
            e.printStackTrace()
            return@withContext emptyList()
        }
    }
    
    /**
     * Extract plain text from RTF content using logic extracted from RTF Parser Kit
     * Based on Apache 2.0 licensed RTF Parser Kit by <PERSON>
     */
    private fun extractPlainTextFromRtf(rtfContent: String): List<String> {

        if (!rtfContent.startsWith("{\\rtf")) {
            return emptyList()
        }
        
        try {
            val extractor = SimpleRtfTextExtractor()
            val plainText = extractor.extractText(rtfContent)
            
            val lines = plainText.split('\n')
                .map { it.trim() }
                .filter { it.isNotEmpty() }
            return lines
            
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    /**
     * Simple RTF text extractor based on RTF Parser Kit logic
     * Extracted from Apache 2.0 licensed RTF Parser Kit by Jon Iles
     */
    private class SimpleRtfTextExtractor {
        
        fun extractText(rtfContent: String): String {
            // First, remove all RTF header groups completely
            val cleanedContent = removeHeaderGroups(rtfContent)
            
            val textBuilder = StringBuilder()
            processCleanedContent(cleanedContent, textBuilder)
            
            return textBuilder.toString().trim()
        }
        
        private fun removeHeaderGroups(content: String): String {
            var cleaned = content
            
            // Remove font table group
            cleaned = cleaned.replace(Regex("\\{\\\\fonttbl[^{}]*(?:\\{[^{}]*\\}[^{}]*)*\\}"), "")
            
            // Remove color table group
            cleaned = cleaned.replace(Regex("\\{\\\\colortbl[^}]*\\}"), "")
            
            // Remove expanded color table group
            cleaned = cleaned.replace(Regex("\\{\\\\\\*\\\\expandedcolortbl[^}]*\\}"), "")
            
            // Remove stylesheet group
            cleaned = cleaned.replace(Regex("\\{\\\\stylesheet[^{}]*(?:\\{[^{}]*\\}[^{}]*)*\\}"), "")
            
            // Remove other header control sequences
            cleaned = cleaned.replace(Regex("\\\\paperw\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\paperh\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\margl\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\margr\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\vieww\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\viewh\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\viewkind\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\cocoartf\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\cocoatextscaling\\d+"), "")
            cleaned = cleaned.replace(Regex("\\\\cocoaplatform\\d+"), "")
            
            return cleaned
        }
        
        private fun processCleanedContent(content: String, textBuilder: StringBuilder) {
            var i = 0
            
            while (i < content.length) {
                when (content[i]) {
                    '\\' -> {
                        i = processCommand(content, i, textBuilder)
                    }
                    '{', '}' -> {
                        // Skip braces
                    }
                    else -> {
                        // Regular text character
                        textBuilder.append(content[i])
                    }
                }
                i++
            }
        }
        
        private fun processCommand(content: String, startIndex: Int, textBuilder: StringBuilder): Int {
            var i = startIndex + 1 // Skip the backslash
            
            if (i >= content.length) return i
            
            // Handle special characters
            when (content[i]) {
                '\\' -> {
                    textBuilder.append('\\')
                    return i
                }
                '{' -> {
                    textBuilder.append('{')
                    return i
                }
                '}' -> {
                    textBuilder.append('}')
                    return i
                }
            }
            
            // Extract command name
            val commandStart = i
            while (i < content.length && content[i].isLetter()) {
                i++
            }
            
            if (i > commandStart) {
                val command = content.substring(commandStart, i)
                
                // Handle paragraph breaks and formatting
                when (command) {
                    "par" -> textBuilder.append('\n')
                    "line" -> textBuilder.append('\n')
                    "row" -> textBuilder.append('\n')
                    "tab" -> textBuilder.append('\t')
                    "cell" -> textBuilder.append('\t')
                    // Skip other formatting commands
                }
            }
            
            // Skip command parameters
            while (i < content.length && (content[i].isDigit() || content[i] == '-')) {
                i++
            }
            
            // Skip optional space after command
            if (i < content.length && content[i] == ' ') {
                i++
            }
            
            return i - 1 // Return last processed index
        }
    }
}
