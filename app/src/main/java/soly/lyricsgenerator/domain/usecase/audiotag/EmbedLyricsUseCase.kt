package soly.lyricsgenerator.domain.usecase.audiotag

import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import javax.inject.Inject

/**
 * Use case for embedding LRC lyrics into audio files.
 * Follows Clean Architecture principles with single responsibility.
 */
class EmbedLyricsUseCase @Inject constructor(
    private val audioTagRepository: AudioTagRepository
) {
    /**
     * Embeds LRC lyrics content directly into the audio file.
     * @param filePath Absolute path to the audio file
     * @param lrcContent The LRC content to embed
     * @param embedSyncedLyrics Whether to embed synchronized lyrics or plain text
     * @return AudioTagResult containing the updated tags or error information
     */
    suspend operator fun invoke(
        filePath: String,
        lrcContent: String,
        embedSyncedLyrics: Boolean = true
    ): AudioTagResult<AudioTag> {
        return audioTagRepository.embedLyrics(filePath, lrcContent, embedSyncedLyrics)
    }
    
    /**
     * Checks if the audio format supports embedded lyrics.
     * @param filePath Absolute path to the audio file
     * @return True if the format supports embedded lyrics
     */
    fun supportsEmbeddedLyrics(filePath: String): Boolean {
        return audioTagRepository.supportsEmbeddedLyrics(filePath)
    }
}