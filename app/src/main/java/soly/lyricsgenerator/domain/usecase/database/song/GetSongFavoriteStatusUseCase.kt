package soly.lyricsgenerator.domain.usecase.database.song

import soly.lyricsgenerator.domain.repository.DatabaseRepository
import javax.inject.Inject

class GetSongFavoriteStatusUseCase @Inject constructor(
    private val databaseRepository: DatabaseRepository
) {
    suspend operator fun invoke(songId: Long): <PERSON><PERSON>an {
        try {
            val result = databaseRepository.isSongFavorite(songId) ?: false
            return result
        } catch (e: Exception) {
            throw e
        }
    }
}