package soly.lyricsgenerator.domain.usecase.lyrics

import timber.log.Timber
import javax.inject.Inject

/**
 * Use case to calculate which lyrics lines should be displayed and which is active
 * based on the current playback position.
 */
class CalculateLyricsUseCase @Inject constructor() {
    
    /**
     * Calculate the active line index based on the current time
     * @param lrcLines Map of time in milliseconds to lyrics line
     * @param currentTimeMs Current playback position in milliseconds
     * @return The index of the active line
     */
    fun calculateActiveLineIndex(lrcLines: Map<Int, String>, currentTimeMs: Int): Int {
        if (lrcLines.isEmpty()) {
            Timber.tag("DEBUG_FLOW").d("CalculateLyricsUseCase: No lyrics available, returning -1")
            return -1
        }

        // Find the last line that should be displayed based on the current time
        val index = lrcLines.keys.indexOfLast { it <= currentTimeMs }
        
        Timber.tag("DEBUG_FLOW").d("CalculateLyricsUseCase: Calculated active line index: $index for currentTimeMs: $currentTimeMs")
        return index
    }

    /**
     * Calculate which lines should be displayed around the active line
     * @param lrcLines Map of time in milliseconds to lyrics line
     * @param activeLineIndex Index of the active line
     * @return List of pairs of (time, text) for the lines to display
     */
    fun calculateLinesToDisplay(lrcLines: Map<Int, String>, activeLineIndex: Int): List<Pair<Int, String>> {
        if (lrcLines.isEmpty()) {
            Timber.tag("DEBUG_FLOW").d("CalculateLyricsUseCase: No lines to display, lrcLines empty")
            return emptyList()
        }

        // Convert the map to a sorted list for consistent indexing
        val sortedLines = lrcLines.entries.sortedBy { it.key }.toList()
        Timber.tag("DEBUG_FLOW").d("CalculateLyricsUseCase: Created sortedLines with ${sortedLines.size} entries")
        
        // Return the ENTIRE sorted list
        val linesToShow = sortedLines.map { (timeMs, text) -> timeMs to text }
        Timber.tag("DEBUG_FLOW").d("CalculateLyricsUseCase: Returning ALL ${linesToShow.size} sorted lines")
        return linesToShow
    }
    
    /**
     * Determine if a line is the active line by comparing time stamps
     * @param lineTimeMs The timestamp of the line
     * @param lrcLines All available lyrics lines
     * @param activeLineIndex The index of the active line
     * @return True if this is the active line
     */
    fun isActiveLine(lineTimeMs: Int, lrcLines: Map<Int, String>, activeLineIndex: Int): Boolean {
        if (activeLineIndex < 0 || lrcLines.isEmpty()) return false
        
        val activeLineTimeMs = lrcLines.entries.sortedBy { it.key }.getOrNull(activeLineIndex)?.key
        return lineTimeMs == activeLineTimeMs
    }
} 