package soly.lyricsgenerator.domain.usecase.preferences

import soly.lyricsgenerator.domain.model.SortOrder
import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import javax.inject.Inject

class SaveSortPreferencesUseCase @Inject constructor(
    private val preferencesRepository: PreferencesRepository
) {
    suspend operator fun invoke(sortType: SortType, sortOrder: SortOrder) {
        preferencesRepository.saveSortType(sortType)
        preferencesRepository.saveSortOrder(sortOrder)
    }
}