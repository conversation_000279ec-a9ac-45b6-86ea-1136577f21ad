package soly.lyricsgenerator.domain.usecase.database

import soly.lyricsgenerator.domain.repository.DatabaseRepository
import timber.log.Timber
import javax.inject.Inject

class DeleteFileUseCase @Inject constructor(
    private val databaseRepository: DatabaseRepository
) {
    suspend operator fun invoke(fileId: Long) {
        Timber.tag("DEBUG_FLOW").d("DeleteFileUseCase: Deleting file $fileId")
        databaseRepository.deleteFileById(fileId)
    }
} 