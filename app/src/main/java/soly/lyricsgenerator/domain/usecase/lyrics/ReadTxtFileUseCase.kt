package soly.lyricsgenerator.domain.usecase.lyrics

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * Use case to handle reading of plain text files
 */
class ReadTxtFileUseCase @Inject constructor() {
    
    /**
     * Read TXT file and return the text lines
     * @param filePath Path to the TXT file
     * @return List of text lines or empty list if reading fails
     */
    suspend fun readTxtFile(filePath: String): List<String> = withContext(Dispatchers.IO) {
        val txtFile = File(filePath)
        
        if (!txtFile.exists()) {
            Timber.tag("DEBUG_FLOW").e("ReadTxtFileUseCase: TXT file does not exist at path $filePath")
            return@withContext emptyList()
        }
        
        Timber.tag("DEBUG_FLOW").d("ReadTxtFileUseCase: TXT file exists, reading content")
        
        try {
            val lines = txtFile.readLines()
            Timber.tag("DEBUG_FLOW").d("ReadTxtFileUseCase: Successfully read TXT file with ${lines.size} lines")
            
            return@withContext lines
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("ReadTxtFileUseCase: Error reading TXT file: ${e.message}")
            e.printStackTrace()
            return@withContext emptyList()
        }
    }
}