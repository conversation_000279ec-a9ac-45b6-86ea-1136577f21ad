package soly.lyricsgenerator.domain.usecase.audiotag

import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.repository.AudioTagRepository
import javax.inject.Inject

/**
 * Use case for reading audio metadata tags.
 * Follows Clean Architecture principles with single responsibility.
 */
class ReadAudioTagsUseCase @Inject constructor(
    private val audioTagRepository: AudioTagRepository
) {
    /**
     * Reads audio metadata tags from the specified file.
     * @param filePath Absolute path to the audio file
     * @return AudioTagResult containing the read tags or error information
     */
    suspend operator fun invoke(filePath: String): AudioTagResult<AudioTag> {
        return audioTagRepository.readTags(filePath)
    }
}