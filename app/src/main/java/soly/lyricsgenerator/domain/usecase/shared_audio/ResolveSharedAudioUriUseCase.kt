package soly.lyricsgenerator.domain.usecase.shared_audio

import android.net.Uri
import soly.lyricsgenerator.domain.model.ResolvedAudioInfo
import soly.lyricsgenerator.domain.repository.ContentResolverRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for resolving shared audio URIs to file paths and metadata.
 * Follows Clean Architecture - pure business logic with no Android dependencies.
 */
class ResolveSharedAudioUriUseCase @Inject constructor(
    private val contentResolverRepository: ContentResolverRepository
) {
    
    /**
     * Resolves a shared audio URI to usable information for song matching
     * @param uri The shared audio URI to resolve
     * @return ResolvedAudioInfo with all available metadata, or null if resolution fails
     */
    suspend operator fun invoke(uri: Uri): ResolvedAudioInfo? {
        Timber.tag("DEBUG_FLOW").d("ResolveSharedAudioUriUseCase: Resolving URI: $uri")
        
        return try {
            // Resolve URI to file path
            val resolvedPath = contentResolverRepository.resolveUriToPath(uri)
            Timber.tag("DEBUG_FLOW").d("ResolveSharedAudioUriUseCase: Resolved path: $resolvedPath")
            
            // Get display name
            val displayName = contentResolverRepository.getDisplayName(uri)
            Timber.tag("DEBUG_FLOW").d("ResolveSharedAudioUriUseCase: Display name: $displayName")
            
            // Extract filename from various sources
            val fileName = extractFileName(resolvedPath, displayName, uri)
            Timber.tag("DEBUG_FLOW").d("ResolveSharedAudioUriUseCase: Extracted filename: $fileName")
            
            val resolvedInfo = ResolvedAudioInfo(
                originalUri = uri,
                resolvedPath = resolvedPath,
                displayName = displayName,
                fileName = fileName
            )
            
            if (resolvedInfo.hasMatchableData()) {
                Timber.tag("DEBUG_FLOW").d("ResolveSharedAudioUriUseCase: Successfully resolved URI with matchable data")
                resolvedInfo
            } else {
                Timber.tag("DEBUG_FLOW").w("ResolveSharedAudioUriUseCase: No matchable data found for URI")
                null
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ResolveSharedAudioUriUseCase: Failed to resolve URI")
            null
        }
    }
    
    /**
     * Extracts filename from various sources in order of preference
     */
    private fun extractFileName(resolvedPath: String?, displayName: String?, uri: Uri): String? {
        // Try resolved path first
        resolvedPath?.let { path ->
            val fileName = path.substringAfterLast('/')
            if (fileName.isNotBlank() && fileName != path) {
                return fileName
            }
        }
        
        // Try display name
        displayName?.let { name ->
            if (name.isNotBlank()) {
                return name
            }
        }
        
        // Try URI last path segment
        uri.lastPathSegment?.let { segment ->
            if (segment.isNotBlank()) {
                return segment
            }
        }
        
        return null
    }
}