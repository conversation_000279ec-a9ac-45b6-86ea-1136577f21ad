package soly.lyricsgenerator.domain.usecase.database

import soly.lyricsgenerator.domain.repository.FilesRepository // Assuming this repository exists
import timber.log.Timber // Added Timber import
import javax.inject.Inject

class GetFileContentUseCase @Inject constructor(
    private val filesRepository: FilesRepository // Inject the repository
) {
    /**
     * Retrieves the content of a file specified by its ID.
     *
     * @param fileId The ID of the file to retrieve content for.
     * @return The file content as a String, or null if an error occurs or the file isn't found.
     */
    suspend operator fun invoke(fileId: Long): String? {
        Timber.tag(TAG).d("invoke: Called with fileId: $fileId") // Log entry
        return try {
            filesRepository.getFileContentById(fileId).also {
                if (it != null) {
                    Timber.tag(TAG).d("invoke: Successfully retrieved content for fileId: $fileId") // Log success
                } else {
                    Timber.tag(TAG).w("invoke: No content found for fileId: $fileId") // Log null return
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "invoke: Error getting content for fileId: $fileId") // Log error
            null
        }
    }

    companion object {
        private const val TAG = "DEBUG_FLOW:GetFileContentUC" // Add class-specific tag prefix
    }
} 