package soly.lyricsgenerator.domain.lrc

import Tuple

/**
 * Utility class for building LRC file content
 */
object LrcContentBuilder {
    /**
     * Builds LRC content from a map of line indices, timestamps and text
     * @param lrcKeyValuePairs Map of line indices to timestamp and text tuples
     * @return Formatted LRC content as a string
     */
    fun buildLrcContent(lrcKeyValuePairs: Map<Int, Tuple<Long, String>>): String {
        val stringBuilder = StringBuilder()
        lrcKeyValuePairs.entries.forEachIndexed { index, entry ->
            val (_, tuple) = entry
            val (time, line) = tuple
            if (index == 0 || time != 0L) {
                val minutes = (time / 60000).toString().padStart(2, '0')
                val seconds = ((time % 60000) / 1000).toString().padStart(2, '0')
                val milliseconds = ((time % 1000) / 10).toString().padStart(2, '0')
                stringBuilder.append("[$minutes:$seconds.$milliseconds]$line\n")
            }
        }
        return stringBuilder.toString()
    }
} 