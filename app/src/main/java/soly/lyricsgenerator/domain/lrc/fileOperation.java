package soly.lyricsgenerator.domain.lrc;

import android.os.Environment;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;

public class fileOperation {
    public static ArrayList<String> fileToLines(File file) throws IOException {
        ArrayList<String> lines = new ArrayList<>();
        try {
            FileReader fr = new FileReader(file.getPath());
            BufferedReader br = new BufferedReader(fr);
            while (br.ready()) lines.add(br.readLine());
            br.close(); fr.close();
        } catch (IOException e) { System.out.println("File open fail."); throw e;}
        return lines;
    }
}