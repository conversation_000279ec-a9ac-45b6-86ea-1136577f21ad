package soly.lyricsgenerator.domain.lrc;

import java.util.Map;
import java.util.TreeMap;
import java.util.Vector;

public class LRC {
    final static public String[] metasName = {"id", "ar", "ti", "al", "by"};
    public String[] metas = new String[metasName.length];

    public MSms length = new MSms();
    public int offset = 0;
    public TreeMap<Integer, String> Lines = new TreeMap<>();

    public LRC(){}
    public LRC(TreeMap<Integer, String> Lines) {
        this.Lines = Lines;
    }

    public void setID(String id, String str) {
        for (int i = 0; i<metasName.length; i++)
            if (id.matches(metasName[i]) == true) metas[i] = str;
    }

    public String getID(String id) {
        for (int i = 0; i<metasName.length; i++)
            if (id.matches(metasName[i]) == true) return metas[i];
        return null;
    }

    public void Show(){
        for (int i = 0; i<metasName.length; i++)
            System.out.println(metasName[i] + ": " + metas[i]);
        System.out.println("offset" + ": " + offset);

        System.out.println("length" + ": " + length.str());
        for (Map.Entry<Integer, String> entry : Lines.entrySet())
            System.out.println(String.format("%10d", entry.getKey()) + " -> " + entry.getValue());
        System.out.println();
    }


    public String getLine(int millisecond, int offset){
        if (Lines.isEmpty()) return null;
        Vector<Integer> time = new Vector<>(Lines.keySet());
        int LinePtr = 0;
        for (; LinePtr < time.size(); LinePtr++){
            if (LinePtr + 1 >= time.size()) break;
            if ((millisecond > time.get(LinePtr)) && (millisecond < time.get(LinePtr + 1))) break;
        }
        if (LinePtr + offset < 0) return null;
        if (LinePtr + offset >= time.size()) return null;
        return Lines.get(time.get(LinePtr + offset));
    }
}