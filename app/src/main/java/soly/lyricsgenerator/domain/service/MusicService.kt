package soly.lyricsgenerator.domain.service

import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.usecase.database.song.SaveSongsUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadShowFavoritesOnlyUseCase
import timber.log.Timber
import javax.inject.Inject

class MusicService @Inject constructor(
    private val musicRepository: IMusicRepository,
    private val saveSongsUseCase: SaveSongsUseCase
) {
    suspend fun getAllSongs(): List<Song> {
        val songs = musicRepository.getAllSongs()
        
        if (songs.isNotEmpty()) {
            saveSongsUseCase(songs)
        }
        return songs
    }

    suspend fun getSongsByArtist(artist: String): List<Song> {
        return musicRepository.getSongsByArtist(artist)
    }
}