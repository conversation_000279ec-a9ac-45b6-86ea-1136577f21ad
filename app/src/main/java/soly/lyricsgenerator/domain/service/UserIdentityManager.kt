package soly.lyricsgenerator.domain.service

import android.content.Context
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.amplitude.android.Amplitude
import com.amplitude.android.Configuration
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.BuildConfig
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.AnalyticsUtils
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import soly.lyricsgenerator.analytics.UserIdentityConstants
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserIdentityManager handles the generation, encrypted storage, and propagation
 * of a unique UUID identifier for each app installation.
 * 
 * This UUID is used to unify user identification across Firebase Analytics,
 * Firebase Crashlytics, and Amplitude without requiring a login system.
 * 
 * Following Clean Architecture principles and MVVM pattern.
 */
@Singleton
class UserIdentityManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseAnalytics: FirebaseAnalytics
) {
    
    private var _installationId: String? = null
    private var amplitude: Amplitude? = null
    
    /**
     * Gets the Amplitude instance after initialization
     * Returns null if not initialized or initialization failed
     */
    val amplitudeInstance: Amplitude?
        get() = amplitude
    
    /**
     * Gets the installation ID after initialization
     * Throws IllegalStateException if accessed before initialization
     */
    val installationId: String
        get() = _installationId ?: throw IllegalStateException("installationId accessed before UserIdentityManager was initialized or initialization failed.")
    
    /**
     * Initializes the UserIdentityManager and sets up all analytics SDKs
     * Should be called from Application.onCreate()
     */
    suspend fun initialize() {
        withContext(Dispatchers.IO) {
            try {
                // Load or generate installation ID
                val id = getOrGenerateInstallationId()
                _installationId = id
                
                // Initialize Amplitude
                initializeAmplitude()
                
                // Propagate UUID to all analytics platforms
                propagateUserIdToAllPlatforms(id)
                
                // Set up global analytics hook
                setupAnalyticsHook()
                
                // Log successful initialization
                logAnalyticsEvent(AnalyticsConstants.Events.USER_ID_LOADED, id, "initialization")
                
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Initialized with UUID: $id")
                
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Initialization failed: ${e.message}")
                logAnalyticsError("UserIdentityManager initialization failed", e)
            }
        }
    }
    
    /**
     * Gets existing installation ID or generates a new one
     */
    private suspend fun getOrGenerateInstallationId(): String {
        return withContext(Dispatchers.IO) {
            try {
                // Try to load existing UUID from encrypted storage
                val existingId = getStoredInstallationId()
                if (existingId != null) {
                    Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Loaded existing UUID from storage")
                    return@withContext existingId
                }
                
                // Generate new UUID if none exists
                val newId = generateAndStoreInstallationId()
                logAnalyticsEvent(AnalyticsConstants.Events.USER_ID_GENERATED, newId, "first_launch")
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Generated new UUID for first launch")
                
                return@withContext newId
                
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error getting/generating installation ID: ${e.message}")
                // Fallback: generate UUID without storage (will be different on each app restart)
                return@withContext UUID.randomUUID().toString()
            }
        }
    }
    
    /**
     * Generates a new UUID and stores it in encrypted preferences
     */
    private fun generateAndStoreInstallationId(): String {
        try {
            val uuid = UUID.randomUUID().toString()
            storeInstallationId(uuid)
            return uuid
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error generating/storing UUID: ${e.message}")
            throw e
        }
    }
    
    /**
     * Retrieves the stored installation ID from encrypted preferences
     */
    private fun getStoredInstallationId(): String? {
        return try {
            val encryptedPrefs = getEncryptedSharedPreferences()
            encryptedPrefs.getString(UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY, null)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error retrieving stored UUID: ${e.message}")
            null
        }
    }
    
    /**
     * Stores the installation ID in encrypted preferences
     */
    private fun storeInstallationId(id: String) {
        try {
            val encryptedPrefs = getEncryptedSharedPreferences()
            encryptedPrefs.edit()
                .putString(UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY, id)
                .apply()
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Stored UUID in encrypted preferences")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error storing UUID: ${e.message}")
            throw e
        }
    }
    
    /**
     * Creates or gets the encrypted shared preferences instance
     */
    private fun getEncryptedSharedPreferences(): android.content.SharedPreferences {
        try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()
            
            return EncryptedSharedPreferences.create(
                context,
                UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error creating encrypted preferences: ${e.message}")
            throw e
        }
    }
    
    /**
     * Initializes Amplitude SDK
     * Disabled in debug builds
     */
    private fun initializeAmplitude() {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Amplitude disabled for debug build")
                return
            }
            val config = Configuration(
                apiKey = BuildConfig.AMPLITUDE_API_KEY,
                context = context
            )
            amplitude = Amplitude(config)
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Amplitude initialized")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error initializing Amplitude: ${e.message}")
        }
    }
    
    /**
     * Propagates the user ID to all analytics platforms
     */
    private fun propagateUserIdToAllPlatforms(userId: String) {
        // Firebase Analytics
        propagateToFirebaseAnalytics(userId)
        
        // Firebase Crashlytics
        propagateToFirebaseCrashlytics(userId)
        
        // Amplitude
        propagateToAmplitude(userId)
    }
    
    /**
     * Sets user ID in Firebase Analytics
     * Disabled in debug builds
     */
    private fun propagateToFirebaseAnalytics(userId: String) {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Firebase Analytics user ID setting skipped in debug build")
                return
            }
            
            firebaseAnalytics.setUserId(userId)
            logAnalyticsEvent(
                AnalyticsConstants.Events.USER_ID_PROPAGATED, 
                userId, 
                UserIdentityConstants.AnalyticsPlatforms.FIREBASE_ANALYTICS
            )
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Set user ID in Firebase Analytics")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error setting Firebase Analytics user ID: ${e.message}")
            logAnalyticsError(UserIdentityConstants.ErrorMessages.ANALYTICS_PROPAGATION_FAILED, e)
        }
    }
    
    /**
     * Sets user ID in Firebase Crashlytics
     */
    private fun propagateToFirebaseCrashlytics(userId: String) {
        try {
            FirebaseCrashlytics.getInstance().setUserId(userId)
            logAnalyticsEvent(
                AnalyticsConstants.Events.USER_ID_PROPAGATED, 
                userId, 
                UserIdentityConstants.AnalyticsPlatforms.FIREBASE_CRASHLYTICS
            )
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Set user ID in Firebase Crashlytics")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error setting Firebase Crashlytics user ID: ${e.message}")
            logAnalyticsError(UserIdentityConstants.ErrorMessages.ANALYTICS_PROPAGATION_FAILED, e)
        }
    }
    
    /**
     * Sets user ID in Amplitude
     * Disabled in debug builds
     */
    private fun propagateToAmplitude(userId: String) {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Amplitude user ID setting skipped in debug build")
                return
            }
            
            amplitude?.setUserId(userId)
            logAnalyticsEvent(
                AnalyticsConstants.Events.USER_ID_PROPAGATED, 
                userId, 
                UserIdentityConstants.AnalyticsPlatforms.AMPLITUDE
            )
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Set user ID in Amplitude")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error setting Amplitude user ID: ${e.message}")
            logAnalyticsError(UserIdentityConstants.ErrorMessages.ANALYTICS_PROPAGATION_FAILED, e)
        }
    }
    
    /**
     * Logs analytics events using the centralized constants pattern
     * This logs to both Firebase Analytics and Amplitude
     * Disabled in debug builds
     */
    private fun logAnalyticsEvent(eventName: String, userId: String, platform: String) {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Analytics event logging skipped in debug build: $eventName")
                return
            }
            
            val bundle = android.os.Bundle()
            bundle.putString(AnalyticsConstants.Params.USER_ID, userId)
            bundle.putString(AnalyticsConstants.Params.ANALYTICS_PLATFORM, platform)
            
            // Log to Firebase Analytics
            firebaseAnalytics.logEvent(eventName, bundle)
            
            // Also log to Amplitude if available
            amplitude?.track(eventName, mapOf(
                AnalyticsConstants.Params.USER_ID to userId,
                AnalyticsConstants.Params.ANALYTICS_PLATFORM to platform
            ))
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error logging analytics event: ${e.message}")
        }
    }
    
    /**
     * Sets up a global analytics hook to intercept Firebase Analytics events and forward them to Amplitude
     */
    private fun setupAnalyticsHook() {
        try {
            // Set up the global analytics hook with both analytics instances
            GlobalAnalyticsHook.setAmplitudeInstance(amplitude)
            GlobalAnalyticsHook.setFirebaseAnalyticsInstance(firebaseAnalytics)
            Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Global analytics hook setup complete")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error setting up analytics hook: ${e.message}")
        }
    }
    
    /**
     * Logs analytics errors using the centralized constants pattern
     * Disabled in debug builds
     */
    private fun logAnalyticsError(errorMessage: String, exception: Exception) {
        try {
            if (BuildConfig.DEBUG) {
                Timber.tag("DEBUG_FLOW").d("UserIdentityManager: Analytics error logging skipped in debug build: $errorMessage")
                return
            }
            
            val bundle = android.os.Bundle()
            bundle.putString(AnalyticsConstants.Params.ERROR_MESSAGE, errorMessage)
            bundle.putString(AnalyticsConstants.Params.MESSAGE, exception.message ?: "Unknown error")
            firebaseAnalytics.logEvent("user_identity_error", bundle)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e("UserIdentityManager: Error logging analytics error: ${e.message}")
        }
    }
}

