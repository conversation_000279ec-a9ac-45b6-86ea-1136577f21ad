package soly.lyricsgenerator.domain.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.BroadcastReceiver
import android.content.IntentFilter
import soly.lyricsgenerator.domain.constants.ServiceConstants
import android.os.IBinder
import androidx.core.app.NotificationCompat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.MusicPlayer
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.model.SortOrder
import soly.lyricsgenerator.domain.usecase.MusicUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadSortPreferencesUseCase
import soly.lyricsgenerator.domain.usecase.preferences.SaveSortPreferencesUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadShuffleEnabledUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadShowFavoritesOnlyUseCase
import soly.lyricsgenerator.domain.usecase.preferences.LoadArtistFilterUseCase
import soly.lyricsgenerator.domain.repository.SongsCache
import soly.lyricsgenerator.ui.navigation.NavRoutes
import soly.lyricsgenerator.ui.viewmodel.PreferenceKeys
import soly.lyricsgenerator.analytics.AnalyticsConstants
import soly.lyricsgenerator.analytics.GlobalAnalyticsHook
import javax.inject.Inject
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaScannerConnection
import android.os.Build
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.PlaybackStateCompat
import android.content.ComponentName
import android.os.Handler
import android.os.Looper
import timber.log.Timber

@AndroidEntryPoint
class MusicPlayerService : Service(), AudioManager.OnAudioFocusChangeListener {
    private var isChannelCreated = false
    private var isForegroundStarted = false

    private lateinit var mediaSession: MediaSessionCompat

    private var songsQueue: List<Song> = listOf()
    private var currentSongIndex: Int = 0
    private var lastPlayedSong: Song? = null

    // Track the current play mode
    private var currentPlayMode: Int = MODE_LISTENING
    
    // Cache shuffle state to avoid suspend calls in non-suspend functions
    private var isShuffleEnabled: Boolean = false

    // Audio Focus members
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false
    private var pausedDueToFocusLoss = false // Track if pause was due to focus loss
    
    // Track playback state for screen transitions
    private var lastPlaybackPosition: Long = 0
    private var wasPlayingBeforeScreenChange: Boolean = false
    private var previousPlayMode: Int = MODE_LISTENING
    
    // Notification dismiss handler
    private var notificationDismissedReceiver: BroadcastReceiver? = null
    
    // Handler for progress updates
    private val progressHandler = Handler(Looper.getMainLooper())
    private val progressUpdateInterval = 1000L // Update every second
    private val progressUpdateRunnable = object : Runnable {
        override fun run() {
            if (musicPlayer.isPlaying()) {
                // Update session with current progress (no need to manually update notification)
                val currentPosition = musicPlayer.getCurrentPosition()
                val currentSong = musicPlayer.getCurrentlyPlayingSong()
                if (currentSong != null) {
                    updatePlaybackState(true, currentPosition)
                }
                // Send current state for UI updates
                sendCurrentState()
            }
            // Schedule next update
            progressHandler.postDelayed(this, progressUpdateInterval)
        }
    }

    @Inject
    lateinit var musicPlayer: MusicPlayer

    @Inject
    lateinit var musicUseCase: MusicUseCase
    
    @Inject
    lateinit var loadSortPreferencesUseCase: LoadSortPreferencesUseCase
    
    @Inject
    lateinit var saveSortPreferencesUseCase: SaveSortPreferencesUseCase
    
    @Inject
    lateinit var loadShuffleEnabledUseCase: LoadShuffleEnabledUseCase
    
    @Inject
    lateinit var loadShowFavoritesOnlyUseCase: LoadShowFavoritesOnlyUseCase
    
    @Inject
    lateinit var loadArtistFilterUseCase: LoadArtistFilterUseCase

    private val serviceScope = CoroutineScope(Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        registerNotificationDismissReceiver()
        mediaSession = MediaSessionCompat(this, "MusicService")
        
        // Set up error handler for MusicPlayer
        musicPlayer.onError = { errorMessage ->
            // Stop the current song and notify user
            stopPlaying(musicPlayer.getCurrentlyPlayingSong())
            // You can also send broadcast to update UI with error
        }
        
        // Add media session callback to handle media button clicks
        mediaSession.setCallback(object : MediaSessionCompat.Callback() {
            override fun onPlay() {
                resumePlayback()
            }

            override fun onPause() {
                pausePlayback(abandonFocus = false)
            }

            override fun onSkipToNext() {
                // Block skip action if in creation mode
                if (currentPlayMode == MODE_CREATION) {
                    return
                }
                playNextSong()
            }

            override fun onSkipToPrevious() {
                // Block skip action if in creation mode
                if (currentPlayMode == MODE_CREATION) {
                    return
                }
                playPreviousSong()
            }

            override fun onStop() {
                stopPlaying(musicPlayer.getCurrentlyPlayingSong())
            }

            override fun onSeekTo(pos: Long) {
                musicPlayer.seekTo(pos)
                sendPlaybackStateBroadcast(musicPlayer.isPlaying())
                sendCurrentState()
            }
        })
        
        mediaSession.isActive = true
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action
        val song = intent?.getSerializableExtra("song") as? Song
        val seekPosition = intent?.getLongExtra(EXTRA_SEEK_POSITION, 0L)
        // Read play mode from intent, default to MODE_LISTENING if not provided or null
        val playMode = intent?.getIntExtra(EXTRA_PLAY_MODE, MODE_LISTENING) ?: MODE_LISTENING
        // NEW: Read source and destination screens
        val sourceScreen = intent?.getStringExtra(EXTRA_SOURCE_SCREEN)
        val destinationScreen = intent?.getStringExtra(EXTRA_DESTINATION_SCREEN)

        // Check if this action was started as a foreground service and ensure we call startForeground if needed
        val requiresForegroundService = when (action) {
            ACTION_PLAY, ACTION_RESUME -> true
            else -> false
        }
        
        // If started as foreground service but not already in foreground, start with a temporary notification
        if (requiresForegroundService && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !isForegroundStarted) {
            // Create a temporary notification to satisfy the foreground service requirement
            val tempNotification = createTemporaryNotification()
            startForeground(NOTIFICATION_ID, tempNotification)
            isForegroundStarted = true
        }

        when (action) {
            ACTION_PLAY -> {
                song?.let {
                    // Log analytics event for service play action
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_PLAY_SONG) {
                        param(AnalyticsConstants.Params.SONG_ID, it.id.toString())
                        param(AnalyticsConstants.Params.SONG_TITLE, it.title ?: AnalyticsConstants.Params.UNKNOWN)
                        param(AnalyticsConstants.Params.PLAY_MODE, if (playMode == MODE_CREATION) "creation" else "listening")
                        param(AnalyticsConstants.Params.SOURCE_SCREEN, sourceScreen ?: AnalyticsConstants.Params.UNKNOWN)
                    }
                    
                    // Set play mode before playing
                    currentPlayMode = playMode // Use the mode from intent (now non-null)
                    playSong(it)
                }
            }

            ACTION_PAUSE -> {
                
                // Log analytics event for service pause action
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_PAUSE_SONG) {}
                
                // Differentiate pause reason
                pausePlayback(abandonFocus = true)
            }

            ACTION_RESUME -> {
                
                // Log analytics event for service resume action
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_RESUME_SONG) {}
                
                // Explicitly update mode if provided
                intent?.getIntExtra(EXTRA_PLAY_MODE, -1)?.takeIf { it != -1 }?.let {
                    currentPlayMode = it
                }
                resumePlayback()
            }

            ACTION_STOP -> {
                
                // Log analytics event for service stop action
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_STOP_SONG) {}
                
                stopPlaying(song)
            }

            ACTION_LOAD_SONGS -> {
                
                // Log analytics event for songs loading
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_LOAD_SONGS) {}
                
                loadSongs()
            }

            ACTION_SEEK_TO -> {
                
                // Log analytics event for seek action
                seekPosition?.let { position ->
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_SEEK_TO) {
                        param(AnalyticsConstants.Params.SEEK_POSITION_MS, position)
                    }
                }
                
                // Explicitly update mode if provided
                 intent?.getIntExtra(EXTRA_PLAY_MODE, -1)?.takeIf { it != -1 }?.let {
                    currentPlayMode = it
                }
                seekPosition?.let {
                    musicPlayer.seekTo(it)
                    sendPlaybackStateBroadcast(musicPlayer.isPlaying())
                    sendCurrentState()
                }
            }

            ACTION_GET_CURRENT_STATE -> {
                sendCurrentState()
            }

            ACTION_PLAY_NEXT -> {
                playNextSong()
            }

            ACTION_PLAY_PREVIOUS -> {
                playPreviousSong()
            }
            
            // NEW: Handle sorting update
            ServiceConstants.Actions.UPDATE_SORTING -> {
                val sortType = intent?.getStringExtra(ServiceConstants.Extras.SORT_TYPE)
                val sortOrder = intent?.getStringExtra(ServiceConstants.Extras.SORT_ORDER)
                
                
                // Log analytics event for sorting change
                GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_SORT_CHANGED) {
                    param(AnalyticsConstants.Params.SORT_TYPE, sortType ?: AnalyticsConstants.Params.UNKNOWN)
                    param(AnalyticsConstants.Params.SORT_ORDER, sortOrder ?: AnalyticsConstants.Params.UNKNOWN)
                }
                
                // Save sort parameters using use case
                sortType?.let { type ->
                    sortOrder?.let { order ->
                        serviceScope.launch {
                            val sortTypeEnum = SortType.fromName(type)
                            val sortOrderEnum = SortOrder.fromName(order)
                            saveSortPreferencesUseCase(sortTypeEnum, sortOrderEnum)
                            
                            // Trigger song list refresh with new sorting
                            loadSongs()
                        }
                    }
                }
            }
            
            // NEW: Handle screen transition
            ACTION_SCREEN_TRANSITION -> {
                handleScreenTransition(sourceScreen, destinationScreen)
            }
            
            null -> {
            }
            
            else -> {
            }
        }

        if (!isChannelCreated) {
            createNotificationChannel()
        }

        return START_NOT_STICKY
    }
    
    // NEW: Method to handle screen transitions
    private fun handleScreenTransition(sourceScreen: String?, destinationScreen: String?) {
        // Check if we're navigating between main tabs
        val isSourceMainTab = sourceScreen == NavRoutes.Music.route || 
                             sourceScreen == NavRoutes.Create.route || 
                             sourceScreen == NavRoutes.Files.route
                             
        val isDestinationMainTab = destinationScreen == NavRoutes.Music.route || 
                                  destinationScreen == NavRoutes.Create.route || 
                                  destinationScreen == NavRoutes.Files.route
        
        if (isSourceMainTab && isDestinationMainTab) {
            // When transitioning between any main tabs
            // Store the current playback state
            lastPlaybackPosition = musicPlayer.getCurrentPosition()
            wasPlayingBeforeScreenChange = musicPlayer.isPlaying()
            previousPlayMode = currentPlayMode

            // When going to Music tab, ensure we're in LISTENING mode
            if (destinationScreen == NavRoutes.Music.route && currentPlayMode != MODE_LISTENING) {
                val currentlyPlayingSong = musicPlayer.getCurrentlyPlayingSong()
                if (currentlyPlayingSong != null && musicPlayer.isPlaying()) {
                    currentPlayMode = MODE_LISTENING
                    // No need to restart playback, just update mode
                }
            }
            
            // When going to Create tab, consider setting mode to CREATION 
            // (but only do this if explicitly requested elsewhere, not automatically)
        } 
        
        // Send current state broadcast to update UI
        sendCurrentState()
    }

    private fun stopPlaying(song: Song?) {
        val currentSong = musicPlayer.getCurrentlyPlayingSong()
        if (currentSong != null) {
            lastPlayedSong = currentSong
        }
        // Abandon audio focus
        abandonAudioFocus()
        hasAudioFocus = false
        pausedDueToFocusLoss = false

        musicPlayer.stop()
        sendPlaybackStateBroadcast(false)
        
        // First update notification to make it dismissible before stopping foreground
        lastPlayedSong?.let {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // Create a dismissible version of the notification
            val dismissibleNotification = createNotification(it).apply {
                flags = flags and Notification.FLAG_ONGOING_EVENT.inv()
            }
            stopForeground(false) // Keep the notification visible
            notificationManager.notify(NOTIFICATION_ID, dismissibleNotification)
            isForegroundStarted = false
        } ?: run {
            // If no last played song, just remove the notification
            stopForeground(true)
        }
        
        // Stop progress updates
        stopProgressUpdates()
    }

    private fun getNextSongIndex(currentIndex: Int): Int {
        if (songsQueue.isEmpty()) {
            return 0
        }
        
        return if (isShuffleEnabled && songsQueue.size > 1) {
            var nextIndex: Int
            do {
                nextIndex = (songsQueue.indices).random()
            } while (nextIndex == currentIndex)
            nextIndex
        } else {
            (currentIndex + 1) % songsQueue.size
        }
    }

    private fun playNextSong() {
        // Block skip action if in creation mode
        if (currentPlayMode == MODE_CREATION) {
            return
        }
        
        // Check if queue is empty
        if (songsQueue.isEmpty()) {
            return
        }
        
        // If skipping while in creation mode, switch to listening mode
        val nextIndex = getNextSongIndex(currentSongIndex)
        
        currentSongIndex = nextIndex
        val nextSong = songsQueue[currentSongIndex]
        
        playSong(nextSong)
    }

    private fun playPreviousSong() {
        // Block skip action if in creation mode
        if (currentPlayMode == MODE_CREATION) {
            return
        }
        
        // Check if queue is empty
        if (songsQueue.isEmpty()) {
            return
        }
        
        // If skipping while in creation mode, switch to listening mode
        currentSongIndex--
        if (currentSongIndex < 0) {
            currentSongIndex = songsQueue.size - 1
        }
        
        val previousSong = songsQueue[currentSongIndex]
        
        playSong(previousSong)
    }

    private fun playSong(song: Song) {
        // Request audio focus before playing
        if (!requestAudioFocus()) {
            return // Don't play if focus isn't granted
        }
        hasAudioFocus = true
        pausedDueToFocusLoss = false

        currentSongIndex = songsQueue.indexOf(song)
        
        // Play the song with error handling
        try {
            musicPlayer.play(song)
            lastPlayedSong = song
            
            // Update metadata and playback state
            updateMetadata(song)
            updatePlaybackState(true, 0)
            
            // Send broadcasts AFTER the song has started playing
            sendSongChangedBroadcast(song)
            sendPlaybackStateBroadcast(true)

            // IMPORTANT: Create and show notification AFTER playback has started
            
            // Use a coroutine to force a small delay to ensure MediaPlayer is properly started
            serviceScope.launch {
                // Small delay to ensure MediaPlayer is properly prepared and playing
                kotlinx.coroutines.delay(100)
                withContext(Dispatchers.Main) {
                    // Double check if we're still playing after the delay
                    ensurePlayerState()
                    
                    // Start progress updates
                    startProgressUpdates()
                }
            }
            
            val notification = createNotification(song)
            startForeground(NOTIFICATION_ID, notification)
            isForegroundStarted = true
        } catch (e: SecurityException) {
            // Handle permission errors gracefully
            
            // Abandon audio focus since we failed to play
            abandonAudioFocus()
            hasAudioFocus = false
            
            // Send error state broadcast
            sendPlaybackErrorBroadcast(song, "Permission denied: Unable to access audio file")
            
            // Try to play next song if available, or stop if this was the only song
            if (songsQueue.size > 1) {
                playNextSong()
            } else {
                stopPlaying(song)
            }
            return
        } catch (e: Exception) {
            // Handle other errors (corrupted files, unsupported formats, etc.)
            
            // Abandon audio focus since we failed to play
            abandonAudioFocus()
            hasAudioFocus = false
            
            // Send error state broadcast
            sendPlaybackErrorBroadcast(song, "Playback error: ${e.message}")
            
            // Try to play next song if available
            if (songsQueue.size > 1) {
                playNextSong()
            } else {
                stopPlaying(song)
            }
            return
        }

        musicPlayer.onSongCompletion = {
            // Check play mode before deciding action on completion
            if (currentPlayMode == MODE_CREATION) {
                musicPlayer.seekTo(0)
                stopPlaying(song) // Keep original behavior for creation mode
                sendCurrentState() // Send state update after stopping
            } else { // MODE_LISTENING (or default)
                currentSongIndex = getNextSongIndex(currentSongIndex)
                if (songsQueue.isNotEmpty()) { // Avoid index issues if queue becomes empty
                     playSong(songsQueue[currentSongIndex])
                } else {
                     stopPlaying(null) // Stop if queue is empty
                }
            }
        }
    }

    private fun createTemporaryNotification(): Notification {
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(getString(R.string.notification_loading_title))
            .setContentText(getString(R.string.notification_loading_text))
            .setSilent(true)
            .setOngoing(false)
            .setShowWhen(false)
            .build()
    }

    private fun createNotification(song: Song?): Notification {
        if (song == null) {
            return NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(getString(R.string.notification_default_title))
                .setContentText(getString(R.string.notification_default_artist))
                .build()
        }

        // Create an explicit intent directly to MainActivity with proper flags
        val uniqueAction = "music_notification_click_${System.currentTimeMillis()}"
        val contentIntent = Intent(applicationContext, Class.forName("$packageName.MainActivity")).apply {
            action = uniqueAction
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or 
                    Intent.FLAG_ACTIVITY_CLEAR_TASK or
                    Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
            putExtra("FROM_NOTIFICATION", true)
            putExtra("TIMESTAMP", System.currentTimeMillis())
            putExtra("FROM_MUSIC_SERVICE", true)
        }
        
        val requestCode = (System.currentTimeMillis() % 10000).toInt()
        val contentPendingIntent = PendingIntent.getActivity(
            applicationContext,
            requestCode,
            contentIntent,
            PendingIntent.FLAG_CANCEL_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Create intent for notification dismissal
        val dismissedIntent = Intent(ACTION_NOTIFICATION_DISMISSED).apply {
            setPackage(packageName)
        }
        val dismissedPendingIntent = PendingIntent.getBroadcast(
            this,
            4,
            dismissedIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Create component name for service actions
        val componentName = ComponentName(this, MusicPlayerService::class.java)
        
        // Create action PendingIntents
        val prevIntent = PendingIntent.getService(
            this, 1001,
            Intent(ACTION_PLAY_PREVIOUS).apply { setComponent(componentName) },
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val pauseIntent = PendingIntent.getService(
            this, 1002,
            Intent(ACTION_PAUSE).apply { setComponent(componentName) },
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val playIntent = PendingIntent.getService(
            this, 1003,
            Intent(ACTION_RESUME).apply { setComponent(componentName) },
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val nextIntent = PendingIntent.getService(
            this, 1004,
            Intent(ACTION_PLAY_NEXT).apply { setComponent(componentName) },
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Determine which play control to show based on current playback state
        val isPlaying = musicPlayer.isPlaying()
        val playPauseAction = if (isPlaying) {
            NotificationCompat.Action(R.drawable.ic_pause, getString(R.string.pause), pauseIntent)
        } else {
            NotificationCompat.Action(R.drawable.ic_play, getString(R.string.play), playIntent)
        }
        
        // Create the MediaStyle
        val mediaStyle = androidx.media.app.NotificationCompat.MediaStyle()
            .setMediaSession(mediaSession.sessionToken)
            .setShowActionsInCompactView(0, 1, 2) // Show prev, play/pause, next buttons
        
        // Build notification with MediaStyle
        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(song.title)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(contentPendingIntent)
            .setDeleteIntent(dismissedPendingIntent)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setStyle(mediaStyle)
            .setOnlyAlertOnce(true)
            .setSilent(true) // Explicitly set notification to be silent
            .setOngoing(isPlaying) // This makes the notification swipeable when not playing
            .setColorized(true)
            .setColor(getColor(R.color.primary))
            .setShowWhen(false)
            .addAction(R.drawable.ic_skip_previous, getString(R.string.previous), prevIntent)
            .addAction(playPauseAction)
            .addAction(R.drawable.ic_skip_next, getString(R.string.next), nextIntent)
        
        // Only set content text if artist is not "<unknown>"
        if (song.artist != "<unknown>") {
            builder.setContentText(song.artist)
        }
        
        return builder.build()
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            getString(R.string.notification_channel_name),
            NotificationManager.IMPORTANCE_LOW // Use LOW importance to avoid sound
        )
        channel.description = "Music playback controls"
        channel.enableLights(false)
        channel.enableVibration(false)
        channel.setSound(null, null) // Disable notification sound
        channel.setShowBadge(true)
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        
        val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        // Delete old channel if it exists
        try {
            manager.deleteNotificationChannel("MusicPlayerChannel")
        } catch (e: Exception) {
            // Ignore if old channel doesn't exist
        }
        manager.createNotificationChannel(channel)
        isChannelCreated = true
        
    }

    private fun registerNotificationDismissReceiver() {
        notificationDismissedReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {

                // If music is stopped, let the notification be dismissed completely
                if (!musicPlayer.isPlaying()) {
                    stopSelf()
                    return
                }
                
                // Only recreate notification if music is still playing or paused but not stopped
                lastPlayedSong?.let { song ->
                    val notification = createNotification(song)
                    if (musicPlayer.isPlaying()) {
                        startForeground(NOTIFICATION_ID, notification)
                        isForegroundStarted = true
                    } else {
                        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                        notificationManager.notify(NOTIFICATION_ID, notification)
                    }
                }
            }
        }
        
        registerReceiver(
            notificationDismissedReceiver,
            IntentFilter(ACTION_NOTIFICATION_DISMISSED),
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) Context.RECEIVER_NOT_EXPORTED else 0
        )
    }

    private fun loadSongs() {
        serviceScope.launch {
            // Load and cache shuffle state
            isShuffleEnabled = loadShuffleEnabledUseCase()
            
            val allSongs = withContext(Dispatchers.IO) {
                musicUseCase()
            }
            
            
            // If no songs found on any Android version, try rescanning and loading again
            if (allSongs.isEmpty()) {
                triggerMediaScanAndReloadWithRetry()
                return@launch
            }
            
            // Update currently playing song with fresh data if it exists
            val currentSong = musicPlayer.getCurrentlyPlayingSong()
            if (currentSong != null) {
                val updatedCurrentSong = allSongs.find { it.id == currentSong.id }
                if (updatedCurrentSong != null && updatedCurrentSong != currentSong) {
                    // Update the music player with the refreshed song data
                    updateCurrentlyPlayingSongData(updatedCurrentSong)
                }
            }
            
            // Apply sorting to ALL songs first
            val sortedAllSongs = applySorting(allSongs)
            
            // Check favorites filter state
            val favoritesFilterActive = isFavoritesFilterActive()
            
            // Check artist filter state
            val artistFilter = isArtistFilterActive()
            
            // Apply favorites filter to sorted songs for the service queue
            var filteredSongs = if (favoritesFilterActive) {
                sortedAllSongs.filter { it.isFavorite }
            } else {
                sortedAllSongs
            }
            
            // Apply artist filter to the filtered songs
            if (artistFilter != PreferenceKeys.DEFAULT_ARTIST_FILTER) {
                filteredSongs = filteredSongs.filter { it.artist == artistFilter }
            }
            
            songsQueue = filteredSongs
            
            // Update currentSongIndex to match the new sorted position
            val currentlyPlayingSong = musicPlayer.getCurrentlyPlayingSong()
            if (currentlyPlayingSong != null) {
                val newIndex = filteredSongs.indexOfFirst { it.id == currentlyPlayingSong.id }
                if (newIndex != -1) {
                    currentSongIndex = newIndex
                } else {
                    // Currently playing song not found in filtered list (might be due to favorites filter)
                }
            }
            
            // Store filtered songs in cache for UI display (UI no longer needs to filter)
            SongsCache.setSongs(filteredSongs)
            
            // Send broadcast notification without the actual songs data
            sendSongsLoadedNotification(allSongs.size)
        }
    }
    
    private suspend fun isFavoritesFilterActive(): Boolean {
        return loadShowFavoritesOnlyUseCase()
    }
    
    private suspend fun isArtistFilterActive(): String {
        return loadArtistFilterUseCase()
    }
    
    private suspend fun applySorting(songs: List<Song>): List<Song> {
        val sortType = loadSortType()
        val sortOrder = loadSortOrder()
        
        return songs.sortedWith { song1, song2 ->
            sortType.compare(song1, song2, sortOrder)
        }
    }
    
    private suspend fun loadSortType(): SortType {
        return loadSortPreferencesUseCase().sortType
    }
    
    private suspend fun loadSortOrder(): SortOrder {
        return loadSortPreferencesUseCase().sortOrder
    }
    

    private fun sendCurrentState() {
        val currentSong = musicPlayer.getCurrentlyPlayingSong() ?: lastPlayedSong
        val intent = Intent(ACTION_STATE_UPDATED).apply {
            putExtra(EXTRA_CURRENT_SONG, currentSong)
            putExtra(EXTRA_CURRENT_POSITION, musicPlayer.getCurrentPosition())
            putExtra(EXTRA_IS_PLAYING, musicPlayer.isPlaying())
            setPackage(baseContext.packageName)
        }
        baseContext.sendBroadcast(intent)
    }

    private fun sendSongsBroadcast(songs: List<Song>) {
        // Deprecated: This method caused crashes with large song lists
        // Use sendSongsLoadedNotification instead
        sendSongsLoadedNotification(songs.size)
    }
    
    private fun sendSongsLoadedNotification(songCount: Int) {
        val intent = Intent(ACTION_SONGS_LOADED).apply {
            putExtra("SONG_COUNT", songCount)
            setPackage(baseContext.packageName)
        }
        baseContext.sendBroadcast(intent)
    }
    
    private fun triggerMediaScanAndReloadWithRetry() {
        serviceScope.launch {
            repeat(3) { attempt ->
                try {
                    
                    // Use MediaScannerConnection for all Android versions
                    scanCommonMusicDirectories()
                    
                    // Wait progressively longer on each attempt
                    val delayTime = (3000 + attempt * 2000).toLong()
                    kotlinx.coroutines.delay(delayTime)
                    
                    // Try loading songs again
                    val allSongs = withContext(Dispatchers.IO) {
                        musicUseCase()
                    }
                    
                    
                    if (allSongs.isNotEmpty()) {
                        // Success! Process songs as normal
                        val filteredSongs = if (isFavoritesFilterActive()) {
                            allSongs.filter { it.isFavorite }
                        } else {
                            allSongs
                        }
                        
                        songsQueue = filteredSongs
                        SongsCache.setSongs(allSongs)
                        sendSongsLoadedNotification(allSongs.size)
                        
                        // Log successful refresh analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MEDIASTORE_REFRESH_ATTEMPTED) {
                            param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                            param(AnalyticsConstants.Params.REFRESH_ATTEMPT_NUMBER, (attempt + 1).toLong())
                            param(AnalyticsConstants.Params.REFRESH_SUCCESS, true)
                            param(AnalyticsConstants.Params.SONGS_COUNT, allSongs.size.toLong())
                        }
                        
                        return@launch
                    } else {
                        // Log failed attempt analytics
                        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MEDIASTORE_REFRESH_ATTEMPTED) {
                            param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                            param(AnalyticsConstants.Params.REFRESH_ATTEMPT_NUMBER, (attempt + 1).toLong())
                            param(AnalyticsConstants.Params.REFRESH_SUCCESS, false)
                            param(AnalyticsConstants.Params.SONGS_COUNT, allSongs.size.toLong())
                        }
                    }
                    
                } catch (e: Exception) {
                    // Production monitoring via analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_ERROR) {
                        param(AnalyticsConstants.Params.ERROR_TYPE, AnalyticsConstants.Values.ERROR_TYPE_MEDIA_SCAN_RETRY_FAILED)
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: AnalyticsConstants.Values.ERROR_MESSAGE_UNKNOWN)
                        param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                        param(AnalyticsConstants.Params.REFRESH_ATTEMPT_NUMBER, (attempt + 1).toLong())
                    }
                    
                    // Development debugging via Timber (also goes to Crashlytics)
                    Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Media scan retry failed on attempt ${attempt + 1}")
                }
            }
            
            // All attempts failed
            sendSongsLoadedNotification(0)
        }
    }
    
    private suspend fun scanCommonMusicDirectories() = withContext(Dispatchers.IO) {
        
        val directoriesToScan = mutableListOf<java.io.File>().apply {
            // Standard music directories
            add(android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC))
            add(android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_DOWNLOADS))
            add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Music"))
            add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Download"))
            
            // Social media app directories
            add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "WhatsApp/Media/WhatsApp Audio"))
            add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Telegram/Telegram Audio"))
            
            // Android 10+ might have additional paths, try common music app directories
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Android/media"))
                add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Documents/Music"))
                add(java.io.File(android.os.Environment.getExternalStorageDirectory(), "Movies"))  // Some apps store audio in Movies
            }
        }
        
        var totalAudioFiles = 0
        var accessibleDirectories = 0
        
        directoriesToScan.forEach { dir ->
            if (dir.exists() && dir.canRead()) {
                accessibleDirectories++
                
                // First try scanning the directory itself
                try {
                    MediaScannerConnection.scanFile(
                        this@MusicPlayerService,
                        arrayOf(dir.absolutePath),
                        null,
                        null
                    )
                } catch (e: SecurityException) {
                    // Production monitoring via analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_ERROR) {
                        param(AnalyticsConstants.Params.ERROR_TYPE, AnalyticsConstants.Values.ERROR_TYPE_DIRECTORY_SCAN_SECURITY_ERROR)
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, AnalyticsConstants.Values.ERROR_MESSAGE_PERMISSION_DENIED)
                        param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                    }
                    
                    // Development debugging via Timber
                    Timber.tag("DEBUG_FLOW").w(e, "MusicPlayerService: Security restriction scanning ${dir.absolutePath}")
                } catch (e: Exception) {
                    // Production monitoring via analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_ERROR) {
                        param(AnalyticsConstants.Params.ERROR_TYPE, AnalyticsConstants.Values.ERROR_TYPE_DIRECTORY_SCAN_UNEXPECTED_ERROR)
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: AnalyticsConstants.Values.ERROR_MESSAGE_UNKNOWN)
                        param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                    }
                    
                    // Development debugging via Timber
                    Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Failed to scan directory: ${dir.absolutePath}")
                }
                
                // Also try to find and scan individual audio files
                try {
                    val audioFiles = findAudioFiles(dir)
                    totalAudioFiles += audioFiles.size
                    if (audioFiles.isNotEmpty()) {
                        
                        // Scan files in batches to avoid overwhelming the MediaScanner
                        audioFiles.chunked(10).forEach { batch ->
                            MediaScannerConnection.scanFile(
                                this@MusicPlayerService,
                                batch.toTypedArray(),
                                null,
                                null
                            )
                            // Small delay between batches
                            kotlinx.coroutines.delay(100)
                        }
                    }
                } catch (e: Exception) {
                    // Production monitoring via analytics
                    GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.SERVICE_ERROR) {
                        param(AnalyticsConstants.Params.ERROR_TYPE, AnalyticsConstants.Values.ERROR_TYPE_AUDIO_FILES_SCAN_ERROR)
                        param(AnalyticsConstants.Params.ERROR_MESSAGE, e.message ?: AnalyticsConstants.Values.ERROR_MESSAGE_UNKNOWN)
                        param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
                    }
                    
                    // Development debugging via Timber
                    Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Failed to find/scan audio files in directory: ${dir.absolutePath}")
                }
            }
        }
        
        // Log MediaStore scan analytics
        GlobalAnalyticsHook.logEvent(AnalyticsConstants.Events.MEDIASTORE_SCAN_TRIGGERED) {
            param(AnalyticsConstants.Params.ANDROID_VERSION, android.os.Build.VERSION.SDK_INT.toLong())
            param(AnalyticsConstants.Params.SCAN_DIRECTORIES_COUNT, accessibleDirectories.toLong())
            param(AnalyticsConstants.Params.AUDIO_FILES_FOUND, totalAudioFiles.toLong())
        }
    }
    
    private fun findAudioFiles(directory: java.io.File): List<String> {
        val audioExtensions = setOf(".mp3", ".m4a", ".wav", ".flac", ".ogg", ".opus", ".aac", ".wma")
        val audioFiles = mutableListOf<String>()
        
        try {
            directory.listFiles()?.forEach { file ->
                when {
                    file.isFile && audioExtensions.any { file.name.endsWith(it, ignoreCase = true) } -> {
                        audioFiles.add(file.absolutePath)
                        if (audioFiles.size >= 50) return audioFiles // Limit to first 50 files
                    }
                    file.isDirectory && !file.name.startsWith(".") && audioFiles.size < 50 -> {
                        // Recursively scan one level deep
                        audioFiles.addAll(findAudioFiles(file))
                    }
                }
            }
        } catch (e: Exception) {
        }
        
        return audioFiles
    }
    
    private fun triggerMediaScanAndReload() {
        serviceScope.launch {
            try {
                // For Android 9 and below, try multiple approaches
                if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P) {
                    // First try: Broadcast to scan entire external storage
                    val scanIntent = Intent("android.intent.action.MEDIA_SCANNER_SCAN_FILE")
                    scanIntent.data = android.net.Uri.parse("file://" + android.os.Environment.getExternalStorageDirectory())
                    sendBroadcast(scanIntent)
                    
                    // Second try: Also send MEDIA_MOUNTED broadcast (works on some devices)
                    val mountIntent = Intent(Intent.ACTION_MEDIA_MOUNTED)
                    mountIntent.data = android.net.Uri.parse("file://" + android.os.Environment.getExternalStorageDirectory())
                    sendBroadcast(mountIntent)
                    
                }
                
                // Wait longer for MediaStore to update on Android 9
                val delayTime = if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P) 4000L else 2000L
                kotlinx.coroutines.delay(delayTime)
                
                // Try loading songs again
                val allSongs = withContext(Dispatchers.IO) {
                    musicUseCase()
                }
                
                
                // If still no songs on Android 9, show a more helpful message
                if (allSongs.isEmpty() && android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.P) {
                    // Note: User may need to restart the device or use a file manager to trigger MediaStore update
                }
                
                // Process songs as normal
                val filteredSongs = if (isFavoritesFilterActive()) {
                    allSongs.filter { it.isFavorite }
                } else {
                    allSongs
                }
                
                songsQueue = filteredSongs
                SongsCache.setSongs(allSongs)
                sendSongsLoadedNotification(allSongs.size)
                
            } catch (e: Exception) {
                // Still send notification even if refresh failed
                sendSongsLoadedNotification(0)
            }
        }
    }

    private fun sendSongChangedBroadcast(song: Song) {
        val intent = Intent(ACTION_SONG_CHANGED).apply {
            putExtra(EXTRA_CURRENT_SONG, song)
            setPackage(baseContext.packageName)
        }
        baseContext.sendBroadcast(intent)
    }

    private fun sendPlaybackStateBroadcast(isPlaying: Boolean) {
        val intent = Intent(ACTION_PLAYBACK_STATE_CHANGED).apply {
            putExtra(EXTRA_IS_PLAYING, isPlaying)
            setPackage(baseContext.packageName)
        }
        baseContext.sendBroadcast(intent)
        
        // Update notification whenever playback state changes
        updateNotification()
    }

    private fun sendPlaybackErrorBroadcast(song: Song, errorMessage: String) {
        val intent = Intent(ACTION_PLAYBACK_ERROR).apply {
            putExtra(EXTRA_CURRENT_SONG, song)
            putExtra(EXTRA_ERROR_MESSAGE, errorMessage)
            setPackage(baseContext.packageName)
        }
        baseContext.sendBroadcast(intent)
    }

    // Add a method to explicitly check if the MediaPlayer is properly prepared and playing
    private fun ensurePlayerState() {
        // Ensure the playback state is synchronized with actual player state
        val isActuallyPlaying = musicPlayer.isPlaying()

        // Force update the notification to match the actual state
        updateNotification()
    }
    
    private fun updateNotification() {
        val currentSong = musicPlayer.getCurrentlyPlayingSong() ?: lastPlayedSong
        currentSong?.let {
            val notification = createNotification(it)
            if (musicPlayer.isPlaying()) {
                startForeground(NOTIFICATION_ID, notification)
                isForegroundStarted = true
            } else {
                // Just update the notification if we're paused
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.notify(NOTIFICATION_ID, notification)
            }
        }
    }

    // Method to start progress updates
    private fun startProgressUpdates() {
        // Remove any existing callbacks to prevent duplicates
        progressHandler.removeCallbacks(progressUpdateRunnable)
        // Start periodic updates
        progressHandler.post(progressUpdateRunnable)
    }
    
    // Method to stop progress updates
    private fun stopProgressUpdates() {
        progressHandler.removeCallbacks(progressUpdateRunnable)
    }

    // --- Playback Control with Audio Focus ---

    private fun pausePlayback(abandonFocus: Boolean) {
        if (musicPlayer.isPlaying()) {
            musicPlayer.pause()
            // Update MediaSession state
            updatePlaybackState(false)
            
            sendPlaybackStateBroadcast(false)
            if (abandonFocus) {
                abandonAudioFocus()
                hasAudioFocus = false
                pausedDueToFocusLoss = false // Reset flag if manually paused

                // Stop progress updates when paused
                stopProgressUpdates()
            } else {
                // Keep focus if pause is transient (e.g., focus loss)
                pausedDueToFocusLoss = true // Set flag if paused due to focus loss
            }
            
            // Update notification
            updateNotification()
        }
    }

    private fun resumePlayback() {
         if (!musicPlayer.isPlaying()) {
            // Request focus again before resuming
             if (!requestAudioFocus()) {
                 return
             }
             hasAudioFocus = true
             pausedDueToFocusLoss = false // Resuming, so reset flag

             val songToResume = musicPlayer.getCurrentlyPlayingSong() ?: lastPlayedSong
             if (songToResume != null) {
                 if (musicPlayer.getCurrentlyPlayingSong() == null && lastPlayedSong != null) {
                     playSong(songToResume) // Need to replay if player was stopped/nulled
                 } else {
                     musicPlayer.resume()
                     
                     // Update MediaSession state
                     updatePlaybackState(true)
                     
                     sendPlaybackStateBroadcast(true)
                     
                     // Start progress updates when resumed
                     startProgressUpdates()
                     
                     // Ensure we have proper notification for resumed playback
                     val notification = createNotification(songToResume)
                     if (!isForegroundStarted) {
                         startForeground(NOTIFICATION_ID, notification)
                         isForegroundStarted = true
                     } else {
                         // Update existing notification
                         updateNotification()
                     }
                 }
             }
         }
    }

    // --- Audio Focus Handling ---

    private fun requestAudioFocus(): Boolean {
        audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
            .setOnAudioFocusChangeListener(this)
            .build()
        val request = audioFocusRequest ?: return false
        val result = audioManager.requestAudioFocus(request)
        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    private fun abandonAudioFocus() {
        audioFocusRequest?.let { audioManager.abandonAudioFocusRequest(it) }
        audioFocusRequest = null
        hasAudioFocus = false
    }

    override fun onAudioFocusChange(focusChange: Int) {
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                // Resume playback only if it was paused due to focus loss
                if (pausedDueToFocusLoss) {
                    resumePlayback()
                }
                // Optional: Restore volume if ducked (not implemented here)
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                // Permanent loss, stop playback and abandon focus
                stopPlaying(musicPlayer.getCurrentlyPlayingSong())
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                // Temporary loss, pause playback but keep focus
                 if (musicPlayer.isPlaying()) {
                     pausePlayback(abandonFocus = false)
                 }
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                // Lower volume requested, pause for simplicity
                 if (musicPlayer.isPlaying()) {
                     pausePlayback(abandonFocus = false)
                 }
            }
        }
    }

    // --- End Audio Focus Handling ---
    
    /**
     * Updates the currently playing song data with fresh metadata from MediaStore.
     * This is called after song refresh to ensure the UI displays updated tag information.
     */
    private fun updateCurrentlyPlayingSongData(updatedSong: Song) {
        musicPlayer.updateCurrentlyPlayingSong(updatedSong)
        // Update notification with new metadata
        updateNotification()
        // Send current state with updated song data
        sendCurrentState()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        Timber.tag("DEBUG_FLOW").d("MusicPlayerService: onTaskRemoved called, isPlaying=${musicPlayer.isPlaying()}")
        
        // ONLY clean up if music is NOT playing
        if (!musicPlayer.isPlaying()) {
            Timber.tag("DEBUG_FLOW").d("MusicPlayerService: Music NOT playing - removing notification and stopping service")
            
            try {
                // 1. Stop any background threads first
                stopProgressUpdates()
                
                // 2. Get notification manager
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                
                // 3. Cancel ALL notifications (before stopForeground)
                notificationManager.cancelAll()
                
                // 4. Stop foreground with notification removal
                if (isForegroundStarted) {
                    stopForeground(true)
                    isForegroundStarted = false
                }
                
                try {
                    Timber.tag("DEBUG_FLOW").d("MusicPlayerService: Deleting notification channel to force cleanup")
                    notificationManager.deleteNotificationChannel(CHANNEL_ID)
                    isChannelCreated = false
                } catch (e: Exception) {
                    Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Error deleting notification channel")
                }

                // 6. Clean up audio focus
                abandonAudioFocus()
                
                // 7. Clean up media session
                mediaSession.isActive = false
                
                // 8. Stop the service
                Timber.tag("DEBUG_FLOW").d("MusicPlayerService: Stopping service")
                stopSelf()
                
                // 9. Force stop using stopService as last resort
                try {
                    val serviceIntent = Intent(this, MusicPlayerService::class.java)
                    stopService(serviceIntent)
                } catch (e: Exception) {
                    Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Error force stopping service")
                }
                
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Error in onTaskRemoved cleanup")
                // Force cleanup anyway
                try {
                    stopForeground(true)
                    stopSelf()
                } catch (e2: Exception) {
                    Timber.tag("DEBUG_FLOW").e(e2, "MusicPlayerService: Error in force cleanup")
                }
            }
        } else {
            // Music IS playing - do NOT remove notification
            Timber.tag("DEBUG_FLOW").d("MusicPlayerService: Music IS playing - keeping notification and service active")
        }
        
        // Call super.onTaskRemoved at the end
        super.onTaskRemoved(rootIntent)
    }

    override fun onDestroy() {
        Timber.tag("DEBUG_FLOW").d("MusicPlayerService: onDestroy() called")
        
        // Unregister the notification dismissed receiver
        notificationDismissedReceiver?.let {
            try {
                unregisterReceiver(it)
                notificationDismissedReceiver = null
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Error unregistering notification receiver")
            }
        }
        
        // Stop progress updates handler
        stopProgressUpdates()
        
        // Ensure resources are released
        stopPlaying(null)
        
        // Release MediaSession to prevent resource leak
        try {
            mediaSession.release()
            Timber.tag("DEBUG_FLOW").d("MusicPlayerService: MediaSession released")
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "MusicPlayerService: Error releasing MediaSession")
        }
        
        super.onDestroy()
    }

    // Add new methods for MediaSession handling
    private fun updatePlaybackState(isPlaying: Boolean, position: Long = musicPlayer.getCurrentPosition()) {
        val playbackSpeed = if (isPlaying) 1f else 0f
        val stateBuilder = PlaybackStateCompat.Builder()
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                PlaybackStateCompat.ACTION_PAUSE or
                PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS or
                PlaybackStateCompat.ACTION_SEEK_TO
            )
            .setState(
                if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED,
                position,
                playbackSpeed
            )
        mediaSession.setPlaybackState(stateBuilder.build())
        
        // Reduced frequent logging
    }
    
    private fun updateMetadata(song: Song) {
        val metadataBuilder = MediaMetadataCompat.Builder()
            .putString(MediaMetadataCompat.METADATA_KEY_TITLE, song.title)
            .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, song.duration)
            
        // Only add artist metadata if it's not "<unknown>"
        if (song.artist != "<unknown>") {
            metadataBuilder.putString(MediaMetadataCompat.METADATA_KEY_ARTIST, song.artist)
        }
        
        mediaSession.setMetadata(metadataBuilder.build())
        
    }

    companion object {
        const val CHANNEL_ID = "MusicPlayerChannelSilent"
        const val NOTIFICATION_ID = 1
        const val ACTION_PLAY = "soly.lyricsgenerator.action.PLAY"
        const val ACTION_PAUSE = "soly.lyricsgenerator.action.PAUSE"
        const val ACTION_RESUME = "soly.lyricsgenerator.action.RESUME"
        const val ACTION_STOP = "soly.lyricsgenerator.action.STOP"
        const val ACTION_LOAD_SONGS = "soly.lyricsgenerator.action.LOAD_SONGS"
        const val ACTION_SONGS_LOADED = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_SONGS_LOADED"
        const val ACTION_SONG_CHANGED = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_SONG_CHANGED"
        const val ACTION_PLAYBACK_STATE_CHANGED = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_PLAYBACK_STATE_CHANGED"
        const val ACTION_SEEK_TO = "soly.lyricsgenerator.action.SEEK_TO"
        const val ACTION_POSITION_UPDATED = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_POSITION_UPDATED"
        const val ACTION_GET_CURRENT_STATE = "soly.lyricsgenerator.action.GET_CURRENT_STATE"
        const val ACTION_STATE_UPDATED = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_STATE_UPDATED"
        const val ACTION_OPEN_APP = "soly.lyricsgenerator.action.OPEN_APP"
        const val ACTION_NOTIFICATION_DISMISSED = "soly.lyricsgenerator.action.NOTIFICATION_DISMISSED"
        const val ACTION_PLAYBACK_ERROR = "soly.lyricsgenerator.domain.service.MusicPlayerService.ACTION_PLAYBACK_ERROR"
        const val EXTRA_SONGS = "EXTRA_SONGS"
        const val EXTRA_CURRENT_SONG = "EXTRA_CURRENT_SONG"
        const val EXTRA_IS_PLAYING = "EXTRA_IS_PLAYING"
        const val EXTRA_CURRENT_POSITION = "EXTRA_CURRENT_POSITION"
        const val EXTRA_SEEK_POSITION = "EXTRA_SEEK_POSITION"
        const val EXTRA_ERROR_MESSAGE = "EXTRA_ERROR_MESSAGE"
        const val ACTION_PLAY_NEXT = "soly.lyricsgenerator.action.PLAY_NEXT"
        const val ACTION_PLAY_PREVIOUS = "soly.lyricsgenerator.action.PLAY_PREVIOUS"

        // Constants for play mode
        const val EXTRA_PLAY_MODE = "EXTRA_PLAY_MODE"
        const val MODE_LISTENING = 0
        const val MODE_CREATION = 1
        
        // NEW: Screen transition handling
        const val ACTION_SCREEN_TRANSITION = "soly.lyricsgenerator.action.SCREEN_TRANSITION"
        const val EXTRA_SOURCE_SCREEN = "EXTRA_SOURCE_SCREEN"
        const val EXTRA_DESTINATION_SCREEN = "EXTRA_DESTINATION_SCREEN"
        
        // NEW: Sorting functionality
        // Service action and extra constants moved to ServiceConstants

        fun startService(
            context: Context,
            action: String,
            song: Song? = null,
            seekPosition: Long? = null,
            navRoute: NavRoutes? = null,
            playMode: Int? = null, // Made optional, default handled in onStartCommand or remains unchanged
            sourceScreen: String? = null,
            destinationScreen: String? = null,
            sortType: String? = null,
            sortOrder: String? = null
        ) {
            val intent = Intent(context, MusicPlayerService::class.java).apply {
                this.action = action
                song?.let { putExtra("song", it) } // Use let for safety
                seekPosition?.let { putExtra(EXTRA_SEEK_POSITION, it) }
                // Always add playMode if provided, regardless of action
                playMode?.let { putExtra(EXTRA_PLAY_MODE, it) }
                // Add screen transition info if provided
                sourceScreen?.let { putExtra(EXTRA_SOURCE_SCREEN, it) } 
                destinationScreen?.let { putExtra(EXTRA_DESTINATION_SCREEN, it) }
                // Add sorting info if provided
                sortType?.let { putExtra(ServiceConstants.Extras.SORT_TYPE, it) }
                sortOrder?.let { putExtra(ServiceConstants.Extras.SORT_ORDER, it) }
            }
            
            // Determine if this action requires a foreground service
            val requiresForegroundService = when (action) {
                ACTION_PLAY, ACTION_RESUME -> true
                else -> false
            }
            
            try {
                if (requiresForegroundService && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // For Android 12+ and actions that need foreground service
                    context.startForegroundService(intent)
                } else {
                    // For other actions or older Android versions
                    context.startService(intent)
                }
            } catch (e: IllegalStateException) {
                // Handle BackgroundServiceStartNotAllowedException and other service start failures
                
                if (requiresForegroundService) {
                    // For critical actions that require the service, log error but don't crash
                } else {
                    // For non-critical actions, just log and continue
                }
            } catch (e: Exception) {
                // Handle any other service start exceptions
            }
        }
    }
}
