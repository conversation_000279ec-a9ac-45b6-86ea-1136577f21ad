package soly.lyricsgenerator

import android.app.Application
import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import soly.lyricsgenerator.domain.repository.SongsCache
import soly.lyricsgenerator.domain.service.UserIdentityManager
import timber.log.Timber
import javax.inject.Inject

@HiltAndroidApp
class MyApplication : Application() {
    
    @Inject
    lateinit var userIdentityManager: UserIdentityManager
    
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Firebase
        FirebaseApp.initializeApp(this)
        
        // Initialize Crashlytics
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)
        
        // Disable analytics collection in debug builds
        if (BuildConfig.DEBUG) {
            com.google.firebase.analytics.FirebaseAnalytics.getInstance(this)
                .setAnalyticsCollectionEnabled(false)
            Timber.tag("DEBUG_FLOW").d("MyApplication: Firebase Analytics disabled for debug build")
        }
        
        // Set up conditional logging based on build type
        if (BuildConfig.DEBUG) {
            // Plant debug tree only in debug builds
            Timber.plant(Timber.DebugTree())
        } else {
            // Plant production tree that only logs warnings and errors to Crashlytics
            Timber.plant(object : Timber.Tree() {
                override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                    // Only log warnings and errors in production
                    if (priority >= Log.WARN) {
                        // Log the message to Crashlytics
                        FirebaseCrashlytics.getInstance().log("$tag: $message")
                        
                        // If there's an exception, record it
                        if (t != null) {
                            FirebaseCrashlytics.getInstance().recordException(t)
                        }
                    }
                }
            })
        }
        
        // Initialize UserIdentityManager for analytics UUID management
        // This must be done before any analytics events are logged
        applicationScope.launch {
            try {
                userIdentityManager.initialize()
                Timber.tag("DEBUG_FLOW").d("MyApplication: UserIdentityManager initialized successfully")
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e(e, "MyApplication: UserIdentityManager initialization failed")
            }
        }
    }
    
    override fun onTerminate() {
        super.onTerminate()
        // Clear songs cache when app terminates to free memory
        SongsCache.clear()
        Timber.tag("DEBUG_FLOW").d("MyApplication: Cleared songs cache on app termination")
    }
} 