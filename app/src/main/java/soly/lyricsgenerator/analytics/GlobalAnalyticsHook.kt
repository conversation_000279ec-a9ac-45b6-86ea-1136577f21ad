package soly.lyricsgenerator.analytics

import com.amplitude.android.Amplitude

/**
 * Global analytics hook that provides unified analytics logging for both
 * Firebase Analytics and Amplitude with a single function call.
 * 
 * Usage: Replace all Firebase Analytics + Amplitude dual logging with:
 * GlobalAnalyticsHook.logEvent("event_name") {
 *     param("key", "value")
 *     param("number", 123)
 * }
 * 
 * This automatically logs to both Firebase Analytics and Amplitude.
 */
object GlobalAnalyticsHook {
    @JvmStatic
    var amplitude: Amplitude? = null
        private set
    
    @JvmStatic
    var firebaseAnalytics: com.google.firebase.analytics.FirebaseAnalytics? = null
        private set
    
    /**
     * Internal method for UserIdentityManager to set analytics instances
     */
    fun setAmplitudeInstance(amplitudeInstance: Amplitude?) {
        amplitude = amplitudeInstance
    }
    
    /**
     * Internal method to set Firebase Analytics instance
     */
    fun setFirebaseAnalyticsInstance(firebaseInstance: com.google.firebase.analytics.FirebaseAnalytics?) {
        firebaseAnalytics = firebaseInstance
    }
    
    /**
     * Logs an event to both Firebase Analytics and Amplitude with a single call.
     * This replaces the need for separate Firebase + Amplitude logging calls.
     * Disabled in debug builds.
     * 
     * @param eventName Event name
     * @param block Lambda to configure event parameters
     */
    fun logEvent(eventName: String, block: EventBuilder.() -> Unit = {}) {
        try {
            if (soly.lyricsgenerator.BuildConfig.DEBUG) {
                // Skip analytics in debug builds
                return
            }
            
            val builder = EventBuilder()
            builder.block()
            
            // Log to Firebase Analytics
            firebaseAnalytics?.logEvent(eventName, builder.toBundle())
            
            // Log to Amplitude  
            amplitude?.let { amp ->
                val eventProperties = builder.toMap()
                amp.track(eventName, eventProperties)
            }
        } catch (e: Exception) {
            // Silently fail to not disrupt app flow
        }
    }
    
    /**
     * Legacy method for backward compatibility.
     * Use logEvent() instead for new code.
     * Disabled in debug builds.
     */
    fun logToAmplitude(eventName: String, parameters: android.os.Bundle?) {
        try {
            if (soly.lyricsgenerator.BuildConfig.DEBUG) {
                // Skip analytics in debug builds
                return
            }
            
            amplitude?.let { amp ->
                val eventProperties = AnalyticsUtils.bundleToMap(parameters)
                amp.track(eventName, eventProperties)
            }
        } catch (e: Exception) {
            // Silently fail to not disrupt the main analytics flow
        }
    }
}

/**
 * Builder class for creating event parameters
 */
class EventBuilder {
    private val bundle = android.os.Bundle()
    private val map = mutableMapOf<String, Any>()
    
    fun param(key: String, value: String) {
        bundle.putString(key, value)
        map[key] = value
    }
    
    fun param(key: String, value: Long) {
        bundle.putLong(key, value)
        map[key] = value
    }
    
    fun param(key: String, value: Int) {
        bundle.putInt(key, value)
        map[key] = value
    }
    
    fun param(key: String, value: Double) {
        bundle.putDouble(key, value)
        map[key] = value
    }
    
    fun param(key: String, value: Boolean) {
        bundle.putBoolean(key, value)
        map[key] = value
    }
    
    fun toBundle(): android.os.Bundle = bundle
    fun toMap(): Map<String, Any> = map
}