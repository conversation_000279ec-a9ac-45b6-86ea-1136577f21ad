package soly.lyricsgenerator.analytics

/**
 * Constants for Firebase Analytics events and parameters.
 * This centralizes all analytics-related strings to prevent hardcoding and ensure consistency.
 */
object AnalyticsConstants {

    /**
     * Firebase Analytics Event Names
     */
    object Events {
        // Navigation and UI Events
        const val BOTTOM_NAV_ITEM_SELECTED = "bottom_nav_item_selected"
        const val SEARCH_STARTED = "search_started"
        
        // Creation Screen Events
        const val CREATION_SONG_SELECTED = "creation_song_selected"
        const val CREATION_PLAYBACK_PLAY = "creation_playback_play"
        const val CREATION_PLAYBACK_PAUSE = "creation_playback_pause"
        const val CREATION_PLAYBACK_RESUME = "creation_playback_resume"
        const val CREATION_PLAYBACK_SEEK = "creation_playback_seek"
        const val CREATION_PLAYBACK_STOP = "creation_playback_stop"
        const val CREATION_LYRIC_TIMESTAMP_UPDATED = "creation_lyric_timestamp_updated"
        const val CREATION_PREVIEW_TOGGLED = "creation_preview_toggled"
        const val CREATION_LRC_SAVE_STARTED = "creation_lrc_save_started"
        const val CREATION_LRC_SAVE_SUCCEEDED = "creation_lrc_save_succeeded"
        const val CREATION_LRC_SAVE_FAILED = "creation_lrc_save_failed"
        const val CREATION_STATE_CLEARED = "creation_state_cleared"
        const val CREATION_LYRICS_PASTED_SAVED = "creation_lyrics_pasted_saved"
        const val CREATION_LYRICS_ADDED_FROM_FILE = "creation_lyrics_added_from_file"
        
        // Transcription Events
        const val TRANSCRIPTION_STARTED = "transcription_started"
        const val TRANSCRIPTION_SUCCEEDED = "transcription_succeeded"
        const val TRANSCRIPTION_FAILED = "transcription_failed"
        
        // Song Details Screen Events
        const val DETAILS_PERMISSION_RESULT = "details_permission_result"
        const val DETAILS_EXPORT_VIDEO_CLICK = "details_export_video_click"
        const val DETAILS_SNACKBAR_SHOWN = "details_snackbar_shown"
        const val DETAILS_LRC_FILE_SAVED = "details_lrc_file_saved"
        const val DETAILS_SONG_PLAY_SUCCESS = "details_song_play_success"
        const val DETAILS_SONG_PLAY_FAILURE = "details_song_play_failure"
        const val DETAILS_SEEK_SONG = "details_seek_song"
        const val DETAILS_PAUSE_SONG = "details_pause_song"
        const val DETAILS_PLAY_SONG = "details_play_song"
        const val DETAILS_RESUME_SONG = "details_resume_song"
        const val DETAILS_NEXT_SONG_CLICK = "details_next_song_click"
        const val DETAILS_PREVIOUS_SONG_CLICK = "details_previous_song_click"
        const val DETAILS_TOGGLE_SHUFFLE_CLICK = "details_toggle_shuffle_click"
        const val DETAILS_FLOATING_LYRICS_CLICK = "details_floating_lyrics_click"
        
        // Files Screen Events
        const val FILES_ITEM_CLICKED = "files_item_clicked"
        const val FILES_DELETE_OPTION_CLICKED = "files_delete_option_clicked"
        const val FILES_SAVE_OPTION_CLICKED = "files_save_option_clicked"
        const val FILES_RETRY_LOAD_CLICKED = "files_retry_load_clicked"
        const val FILES_FAB_IMPORT_FILE_CLICKED = "files_fab_import_file_clicked"
        const val FILES_FILE_SELECTED = "files_file_selected"
        const val FILES_SONG_SELECTED_FOR_LINKING = "files_song_selected_for_linking"
        const val FILES_FILE_LINKED_SUCCESSFULLY = "files_file_linked_successfully"
        const val FILES_FILE_LINK_FAILED = "files_file_link_failed"
        const val FILE_DELETED = "file_deleted"
        const val FILE_SAVED = "file_saved"
        
        // Music Screen Events
        const val MUSIC_PLAY_SONG = "music_play_song"
        const val MUSIC_PAUSE_SONG = "music_pause_song"
        const val MUSIC_RESUME_SONG = "music_resume_song"
        const val MUSIC_NEXT_SONG_CLICK = "music_next_song_click"
        const val MUSIC_PREVIOUS_SONG_CLICK = "music_previous_song_click"
        const val MUSIC_COMPACT_PLAYER_EXPAND = "music_compact_player_expand"
        const val MUSIC_GRANT_PERMISSION_CLICK = "music_grant_permission_click"
        const val MUSIC_SHUFFLE_TOGGLED = "music_shuffle_toggled"
        const val MUSIC_FAVORITES_FILTER_TOGGLED = "music_favorites_filter_toggled"
        const val MUSIC_SEEK_SONG = "music_seek_song"
        const val MUSIC_FAVORITE_TOGGLED = "music_favorite_toggled"
        const val MUSIC_SCREEN_OPENED = "music_screen_opened"
        const val MUSIC_SONG_INFO_CLICK = "music_song_info_click"
        const val MUSIC_SONG_DETAILS_CLICK = "music_song_details_click"
        const val MUSIC_TAG_EDITOR_CLICK = "music_tag_editor_click"
        
        // Sync Events
        const val SYNC_FLOW_STARTED = "sync_flow_started"
        const val SYNC_MODE_INITIALIZED = "sync_mode_initialized"
        const val SONG_PICKED_FOR_LINKING = "song_picked_for_linking"
        
        // Settings Events
        const val SETTINGS_PRIVACY_POLICY_OPENED = "settings_privacy_policy_opened"
        const val SETTINGS_CONTACT_US_OPENED = "settings_contact_us_opened"
        const val SETTINGS_FAQ_OPENED = "settings_faq_opened"
        const val SETTINGS_FAQ_ITEM_EXPANDED = "settings_faq_item_expanded"
        const val SETTINGS_FAQ_ITEM_COLLAPSED = "settings_faq_item_collapsed"
        const val SETTINGS_CHANGELOG_OPENED = "settings_changelog_opened"
        const val SETTINGS_CHANGELOG_ENTRY_EXPANDED = "settings_changelog_entry_expanded"
        const val SETTINGS_CHANGELOG_ENTRY_COLLAPSED = "settings_changelog_entry_collapsed"
        
        // User Identity Events
        const val USER_ID_GENERATED = "user_id_generated"
        const val USER_ID_LOADED = "user_id_loaded"
        const val USER_ID_PROPAGATED = "user_id_propagated"
        
        // Lyrics Events
        const val LYRICS_LOADED = "lyrics_loaded"
        const val LYRICS_DISPLAY_EMPTY = "lyrics_display_empty"
        const val LYRICS_LINE_CHANGED = "lyrics_line_changed"
        
        // Navigation Events
        const val NAVIGATION_UNSAVED_CHANGES_STATE = "navigation_unsaved_changes_state"
        const val NAVIGATION_SUBFLOW_STATE = "navigation_subflow_state"
        
        // Files Management Events
        const val FILES_REFRESH_TRIGGERED = "files_refresh_triggered"
        const val FILES_IMPORT_TRIGGERED = "files_import_triggered"
        const val SONG_LINKING_INITIATED = "song_linking_initiated"
        
        // Song Details Events
        const val SONG_DETAILS_OVERLAY_PERMISSION_CHECKED = "song_details_overlay_permission_checked"
        
        // Files Screen Additional Events
        const val FILES_FILE_SELECTION_CANCELLED = "files_file_selection_cancelled"
        
        // Song Picker Events
        const val SONG_PICKER_SONG_SELECTED = "song_picker_song_selected"
        
        // Service Events
        const val SERVICE_PLAY_SONG = "service_play_song"
        const val SERVICE_PAUSE_SONG = "service_pause_song"
        const val SERVICE_RESUME_SONG = "service_resume_song"
        const val SERVICE_STOP_SONG = "service_stop_song"
        const val SERVICE_LOAD_SONGS = "service_load_songs"
        const val SERVICE_SEEK_TO = "service_seek_to"
        const val SERVICE_ERROR = "service_error"
        const val SERVICE_SORT_CHANGED = "service_sort_changed"
        
        // Media Permission & Loading Events
        const val MEDIA_PERMISSION_STATUS = "media_permission_status"
        const val MEDIASTORE_QUERY_EXECUTED = "mediastore_query_executed"
        const val MEDIASTORE_SCAN_TRIGGERED = "mediastore_scan_triggered"
        const val SONGS_LOADING_RESULT = "songs_loading_result"
        const val MEDIASTORE_REFRESH_ATTEMPTED = "mediastore_refresh_attempted"
        
        // Broadcast Receiver Events
        const val BROADCAST_SONGS_LOADED = "broadcast_songs_loaded"
        const val BROADCAST_APP_OPENED_FROM_NOTIFICATION = "broadcast_app_opened_from_notification"
        const val BROADCAST_SONG_CHANGED = "broadcast_song_changed"
        const val BROADCAST_PLAYBACK_STATE_CHANGED = "broadcast_playback_state_changed"
        const val BROADCAST_ERROR = "broadcast_error"
        
        // Overlay Service Events
        const val OVERLAY_SERVICE_ACTION = "overlay_service_action"
        const val OVERLAY_PERMISSION_DENIED = "overlay_permission_denied"
        const val OVERLAY_WINDOW_SHOWN = "overlay_window_shown"
        const val OVERLAY_WINDOW_HIDDEN = "overlay_window_hidden"
        const val OVERLAY_ERROR = "overlay_error"
        
        // Create Screen Events - Missing Interactions
        const val CREATION_BACK_BUTTON_PRESSED = "creation_back_button_pressed"
        const val CREATION_CONFIRMATION_DIALOG_SHOWN = "creation_confirmation_dialog_shown"
        const val CREATION_CONFIRMATION_DIALOG_CONFIRMED = "creation_confirmation_dialog_confirmed"
        const val CREATION_CONFIRMATION_DIALOG_CANCELLED = "creation_confirmation_dialog_cancelled"
        const val CREATION_START_BUTTON_CLICKED = "creation_start_button_clicked"
        const val CREATION_SONG_CHANGE_CLICKED = "creation_song_change_clicked"
        const val CREATION_TEXT_CHANGE_CLICKED = "creation_text_change_clicked"
        const val CREATION_UNDO_CLICKED = "creation_undo_clicked"
        const val CREATION_MORE_OPTIONS_CLICKED = "creation_more_options_clicked"
        const val CREATION_LYRICS_TEXT_EDITED = "creation_lyrics_text_edited"
        const val CREATION_TIMESTAMP_BUTTON_CLICKED = "creation_timestamp_button_clicked"
        const val CREATION_STEPS_INDICATOR_SHOWN = "creation_steps_indicator_shown"
        const val CREATION_STEPS_INDICATOR_HIDDEN = "creation_steps_indicator_hidden"
        const val CREATION_IMPORT_PASTE_DIALOG_SHOWN = "creation_import_paste_dialog_shown"
        const val CREATION_IMPORT_PASTE_DIALOG_DISMISSED = "creation_import_paste_dialog_dismissed"
        
        // Tag Editor Events - All Interactions
        const val TAG_EDITOR_SCREEN_OPENED = "tag_editor_screen_opened"
        const val TAG_EDITOR_FIELD_EDITED = "tag_editor_field_edited"
        const val TAG_EDITOR_FIELD_FOCUSED = "tag_editor_field_focused"
        const val TAG_EDITOR_FIELD_BLURRED = "tag_editor_field_blurred"
        const val TAG_EDITOR_SAVE_CLICKED = "tag_editor_save_clicked"
        const val TAG_EDITOR_SAVE_SUCCESS = "tag_editor_save_success"
        const val TAG_EDITOR_SAVE_FAILED = "tag_editor_save_failed"
        const val TAG_EDITOR_CANCEL_CLICKED = "tag_editor_cancel_clicked"
        const val TAG_EDITOR_BACK_BUTTON_PRESSED = "tag_editor_back_button_pressed"
        const val TAG_EDITOR_BATCH_MODE_INITIATED = "tag_editor_batch_mode_initiated"
        const val TAG_EDITOR_BATCH_NEXT_CLICKED = "tag_editor_batch_next_clicked"
        const val TAG_EDITOR_BATCH_PREVIOUS_CLICKED = "tag_editor_batch_previous_clicked"
        const val TAG_EDITOR_EXPANDED_TOGGLED = "tag_editor_expanded_toggled"
        
        // Paste Lyrics Events - All Interactions
        const val PASTE_LYRICS_SCREEN_OPENED = "paste_lyrics_screen_opened"
        const val PASTE_LYRICS_TEXT_ENTERED = "paste_lyrics_text_entered"
        const val PASTE_LYRICS_TEXT_CLEARED = "paste_lyrics_text_cleared"
        const val PASTE_LYRICS_SAVE_CLICKED = "paste_lyrics_save_clicked"
        const val PASTE_LYRICS_SAVE_SUCCESS = "paste_lyrics_save_success"
        const val PASTE_LYRICS_SAVE_FAILED = "paste_lyrics_save_failed"
        const val PASTE_LYRICS_BACK_BUTTON_PRESSED = "paste_lyrics_back_button_pressed"
        const val PASTE_LYRICS_KEYBOARD_SHOWN = "paste_lyrics_keyboard_shown"
        const val PASTE_LYRICS_KEYBOARD_HIDDEN = "paste_lyrics_keyboard_hidden"
        const val PASTE_LYRICS_CONFIRMATION_DIALOG_SHOWN = "paste_lyrics_confirmation_dialog_shown"
        const val PASTE_LYRICS_CONFIRMATION_DIALOG_CONFIRMED = "paste_lyrics_confirmation_dialog_confirmed"
        const val PASTE_LYRICS_CONFIRMATION_DIALOG_CANCELLED = "paste_lyrics_confirmation_dialog_cancelled"
        
        // Component-Level Events - Music Player Controls
        const val COMPACT_PLAYER_CLICKED = "compact_player_clicked"
        const val COMPACT_PLAYER_PLAY_PAUSE_CLICKED = "compact_player_play_pause_clicked"
        const val COMPACT_PLAYER_NEXT_CLICKED = "compact_player_next_clicked"
        const val COMPACT_PLAYER_PREVIOUS_CLICKED = "compact_player_previous_clicked"
        const val COMPACT_PLAYER_PROGRESS_CLICKED = "compact_player_progress_clicked"
        
        // Music Player Controller Events
        const val PLAYER_CONTROLLER_PLAY_CLICKED = "player_controller_play_clicked"
        const val PLAYER_CONTROLLER_PAUSE_CLICKED = "player_controller_pause_clicked"
        const val PLAYER_CONTROLLER_NEXT_CLICKED = "player_controller_next_clicked"
        const val PLAYER_CONTROLLER_PREVIOUS_CLICKED = "player_controller_previous_clicked"
        const val PLAYER_CONTROLLER_SEEK_STARTED = "player_controller_seek_started"
        const val PLAYER_CONTROLLER_SEEK_ENDED = "player_controller_seek_ended"
        const val PLAYER_CONTROLLER_SHUFFLE_TOGGLED = "player_controller_shuffle_toggled"
        const val PLAYER_CONTROLLER_REPEAT_TOGGLED = "player_controller_repeat_toggled"
        const val SEEK_FORWARD_5_SECONDS = "seek_forward_5_seconds"
        const val SEEK_BACKWARD_5_SECONDS = "seek_backward_5_seconds"
        
        // Bottom Sheet Events - General
        const val BOTTOM_SHEET_OPENED = "bottom_sheet_opened"
        const val BOTTOM_SHEET_CLOSED = "bottom_sheet_closed"
        const val BOTTOM_SHEET_DISMISSED = "bottom_sheet_dismissed"
        const val BOTTOM_SHEET_EXPANDED = "bottom_sheet_expanded"
        const val BOTTOM_SHEET_COLLAPSED = "bottom_sheet_collapsed"
        
        // Song Details Bottom Sheet Events
        const val SONG_DETAILS_BOTTOM_SHEET_OPENED = "song_details_bottom_sheet_opened"
        const val SONG_DETAILS_BOTTOM_SHEET_CLOSED = "song_details_bottom_sheet_closed"
        const val SONG_DETAILS_BOTTOM_SHEET_ACTION_CLICKED = "song_details_bottom_sheet_action_clicked"
        
        // File Chooser Bottom Sheet Events
        const val FILE_CHOOSER_BOTTOM_SHEET_OPENED = "file_chooser_bottom_sheet_opened"
        const val FILE_CHOOSER_BOTTOM_SHEET_CLOSED = "file_chooser_bottom_sheet_closed"
        const val FILE_CHOOSER_OPTION_SELECTED = "file_chooser_option_selected"
        
        // Dialog Events - General
        const val DIALOG_OPENED = "dialog_opened"
        const val DIALOG_CLOSED = "dialog_closed"
        const val DIALOG_CONFIRMED = "dialog_confirmed"
        const val DIALOG_CANCELLED = "dialog_cancelled"
        const val DIALOG_DISMISSED = "dialog_dismissed"
        
        // Alert Dialog Events
        const val ALERT_DIALOG_SHOWN = "alert_dialog_shown"
        const val ALERT_DIALOG_POSITIVE_CLICKED = "alert_dialog_positive_clicked"
        const val ALERT_DIALOG_NEGATIVE_CLICKED = "alert_dialog_negative_clicked"
        const val ALERT_DIALOG_NEUTRAL_CLICKED = "alert_dialog_neutral_clicked"
        
        // Form Interaction Events
        const val TEXT_FIELD_FOCUSED = "text_field_focused"
        const val TEXT_FIELD_BLURRED = "text_field_blurred"
        const val TEXT_FIELD_VALUE_CHANGED = "text_field_value_changed"
        const val TEXT_FIELD_CLEARED = "text_field_cleared"
        const val DROPDOWN_OPENED = "dropdown_opened"
        const val DROPDOWN_CLOSED = "dropdown_closed"
        const val DROPDOWN_OPTION_SELECTED = "dropdown_option_selected"
        
        // Navigation Events - Detailed
        const val NAVIGATION_BACK_PRESSED = "navigation_back_pressed"
        const val NAVIGATION_BACK_GESTURE = "navigation_back_gesture"
        const val NAVIGATION_TAB_SELECTED = "navigation_tab_selected"
        const val NAVIGATION_DEEP_LINK_OPENED = "navigation_deep_link_opened"
        const val NAVIGATION_SCREEN_CHANGED = "navigation_screen_changed"
        
        // List and Card Interactions
        const val LIST_ITEM_CLICKED = "list_item_clicked"
        const val LIST_ITEM_LONG_PRESSED = "list_item_long_pressed"
        const val LIST_ITEM_SWIPED = "list_item_swiped"
        const val CARD_CLICKED = "card_clicked"
        const val CARD_LONG_PRESSED = "card_long_pressed"
        
        // Button and Icon Events
        const val ICON_BUTTON_CLICKED = "icon_button_clicked"
        const val FLOATING_ACTION_BUTTON_CLICKED = "floating_action_button_clicked"
        const val TOGGLE_BUTTON_CLICKED = "toggle_button_clicked"
        const val RADIO_BUTTON_SELECTED = "radio_button_selected"
        const val CHECKBOX_TOGGLED = "checkbox_toggled"
        const val SWITCH_TOGGLED = "switch_toggled"
        
        // Gesture Events
        const val SWIPE_GESTURE_PERFORMED = "swipe_gesture_performed"
        const val PINCH_ZOOM_PERFORMED = "pinch_zoom_performed"
        const val DOUBLE_TAP_PERFORMED = "double_tap_performed"
        const val LONG_PRESS_PERFORMED = "long_press_performed"
        
        // Lyrics Display Events
        const val LYRICS_DISPLAY_SCROLLED = "lyrics_display_scrolled"
        const val LYRICS_LINE_CLICKED = "lyrics_line_clicked"
        const val LYRICS_LINE_HIGHLIGHTED = "lyrics_line_highlighted"
        const val LYRICS_FONT_SIZE_CHANGED = "lyrics_font_size_changed"
        const val LYRICS_AUTO_SCROLL_TOGGLED = "lyrics_auto_scroll_toggled"
        
        // Search Events
        const val SEARCH_QUERY_ENTERED = "search_query_entered"
        const val SEARCH_QUERY_CLEARED = "search_query_cleared"
        const val SEARCH_RESULT_CLICKED = "search_result_clicked"
        const val SEARCH_FILTER_APPLIED = "search_filter_applied"
        const val SEARCH_VOICE_ACTIVATED = "search_voice_activated"
        
        // Menu Events
        const val MENU_OPENED = "menu_opened"
        const val MENU_CLOSED = "menu_closed"
        const val MENU_ITEM_CLICKED = "menu_item_clicked"
        const val CONTEXT_MENU_OPENED = "context_menu_opened"
        const val THREE_DOT_MENU_CLICKED = "three_dot_menu_clicked"
        
        // Snackbar Events
        const val SNACKBAR_SHOWN = "snackbar_shown"
        const val SNACKBAR_DISMISSED = "snackbar_dismissed"
        const val SNACKBAR_ACTION_CLICKED = "snackbar_action_clicked"
        
        // Toast Events
        const val TOAST_SHOWN = "toast_shown"
        
        // Pull to Refresh Events
        const val PULL_TO_REFRESH_TRIGGERED = "pull_to_refresh_triggered"
        const val PULL_TO_REFRESH_COMPLETED = "pull_to_refresh_completed"
        
        // Loading Events
        const val LOADING_STARTED = "loading_started"
        const val LOADING_COMPLETED = "loading_completed"
        const val LOADING_FAILED = "loading_failed"
        
        // Error Handling Events
        const val ERROR_OCCURRED = "error_occurred"
        const val ERROR_DIALOG_SHOWN = "error_dialog_shown"
        const val ERROR_RETRY_CLICKED = "error_retry_clicked"
        const val ERROR_DISMISSED = "error_dismissed"
        
        // In-App Update Events
        const val UPDATE_CHECK_STARTED = "update_check_started"
        const val UPDATE_CHECK_COMPLETED = "update_check_completed"
        const val UPDATE_CHECK_FAILED = "update_check_failed"
        const val FORCE_UPDATE_SHOWN = "force_update_shown"
        const val SOFT_UPDATE_SHOWN = "soft_update_shown"
        const val UPDATE_BUTTON_CLICKED = "update_button_clicked"
        const val UPDATE_LATER_CLICKED = "update_later_clicked"
        const val UPDATE_DIALOG_DISMISSED = "update_dialog_dismissed"
        const val PLAY_STORE_OPENED = "play_store_opened"
        const val PLAY_STORE_OPEN_FAILED = "play_store_open_failed"
        
        // Share Intent Events
        const val AUDIO_SHARED_TO_APP = "audio_shared_to_app"
        const val SHARED_AUDIO_INTENT_RECEIVED = "shared_audio_intent_received"
        const val SHARED_AUDIO_PROCESSING_STARTED = "shared_audio_processing_started"
        const val SHARED_AUDIO_PROCESSING_COMPLETED = "shared_audio_processing_completed"
        const val SHARED_AUDIO_PROCESSING_FAILED = "shared_audio_processing_failed"
        const val SHARED_AUDIO_LOADED_SUCCESSFULLY = "shared_audio_loaded_successfully"
        const val SHARED_AUDIO_VALIDATION_FAILED = "shared_audio_validation_failed"
        const val SHARED_AUDIO_NAVIGATION_SUCCESS = "shared_audio_navigation_success"
        const val SHARED_AUDIO_UNSUPPORTED_FORMAT = "shared_audio_unsupported_format"
        const val SHARED_AUDIO_RECEIVED_IN_MUSIC = "shared_audio_received_in_music"
        const val SHARED_AUDIO_SONG_MATCHED = "shared_audio_song_matched"
        const val SHARED_AUDIO_NO_MATCH = "shared_audio_no_match"
        const val SHARED_AUDIO_URI_RESOLVED = "shared_audio_uri_resolved"
        const val SHARED_AUDIO_PATH_MATCHED = "shared_audio_path_matched"
        const val SHARED_AUDIO_METADATA_MATCHED = "shared_audio_metadata_matched"
        const val SHARED_AUDIO_RESOLUTION_FAILED = "shared_audio_resolution_failed"
    }

    /**
     * Firebase Analytics Parameter Names
     */
    object Params {
        // Common Parameters
        const val SONG_ID = "song_id"
        const val SONG_TITLE = "song_title"
        const val SONG_ARTIST = "song_artist"
        const val FILE_ID = "file_id"
        const val FILE_NAME = "file_name"
        const val FILENAME = "filename"
        const val ERROR_MESSAGE = "error_message"
        const val MESSAGE = "message"
        const val RESULT = "result"
        const val REASON = "reason"
        const val SOURCE = "source"
        const val SCREEN = "screen"
        
        // Navigation Parameters
        const val ITEM_NAME = "item_name"
        const val NAVIGATION = "navigation"
        const val SEARCH_SCREEN = "search_screen"
        
        // Playback Parameters
        const val SEEK_POSITION_MS = "seek_position_ms"
        const val NEW_TIMESTAMP_MS = "new_timestamp_ms"
        const val SHUFFLE_ENABLED = "shuffle_enabled"
        const val CURRENT_PLAYING = "current_playing"
        const val CURRENT_PLAYING_PRESERVE_STATE = "current_playing_preserve_state"
        const val CURRENT_SONG_ID = "current_song_id"
        const val FAVORITES_ONLY = "favorites_only"
        const val IS_FAVORITE = "is_favorite"
        const val CURRENT_TIME_MS = "current_time_ms"
        const val HAS_UNSAVED_CHANGES = "has_unsaved_changes"
        const val HAS_NO_UNSAVED_CHANGES = "has_no_unsaved_changes"
        const val IN_SUBFLOW = "in_subflow"
        const val PERMISSION_GRANTED = "permission_granted"
        const val IS_CHANGING_SONG = "is_changing_song"
        const val LINKING_MODE = "linking_mode"
        const val PLAY_MODE = "play_mode"
        const val SOURCE_SCREEN = "source_screen"
        const val SONGS_COUNT = "songs_count"
        const val IS_PLAYING = "is_playing"
        const val ACTION = "action"
        const val ERROR_TYPE = "error_type"
        const val CURRENT_SONG_TITLE = "current_song_title"
        const val HAS_LYRICS = "has_lyrics"
        
        // Media Loading Parameters
        const val ANDROID_VERSION = "android_version"
        const val CURSOR_COUNT = "cursor_count"
        const val CURSOR_SUCCESS = "cursor_success"
        const val PERMISSION_STATUS = "permission_status"
        const val SCAN_DIRECTORIES_COUNT = "scan_directories_count"
        const val AUDIO_FILES_FOUND = "audio_files_found"
        const val REFRESH_ATTEMPT_NUMBER = "refresh_attempt_number"
        const val REFRESH_SUCCESS = "refresh_success"
        const val LOADING_METHOD = "loading_method"
        
        // Creation Parameters
        const val PREVIEW_ACTIVE = "preview_active"
        const val LINE_INDEX = "line_index"
        const val LINE_COUNT = "line_count"
        const val LINES_GENERATED = "lines_generated"
        const val CHARACTER_COUNT = "character_count"
        const val RETRIES = "retries"
        
        // Permission Parameters
        const val PERMISSION_TYPE = "permission_type"
        const val MEDIA_EXPORT = "media_export"
        const val MANAGE_EXTERNAL_STORAGE = "manage_external_storage"
        const val GRANTED = "granted"
        const val DENIED = "denied"
        
        // Permission Status Values
        const val PERMISSION_STATUS_GRANTED = "granted"
        const val PERMISSION_STATUS_NOT_GRANTED = "not_granted"
        
        // UI Parameters
        const val COMPACT_PLAYER = "compact_player"
        const val DETAILS_CONTROLLER = "details_controller"
        const val SONG_ITEM_CLICK = "song_item_click"
        const val MUSIC_SCREEN = "music_screen"
        const val AUDIO = "audio"
        
        // Status Parameters
        const val UNKNOWN = "unknown"
        const val NOT_FOUND_OR_TIMEOUT = "not_found_or_timeout"
        
        // File Parameters
        const val FILE_URI = "file_uri"
        const val MIME_TYPE = "mime_type"
        const val INVALID_FILE_TYPE = "invalid_file_type"
        
        // Creation State Parameters
        const val CLEARED_SONG = "cleared_song"
        const val CLEARED_LYRICS = "cleared_lyrics"
        const val STOPPED_MUSIC = "stopped_music"
        
        // User Identity Parameters
        const val USER_ID = "user_id"
        const val ANALYTICS_PLATFORM = "analytics_platform"
        
        // FAQ Parameters
        const val FAQ_ITEM_ID = "faq_item_id"
        const val FAQ_QUESTION = "faq_question"
        
        // Changelog Parameters
        const val CHANGELOG_VERSION = "changelog_version"
        const val CHANGELOG_ENTRY_COUNT = "changelog_entry_count"
        const val CHANGELOG_CATEGORY = "changelog_category"
        
        // Refresh Parameters
        const val REFRESH_TYPE = "refresh_type"
        
        // Dialog Parameters
        const val DIALOG_TYPE = "dialog_type"
        const val DIALOG_TITLE = "dialog_title"
        const val DIALOG_MESSAGE = "dialog_message"
        const val BUTTON_TYPE = "button_type"
        const val BUTTON_TEXT = "button_text"
        const val DISMISS_METHOD = "dismiss_method"
        
        // Form Parameters
        const val FIELD_NAME = "field_name"
        const val FIELD_TYPE = "field_type"
        const val INPUT_LENGTH = "input_length"
        const val VALIDATION_ERROR = "validation_error"
        const val DROPDOWN_OPTION = "dropdown_option"
        const val SELECTION_COUNT = "selection_count"
        
        // Navigation Parameters Extended
        const val NAVIGATION_SOURCE = "navigation_source"
        const val NAVIGATION_DESTINATION = "navigation_destination"
        const val NAVIGATION_METHOD = "navigation_method"
        const val DEEP_LINK_URL = "deep_link_url"
        const val TAB_NAME = "tab_name"
        const val PREVIOUS_SCREEN = "previous_screen"
        
        // Gesture Parameters
        const val GESTURE_TYPE = "gesture_type"
        const val GESTURE_DIRECTION = "gesture_direction"
        const val GESTURE_VELOCITY = "gesture_velocity"
        const val GESTURE_DISTANCE = "gesture_distance"
        
        // List Parameters
        const val LIST_ITEM_ID = "list_item_id"
        const val LIST_ITEM_POSITION = "list_item_position"
        const val LIST_SIZE = "list_size"
        const val ITEM_TYPE = "item_type"
        const val SWIPE_DIRECTION = "swipe_direction"
        
        // Player Parameters Extended
        const val PLAYER_STATE = "player_state"
        const val PLAYER_MODE = "player_mode"
        const val PLAYBACK_SPEED = "playback_speed"
        const val VOLUME_LEVEL = "volume_level"
        const val REPEAT_MODE = "repeat_mode"
        const val EQUALIZER_PRESET = "equalizer_preset"
        
        // UI Component Parameters
        const val COMPONENT_TYPE = "component_type"
        const val COMPONENT_ID = "component_id"
        const val ANIMATION_TYPE = "animation_type"
        const val ANIMATION_DURATION = "animation_duration"
        const val UI_STATE = "ui_state"
        const val HAS_CONTENT = "has_content"
        
        // Search Parameters Extended
        const val SEARCH_QUERY = "search_query"
        const val SEARCH_RESULTS_COUNT = "search_results_count"
        const val SEARCH_CATEGORY = "search_category"
        const val SEARCH_FILTER = "search_filter"
        const val SEARCH_SUGGESTION = "search_suggestion"
        
        // Sorting Parameters
        const val SORT_TYPE = "sort_type"
        const val SORT_ORDER = "sort_order"
        
        // Text Parameters
        const val TEXT_LENGTH = "text_length"
        const val TEXT_MODIFIED = "text_modified"
        const val FONT_SIZE = "font_size"
        const val TEXT_STYLE = "text_style"
        const val PREVIOUS_LENGTH = "previous_length"
        
        // Bottom Sheet Parameters
        const val SHEET_TYPE = "sheet_type"
        const val SHEET_HEIGHT = "sheet_height"
        const val SHEET_STATE = "sheet_state"
        
        // Menu Parameters
        const val MENU_TYPE = "menu_type"
        const val MENU_ITEM_ID = "menu_item_id"
        const val MENU_ITEM_TITLE = "menu_item_title"
        const val MENU_POSITION = "menu_position"
        
        // Tag Editor Parameters
        const val TAG_FIELD = "tag_field"
        const val TAG_VALUE = "tag_value"
        const val TAG_OPERATION = "tag_operation"
        const val BATCH_SIZE = "batch_size"
        const val BATCH_INDEX = "batch_index"
        const val BATCH_MODE = "batch_mode"
        const val HAS_SONG_DATA = "has_song_data"
        const val HAS_ERROR = "has_error"
        
        // Create Screen Parameters
        const val STEP_NUMBER = "step_number"
        const val STEP_NAME = "step_name"
        const val CREATION_STAGE = "creation_stage"
        const val TIMESTAMP_COUNT = "timestamp_count"
        const val LYRICS_SOURCE = "lyrics_source"
        const val SYNC_SONG_ID = "sync_song_id"
        const val SYNC_FILE_ID = "sync_file_id"
        
        // Error Parameters Extended
        const val ERROR_CODE = "error_code"
        const val ERROR_CATEGORY = "error_category"
        const val ERROR_CONTEXT = "error_context"
        const val STACK_TRACE = "stack_trace"
        const val USER_ACTION_BEFORE_ERROR = "user_action_before_error"
        
        // Performance Parameters
        const val LOAD_TIME_MS = "load_time_ms"
        const val RESPONSE_TIME_MS = "response_time_ms"
        const val MEMORY_USAGE_MB = "memory_usage_mb"
        const val CPU_USAGE_PERCENT = "cpu_usage_percent"
        
        // Accessibility Parameters
        const val ACCESSIBILITY_ENABLED = "accessibility_enabled"
        const val SCREEN_READER_USED = "screen_reader_used"
        const val CONTENT_DESCRIPTION = "content_description"
        
        // Device Parameters
        const val DEVICE_TYPE = "device_type"
        const val SCREEN_ORIENTATION = "screen_orientation"
        const val SCREEN_DENSITY = "screen_density"
        const val AVAILABLE_STORAGE_GB = "available_storage_gb"
        
        // In-App Update Parameters
        const val UPDATE_TYPE = "update_type"
        const val CURRENT_VERSION = "current_version"
        const val REQUIRED_VERSION = "required_version"
        const val UPDATE_SOURCE = "update_source"
        const val UPDATE_ACTION = "update_action"
        
        // Share Intent Parameters
        const val SHARED_AUDIO_FORMAT = "shared_audio_format"
        const val SHARED_AUDIO_SOURCE_APP = "shared_audio_source_app"
        const val SHARED_AUDIO_FILE_SIZE = "shared_audio_file_size"
        const val SHARED_AUDIO_DURATION_MS = "shared_audio_duration_ms"
        const val SHARED_AUDIO_URI_SCHEME = "shared_audio_uri_scheme"
        const val MULTIPLE_AUDIO_COUNT = "multiple_audio_count"
        const val URI_COUNT = "uri_count"
        const val VALIDATION_ERROR_REASON = "validation_error_reason"
        const val INTENT_ACTION_TYPE = "intent_action_type"
        const val PROCESSING_TIME_MS = "processing_time_ms"
        const val TIMESTAMP = "timestamp"
        const val RESOLVED_PATH = "resolved_path"
        const val DISPLAY_NAME = "display_name"
        const val MATCHING_STRATEGY = "matching_strategy"
        const val ORIGINAL_URI_SCHEME = "original_uri_scheme"
    }

    /**
     * Screen Names for Firebase Analytics
     */
    object ScreenNames {
        const val MUSIC = "Music"
        const val MUSIC_SCREEN = "MusicScreen"
        const val CREATE = "Create"
        const val CREATE_SCREEN = "CreateScreen"
        const val FILES = "Files"
        const val SONG_DETAILS = "SongDetails"
        const val SONG_DETAILS_SCREEN = "SongDetailsScreen"
        const val PASTE_LYRICS = "PasteLyrics"
        const val PASTE_LYRICS_SCREEN = "PasteLyricsScreen"
        const val SETTINGS_SCREEN = "SettingsScreen"
        const val SETTINGS_SCREEN_KT = "SettingsScreen.kt"
        const val SONG_PICKER = "SongPicker"
        const val SONG_PICKER_SCREEN = "SongPickerScreen"
        const val TAG_EDITOR = "TagEditor"
        const val TAG_EDITOR_SCREEN = "TagEditorScreen"
        const val FILES_SCREEN = "FilesScreen"
    }

    /**
     * Common Error Messages for Analytics
     */
    object ErrorMessages {
        const val NO_LYRICS_AVAILABLE = "No lyrics available for this song"
        const val NO_SONG_PLAYING = "No song is currently playing"
        const val NO_SONG_SELECTED = "No song selected"
        const val STORAGE_PERMISSIONS_REQUIRED = "Storage permissions are required for video export"
        const val UNKNOWN_ERROR = "Unknown error"
    }
    
    /**
     * Common Parameter Values for Analytics
     */
    object Values {
        // Loading Method Values
        const val LOADING_METHOD_MEDIASTORE_QUERY = "mediastore_query"

        // Permission Status Values
        const val PERMISSION_STATUS_GRANTED = "granted"
        const val PERMISSION_STATUS_NOT_GRANTED = "not_granted"
        
        // Error Type Values
        const val ERROR_TYPE_MEDIA_SCAN_RETRY_FAILED = "media_scan_retry_failed"
        const val ERROR_TYPE_DIRECTORY_SCAN_SECURITY_ERROR = "directory_scan_security_error"
        const val ERROR_TYPE_DIRECTORY_SCAN_UNEXPECTED_ERROR = "directory_scan_unexpected_error"
        const val ERROR_TYPE_AUDIO_FILES_SCAN_ERROR = "audio_files_scan_error"
        
        // Error Message Values
        const val ERROR_MESSAGE_PERMISSION_DENIED = "permission_denied"
        const val ERROR_MESSAGE_UNKNOWN = "unknown_error"
        
        // Dialog Type Values
        const val DIALOG_TYPE_UNSAVED_CHANGES = "unsaved_changes"
        
        // Button Type Values
        const val BUTTON_TYPE_CONFIRM = "confirm"
        const val BUTTON_TYPE_CANCEL = "cancel"
        
        // Dismiss Method Values
        const val DISMISS_METHOD_OUTSIDE_CLICK = "outside_click"
        const val DISMISS_METHOD_BUTTON_CLICK = "button_click"
        
        // Action Values
        const val ACTION_NAVIGATE_UP = "navigate_up"
        
        // Source Values
        const val SOURCE_DROPDOWN_MENU = "dropdown_menu"
    }
}