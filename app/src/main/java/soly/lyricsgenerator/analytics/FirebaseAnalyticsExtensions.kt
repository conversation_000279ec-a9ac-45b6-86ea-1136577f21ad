package soly.lyricsgenerator.analytics

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics

/**
 * Extension functions for Firebase Analytics to enable dual-platform logging
 * Following the dual-platform analytics implementation pattern
 */

/**
 * Logs an event to both Firebase Analytics and Amplitude with a single call.
 * This extension function provides a clean syntax for dual-platform logging.
 * 
 * Usage:
 * ```kotlin
 * firebaseAnalytics.logEventDual("button_clicked") {
 *     param("button_name", "save")
 *     param("screen", "create")
 * }
 * ```
 * 
 * @param name Event name
 * @param block Lambda to configure event parameters
 */
fun FirebaseAnalytics.logEventDual(name: String, block: EventBuilder.() -> Unit = {}) {
    GlobalAnalyticsHook.logEvent(name, block)
}

/**
 * Legacy support for Bundle-based parameters.
 * Logs to both Firebase Analytics and Amplitude.
 * 
 * @param name Event name
 * @param parameters Event parameters as Bundle
 */
fun FirebaseAnalytics.logEventDual(name: String, parameters: Bundle?) {
    // Use GlobalAnalyticsHook for consistency with the other overload
    GlobalAnalyticsHook.logEvent(name) {
        // Convert Bundle parameters to the EventBuilder format
        // Note: This is legacy support - prefer using the EventBuilder pattern directly
        parameters?.keySet()?.forEach { key ->
            // Use the bundle's string representation for simplicity and compatibility
            val stringValue = parameters.getString(key)
            if (stringValue != null) {
                param(key, stringValue)
            }
        }
    }
}