package soly.lyricsgenerator.analytics

import android.os.Bundle

/**
 * Utility class for analytics-related helper functions.
 * Contains shared functionality used across analytics components.
 */
object AnalyticsUtils {
    
    /**
     * Converts Android Bundle to Map for Amplitude compatibility.
     * 
     * This function handles the conversion of Firebase Analytics Bundle format
     * to the Map format required by Amplitude SDK.
     * 
     * @param bundle Firebase Analytics parameters Bundle
     * @return Map<String, Any> compatible with Amplitude SDK
     */
    fun bundleToMap(bundle: Bundle?): Map<String, Any> {
        if (bundle == null) return emptyMap()
        
        val map = mutableMapOf<String, Any>()
        try {
            for (key in bundle.keySet()) {
                val value = bundle.get(key)
                if (value != null) {
                    // Convert value to Amplitude-compatible type
                    when (value) {
                        is String, is Int, is Long, is Float, is Double, is Boolean -> {
                            map[key] = value
                        }
                        else -> {
                            // Convert other types to string for compatibility
                            map[key] = value.toString()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Return empty map if conversion fails to prevent crashes
        }
        
        return map
    }
}