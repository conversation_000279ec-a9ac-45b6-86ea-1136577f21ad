package soly.lyricsgenerator.analytics

/**
 * Constants for UserIdentityManager
 * Following the project's zero tolerance policy for hardcoded strings.
 */
object UserIdentityConstants {
    
    /**
     * EncryptedSharedPreferences constants
     */
    object PreferenceKeys {
        const val ENCRYPTED_PREFS_FILE_NAME = "user_identity_prefs"
        const val INSTALLATION_ID_KEY = "installation_id"
    }
    
    /**
     * Analytics SDK identifiers
     */
    object AnalyticsPlatforms {
        const val FIREBASE_ANALYTICS = "firebase_analytics"
        const val FIREBASE_CRASHLYTICS = "firebase_crashlytics"
        const val AMPLITUDE = "amplitude"
    }
    
    /**
     * Error messages for analytics events
     */
    object ErrorMessages {
        const val UUID_GENERATION_FAILED = "Failed to generate UUID"
        const val ENCRYPTED_PREFS_CREATION_FAILED = "Failed to create encrypted preferences"
        const val UUID_STORAGE_FAILED = "Failed to store UUID in encrypted preferences"
        const val UUID_RETRIEVAL_FAILED = "Failed to retrieve UUID from encrypted preferences"
        const val ANALYTICS_PROPAGATION_FAILED = "Failed to propagate UUID to analytics platform"
    }
}