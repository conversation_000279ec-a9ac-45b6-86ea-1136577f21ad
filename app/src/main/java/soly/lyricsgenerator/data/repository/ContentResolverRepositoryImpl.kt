package soly.lyricsgenerator.data.repository

import android.app.Application
import android.content.ContentResolver
import android.database.Cursor
import android.net.Uri
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.OpenableColumns
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.repository.ContentResolverRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * Implementation of ContentResolverRepository.
 * Handles Android-specific ContentResolver operations.
 */
class ContentResolverRepositoryImpl @Inject constructor(
    private val context: Application
) : ContentResolverRepository {
    
    override suspend fun resolveUriToPath(uri: Uri): String? = withContext(Dispatchers.IO) {
        Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Resolving URI to path: $uri")
        
        try {
            when {
                // Documents Provider URIs (like Downloads)
                DocumentsContract.isDocumentUri(context, uri) -> {
                    resolveDocumentUri(uri)
                }
                // MediaStore URIs
                "content".equals(uri.scheme, ignoreCase = true) -> {
                    resolveContentUri(uri)
                }
                // File URIs
                "file".equals(uri.scheme, ignoreCase = true) -> {
                    uri.path
                }
                else -> {
                    Timber.tag("DEBUG_FLOW").w("ContentResolverRepositoryImpl: Unknown URI scheme: ${uri.scheme}")
                    null
                }
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ContentResolverRepositoryImpl: Error resolving URI to path")
            null
        }
    }
    
    override suspend fun getDisplayName(uri: Uri): String? = withContext(Dispatchers.IO) {
        Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Getting display name for URI: $uri")
        
        try {
            val cursor: Cursor? = context.contentResolver.query(
                uri,
                arrayOf(OpenableColumns.DISPLAY_NAME),
                null,
                null,
                null
            )
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val displayNameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    if (displayNameIndex != -1) {
                        val displayName = it.getString(displayNameIndex)
                        Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Found display name: $displayName")
                        return@withContext displayName
                    }
                }
            }
            
            Timber.tag("DEBUG_FLOW").w("ContentResolverRepositoryImpl: No display name found for URI")
            null
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ContentResolverRepositoryImpl: Error getting display name")
            null
        }
    }
    
    /**
     * Resolves Document Provider URIs (e.g., Downloads, Google Drive)
     */
    private fun resolveDocumentUri(uri: Uri): String? {
        try {
            val docId = DocumentsContract.getDocumentId(uri)
            Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Document ID: $docId")
            
            // Handle different document providers
            when {
                // Downloads Provider
                isDownloadsDocument(uri) -> {
                    return resolveDownloadsDocument(docId)
                }
                // External Storage Provider
                isExternalStorageDocument(uri) -> {
                    return resolveExternalStorageDocument(docId)
                }
                // MediaStore Documents
                isMediaDocument(uri) -> {
                    return resolveMediaDocument(docId)
                }
                else -> {
                    Timber.tag("DEBUG_FLOW").w("ContentResolverRepositoryImpl: Unknown document provider")
                    return null
                }
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ContentResolverRepositoryImpl: Error resolving document URI")
            return null
        }
    }
    
    /**
     * Resolves regular content URIs
     */
    private fun resolveContentUri(uri: Uri): String? {
        val projection = arrayOf(MediaStore.Audio.Media.DATA)
        
        try {
            val cursor = context.contentResolver.query(uri, projection, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                    val path = it.getString(columnIndex)
                    Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Resolved content URI to path: $path")
                    return path
                }
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ContentResolverRepositoryImpl: Error resolving content URI")
        }
        
        return null
    }
    
    /**
     * Resolves Downloads Provider documents
     */
    private fun resolveDownloadsDocument(docId: String): String? {
        // For Downloads, docId might be "msf:18" or raw ID
        val id = if (docId.startsWith("msf:")) {
            docId.substring(4) // Remove "msf:" prefix
        } else {
            docId
        }
        
        Timber.tag("DEBUG_FLOW").d("ContentResolverRepositoryImpl: Resolving Downloads document with ID: $id")
        
        // Try to resolve through Downloads URI
        try {
            val contentUri = Uri.parse("content://downloads/public_downloads")
            val uri = Uri.withAppendedPath(contentUri, id)
            return resolveContentUri(uri)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ContentResolverRepositoryImpl: Error resolving Downloads document")
        }
        
        return null
    }
    
    /**
     * Resolves External Storage Provider documents
     */
    private fun resolveExternalStorageDocument(docId: String): String? {
        val split = docId.split(":")
        if (split.size >= 2) {
            val type = split[0]
            val path = split[1]
            
            if ("primary".equals(type, ignoreCase = true)) {
                return "${android.os.Environment.getExternalStorageDirectory()}/$path"
            }
        }
        
        return null
    }
    
    /**
     * Resolves MediaStore documents
     */
    private fun resolveMediaDocument(docId: String): String? {
        val split = docId.split(":")
        if (split.size >= 2) {
            val type = split[0]
            val id = split[1]
            
            val contentUri = when (type) {
                "audio" -> MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                else -> return null
            }
            
            return resolveContentUri(Uri.withAppendedPath(contentUri, id))
        }
        
        return null
    }
    
    /**
     * Check if URI is from Downloads Provider
     */
    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }
    
    /**
     * Check if URI is from External Storage Provider
     */
    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }
    
    /**
     * Check if URI is from MediaStore Provider
     */
    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }
}