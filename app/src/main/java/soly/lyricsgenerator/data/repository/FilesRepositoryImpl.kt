package soly.lyricsgenerator.data.repository

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import soly.lyricsgenerator.domain.repository.DatabaseRepository
import soly.lyricsgenerator.domain.repository.FilesRepository
import timber.log.Timber
import java.io.FileNotFoundException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Concrete implementation of the FilesRepository.
 * Reads file content based on file ID by looking up the filename in the database
 * and reading from the app's internal storage.
 */
@Singleton
class FilesRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val databaseRepository: DatabaseRepository // Inject DatabaseRepository
) : FilesRepository {

    override suspend fun getFileContentById(fileId: Long): String? {
        Timber.tag(TAG).d("getFileContentById: Attempting to read file with ID: $fileId")
        return try {
            val fileEntity = databaseRepository.getFileByFileId(fileId)
            if (fileEntity == null) {
                Timber.tag(TAG).w("getFileContentById: No file entity found in DB for file ID: $fileId")
                return null
            }

            val fileName = fileEntity.fileName
            Timber.tag(TAG).d("getFileContentById: Found fileName '$fileName' for ID: $fileId. Attempting to read from internal storage.")

            context.openFileInput(fileName).bufferedReader().use { it.readText() }.also {
                Timber.tag(TAG).d("getFileContentById: Successfully read content for file ID: $fileId (fileName: '$fileName')")
            }
        } catch (e: FileNotFoundException) {
            Timber.tag(TAG).e(e, "getFileContentById: File not found in internal storage for ID: $fileId (fileName likely missing or incorrect)")
            null
        } catch (e: IOException) {
            Timber.tag(TAG).e(e, "getFileContentById: IOException reading file from internal storage for ID: $fileId")
            null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "getFileContentById: Unexpected error retrieving file content for ID: $fileId")
            null
        }
    }

    companion object {
        private const val TAG = "DEBUG_FLOW:FilesRepoImpl"
    }
} 