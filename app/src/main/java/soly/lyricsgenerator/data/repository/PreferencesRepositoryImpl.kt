package soly.lyricsgenerator.data.repository

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.model.SortOrder
import soly.lyricsgenerator.domain.model.SortType
import soly.lyricsgenerator.domain.repository.PreferencesRepository
import soly.lyricsgenerator.ui.viewmodel.PreferenceKeys
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PreferencesRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : PreferencesRepository {
    
    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PreferenceKeys.LYRIC_GENERATOR_PREFS, Context.MODE_PRIVATE)
    }
    
    override suspend fun getSortType(): SortType = withContext(Dispatchers.IO) {
        val sortTypeName = prefs.getString(PreferenceKeys.SORT_TYPE, PreferenceKeys.DEFAULT_SORT_TYPE) 
            ?: PreferenceKeys.DEFAULT_SORT_TYPE
        SortType.fromName(sortTypeName)
    }
    
    override suspend fun saveSortType(sortType: SortType) = withContext(Dispatchers.IO) {
        prefs.edit().putString(PreferenceKeys.SORT_TYPE, sortType::class.simpleName).apply()
    }
    
    override suspend fun getSortOrder(): SortOrder = withContext(Dispatchers.IO) {
        val sortOrderName = prefs.getString(PreferenceKeys.SORT_ORDER, PreferenceKeys.DEFAULT_SORT_ORDER) 
            ?: PreferenceKeys.DEFAULT_SORT_ORDER
        SortOrder.fromName(sortOrderName)
    }
    
    override suspend fun saveSortOrder(sortOrder: SortOrder) = withContext(Dispatchers.IO) {
        prefs.edit().putString(PreferenceKeys.SORT_ORDER, sortOrder::class.simpleName).apply()
    }
    
    override suspend fun getShuffleEnabled(): Boolean = withContext(Dispatchers.IO) {
        prefs.getBoolean(PreferenceKeys.SHUFFLE_ENABLED, false)
    }
    
    override suspend fun saveShuffleEnabled(enabled: Boolean) = withContext(Dispatchers.IO) {
        prefs.edit().putBoolean(PreferenceKeys.SHUFFLE_ENABLED, enabled).apply()
    }
    
    override suspend fun getShowFavoritesOnly(): Boolean = withContext(Dispatchers.IO) {
        prefs.getBoolean(PreferenceKeys.SHOW_FAVORITES_ONLY, false)
    }
    
    override suspend fun saveShowFavoritesOnly(showFavoritesOnly: Boolean) = withContext(Dispatchers.IO) {
        prefs.edit().putBoolean(PreferenceKeys.SHOW_FAVORITES_ONLY, showFavoritesOnly).apply()
    }

    override suspend fun getArtistFilter(): String = withContext(Dispatchers.IO) {
        prefs.getString(PreferenceKeys.ARTIST_FILTER, PreferenceKeys.DEFAULT_ARTIST_FILTER)
            ?: PreferenceKeys.DEFAULT_ARTIST_FILTER
    }

    override suspend fun saveArtistFilter(artist: String) = withContext(Dispatchers.IO) {
        prefs.edit().putString(PreferenceKeys.ARTIST_FILTER, artist).apply()
    }

}