package soly.lyricsgenerator.data.repository.changelog

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import soly.lyricsgenerator.R
import soly.lyricsgenerator.domain.model.ChangelogEntry
import soly.lyricsgenerator.domain.model.ChangelogItem
import soly.lyricsgenerator.domain.model.ChangelogCategory
import soly.lyricsgenerator.domain.repository.ChangelogRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of ChangelogRepository
 * Uses string resources for proper internationalization and follows zero hardcoded strings policy
 */
@Singleton
class ChangelogRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : ChangelogRepository {
    
    // Build changelog entries using string resources - fully internationalized
    private fun buildChangelogEntries(): List<ChangelogEntry> = listOf(
        ChangelogEntry(
            version = "2.0.52",
            date = context.getString(R.string.changelog_v2052_date),
            changes = listOf(
                // New Features
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_undo_button)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_seek_buttons)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_rtf_sync)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_artist_filtering)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_share_from_apps)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_open_with)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2052_three_dots_menu)
                ),
                // Bug Fixes
                ChangelogItem(
                    category = ChangelogCategory.BugFixes,
                    description = context.getString(R.string.changelog_v2052_wav_tags_fix)
                ),
                ChangelogItem(
                    category = ChangelogCategory.BugFixes,
                    description = context.getString(R.string.changelog_v2052_zero_timestamp_scroll_fix)
                )
            )
        ),
        ChangelogEntry(
            version = "2.0.51",
            date = context.getString(R.string.changelog_v2051_date),
            changes = listOf(
                // New Features
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2051_sorting_functionality)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2051_rtf_import)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2051_forced_updates)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2051_gradient_background)
                ),
                // Improvements
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2051_song_loading_performance)
                )
            )
        ),
        ChangelogEntry(
            version = "2.0.50",
            date = context.getString(R.string.changelog_v2050_date),
            changes = listOf(
                // New Features - ordered by user impact
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_floating_overlay)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_tag_editor)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_quick_sync)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_song_details)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_favorites_menu)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_album_art)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_embedded_lyrics)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_linked_lrc)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_txt_support)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_changelog_faq)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_scrollbar)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_hamburger_menu)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_pull_refresh)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_wake_lock)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2050_favorite_menu)
                ),
                // Improvements - ordered by user impact
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_compact_player)
                ),
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_metadata_title_improvement)
                ),
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_performance)
                ),
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_gradient_theme)
                ),
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_navigation_improvements)
                ),
                ChangelogItem(
                    category = ChangelogCategory.Improvements,
                    description = context.getString(R.string.changelog_v2050_toolbar_improvements)
                ),
                // Bug Fixes
                ChangelogItem(
                    category = ChangelogCategory.BugFixes,
                    description = context.getString(R.string.changelog_v2050_android_12_fix)
                )
            )
        ),
        ChangelogEntry(
            version = "2.0.49",
            date = context.getString(R.string.changelog_v2049_date),
            changes = listOf(
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2049_favorites_system)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2049_compact_player)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2049_enhanced_selection)
                ),
                ChangelogItem(
                    category = ChangelogCategory.NewFeatures,
                    description = context.getString(R.string.changelog_v2049_better_paste)
                ),
                ChangelogItem(
                    category = ChangelogCategory.BugFixes,
                    description = context.getString(R.string.changelog_v2049_crash_fixes)
                )
            )
        )
    )
    
    override suspend fun getChangelogEntries(): List<ChangelogEntry> {
        return buildChangelogEntries()
    }
    
    override suspend fun getChangelogForVersion(version: String): ChangelogEntry? {
        return buildChangelogEntries().find { it.version == version }
    }
}