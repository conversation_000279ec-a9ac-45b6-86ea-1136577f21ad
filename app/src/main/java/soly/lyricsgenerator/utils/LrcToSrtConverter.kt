package soly.lyricsgenerator.utils

import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * Utility class to convert LRC format lyrics to SRT format for video export
 */
class LrcToSrtConverter {

    companion object {
        private const val TAG = "LrcToSrtConverter"
    }

    /**
     * Converts a map of LRC lines to SRT format
     * @param lrcLines Map where key is time in milliseconds and value is the lyric line
     * @return String containing the lyrics in SRT format
     */
    fun convertLrcToSrt(lrcLines: Map<Int, String>): String {
        Timber.tag("DEBUG_FLOW").d("$TAG: Converting ${lrcLines.size} lines to SRT format")
        
        if (lrcLines.isEmpty()) {
            Timber.tag("DEBUG_FLOW").e("$TAG: Empty lyrics map provided")
            return ""
        }
        
        val sortedLines = lrcLines.entries.sortedBy { it.key }
        Timber.tag("DEBUG_FLOW").d("$TAG: Sorted ${sortedLines.size} lines by timestamp")
        
        // Log first few and last few entries to verify sorting and content
        sortedLines.take(3).forEach { (timeMs, text) ->
            Timber.tag("DEBUG_FLOW").d("$TAG: Start of lyrics - Time: ${formatSrtTime(timeMs)}, Text: \"$text\"")
        }
        
        if (sortedLines.size > 3) {
            sortedLines.takeLast(3).forEach { (timeMs, text) ->
                Timber.tag("DEBUG_FLOW").d("$TAG: End of lyrics - Time: ${formatSrtTime(timeMs)}, Text: \"$text\"")
            }
        }
        
        val srtBuilder = StringBuilder()
        
        // Calculate display duration for each line (show until next line or for DEFAULT_DURATION if it's the last line)
        val DEFAULT_DURATION = 3000 // 3 seconds for the last line
        
        sortedLines.forEachIndexed { index, (startTimeMs, text) ->
            val endTimeMs = if (index < sortedLines.size - 1) {
                sortedLines[index + 1].key
            } else {
                startTimeMs + DEFAULT_DURATION
            }
            
            // Format times
            val startFormatted = formatSrtTime(startTimeMs)
            val endFormatted = formatSrtTime(endTimeMs)
            
            // Log some entries to verify formatting
            if (index < 3 || index >= sortedLines.size - 3) {
                Timber.tag("DEBUG_FLOW").d("$TAG: Entry ${index+1} - Time: $startFormatted --> $endFormatted, Text: \"$text\"")
            }
            
            // Add subtitle entry
            srtBuilder.append("${index + 1}\n")
            srtBuilder.append("$startFormatted --> $endFormatted\n")
            srtBuilder.append("$text\n\n")
        }
        
        val result = srtBuilder.toString()
        Timber.tag("DEBUG_FLOW").d("$TAG: Conversion complete, generated ${result.length} characters")
        Timber.tag("DEBUG_FLOW").d("$TAG: First 200 chars of SRT result: ${result.take(200)}")
        
        return result
    }
    
    /**
     * Formats milliseconds into SRT time format: HH:MM:SS,mmm
     */
    private fun formatSrtTime(timeMs: Int): String {
        val hours = TimeUnit.MILLISECONDS.toHours(timeMs.toLong())
        val minutes = TimeUnit.MILLISECONDS.toMinutes(timeMs.toLong()) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(timeMs.toLong()) % 60
        val milliseconds = timeMs % 1000
        
        return String.format(
            Locale.US,
            "%02d:%02d:%02d,%03d",
            hours, minutes, seconds, milliseconds
        )
    }
} 