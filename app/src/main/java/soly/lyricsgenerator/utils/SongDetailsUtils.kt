package soly.lyricsgenerator.utils

import android.content.Context
import android.media.MediaMetadataRetriever
import com.arthenica.mobileffmpeg.FFprobe
import com.arthenica.mobileffmpeg.Config
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.model.SongDetails
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object SongDetailsUtils {
    
    suspend fun getSongDetails(@Suppress("UNUSED_PARAMETER") context: Context, song: Song): SongDetails? = withContext(Dispatchers.IO) {
        try {
            val file = File(song.data)
            val fileName = file.name
            val fileSize = file.length()
            val lastModified = file.lastModified()

            // Get additional metadata using MediaMetadataRetriever
            val retriever = MediaMetadataRetriever()
            var format: String
            var bitrate = 0
            var samplingRate = 0
            
            // Get format using FFmpeg for accurate detection
            val (container, codec) = getContainerAndCodecWithFFprobe(song.data)
            format = when {
                container.isNotEmpty() && codec.isNotEmpty() && container != codec -> 
                    "$container ($codec)"
                container.isNotEmpty() -> container
                codec.isNotEmpty() -> codec
                else -> "Unknown"
            }
            
            try {
                retriever.setDataSource(song.data)
                
                // Get bitrate
                val bitrateString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)
                bitrate = bitrateString?.toIntOrNull()?.div(1000) ?: 0 // Convert to kbps
                
                // Get sampling rate (API 31+)
                samplingRate = try {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        val sampleRateString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_SAMPLERATE)
                        sampleRateString?.toIntOrNull() ?: 0
                    } else {
                        // For older APIs, try to get from file extension patterns or use 0
                        getEstimatedSampleRate(fileName)
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error getting sample rate")
                    0
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error extracting metadata for song: ${song.title}")
                // Fallback to file extension for format
                format = fileName.substringAfterLast('.', "").uppercase()
                if (format.isEmpty()) format = "Unknown"
            } finally {
                try {
                    retriever.release()
                } catch (e: Exception) {
                    Timber.e(e, "Error releasing MediaMetadataRetriever")
                }
            }
            
            SongDetails(
                filePath = song.data,
                fileName = fileName,
                fileSize = fileSize,
                lastModified = lastModified,
                format = format,
                container = container,
                audioCodec = codec,
                duration = song.duration,
                bitrate = bitrate,
                samplingRate = samplingRate
            )
        } catch (e: Exception) {
            Timber.e(e, "Error getting song details for: ${song.title}")
            null
        }
    }
    
    fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format(Locale.US, "%.1f GB", gb)
            mb >= 1 -> String.format(Locale.US, "%.1f MB", mb)
            kb >= 1 -> String.format(Locale.US, "%.1f KB", kb)
            else -> "$bytes bytes"
        }
    }
    
    fun formatDate(timestamp: Long): String {
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.US)
        return formatter.format(date)
    }
    
    fun formatDuration(milliseconds: Long): String {
        val seconds = milliseconds / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> String.format(Locale.US, "%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            else -> String.format(Locale.US, "%d:%02d", minutes, seconds % 60)
        }
    }
    
    fun formatBitrate(bitrate: Int): String {
        return if (bitrate > 0) "$bitrate kbps" else "Unknown"
    }
    
    fun formatSamplingRate(samplingRate: Int): String {
        return if (samplingRate > 0) "$samplingRate Hz" else "Unknown"
    }
    
    /**
     * Uses FFprobe to get accurate container and codec information separately
     */
    private fun getContainerAndCodecWithFFprobe(filePath: String): Pair<String, String> {
        return try {
            // Escape file path for FFprobe command
            val escapedPath = "\"${filePath.replace("\"", "\\\"")}\""
            
            // Get container format
            val containerCommand = "-v quiet -show_format -print_format json $escapedPath"
            val containerReturnCode = FFprobe.execute(containerCommand)
            val containerOutput = if (containerReturnCode == Config.RETURN_CODE_SUCCESS) {
                Config.getLastCommandOutput()
            } else ""
            val containerFormat = parseContainerFromFFprobe(containerOutput)
            
            // Get audio codec
            val codecCommand = "-v quiet -select_streams a:0 -show_entries stream=codec_name -print_format json $escapedPath"
            val codecReturnCode = FFprobe.execute(codecCommand)
            val codecOutput = if (codecReturnCode == Config.RETURN_CODE_SUCCESS) {
                Config.getLastCommandOutput()
            } else ""
            val audioCodec = parseCodecFromFFprobe(codecOutput)
            
            Pair(
                containerFormat.ifEmpty { "Unknown" },
                audioCodec.ifEmpty { "Unknown" }
            )
        } catch (e: Exception) {
            Timber.e(e, "Error detecting format with FFprobe")
            // Fallback to file extension
            val extension = File(filePath).name.substringAfterLast('.', "").uppercase()
            val fallbackFormat = extension.ifEmpty { "Unknown" }
            Pair(fallbackFormat, fallbackFormat)
        }
    }
    
    
    /**
     * Parses container format from FFprobe JSON output
     */
    private fun parseContainerFromFFprobe(output: String): String {
        return try {
            // Look for format_name in the JSON output
            val formatNameRegex = "\"format_name\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val match = formatNameRegex.find(output)
            
            val formatName = match?.groupValues?.get(1) ?: return ""
            
            // Map FFprobe format names to user-friendly names
            when (formatName.lowercase()) {
                "mov,mp4,m4a,3gp,3g2,mj2" -> "MP4"
                "mp3" -> "MP3"
                "flac" -> "FLAC"
                "ogg" -> "OGG"
                "wav" -> "WAV"
                "aiff" -> "AIFF"
                "asf" -> "WMA"
                "matroska,webm" -> "MKA"
                "3gp" -> "3GP"
                else -> formatName.split(',')[0].uppercase()
            }
        } catch (e: Exception) {
            Timber.e(e, "Error parsing container format from FFprobe")
            ""
        }
    }
    
    /**
     * Parses audio codec from FFprobe JSON output
     */
    private fun parseCodecFromFFprobe(output: String): String {
        return try {
            // Look for codec_name in the JSON output
            val codecNameRegex = "\"codec_name\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val match = codecNameRegex.find(output)
            
            val codecName = match?.groupValues?.get(1) ?: return ""
            
            // Map FFprobe codec names to user-friendly names
            when (codecName.lowercase()) {
                "aac" -> "AAC"
                "mp3" -> "MP3"
                "mp3float" -> "MP3"
                "flac" -> "FLAC"
                "vorbis" -> "Vorbis"
                "opus" -> "OPUS"
                "pcm_s16le", "pcm_s24le", "pcm_s32le", "pcm_f32le", "pcm_f64le" -> "PCM"
                "wmav1", "wmav2" -> "WMA"
                "ac3" -> "AC3"
                "dts" -> "DTS"
                "amr_nb", "amr_wb" -> "AMR"
                "alac" -> "ALAC"
                else -> codecName.uppercase()
            }
        } catch (e: Exception) {
            Timber.e(e, "Error parsing codec from FFprobe")
            ""
        }
    }
    
    
    /**
     * Provides estimated sample rates for older Android versions
     * Based on common standards for different audio formats
     */
    private fun getEstimatedSampleRate(fileName: String): Int {
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return when (extension) {
            "mp3", "aac", "m4a", "mp4" -> 44100  // Most common for compressed formats
            "flac", "wav", "aiff" -> 44100       // CD quality default
            "ogg" -> 44100                       // Vorbis common rate
            "wma" -> 44100                       // Windows Media common rate
            else -> 0                            // Unknown
        }
    }
}