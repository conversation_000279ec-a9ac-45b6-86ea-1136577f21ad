package soly.lyricsgenerator.utils

import org.jaudiotagger.tag.FieldKey
import soly.lyricsgenerator.domain.model.AudioTag

/**
 * Extension functions for converting between AudioTag and JAudioTagger FieldKey map.
 */

/**
 * Converts AudioTag to a map of FieldKey values for JAudioTagger.
 */
fun AudioTag.toFieldKeyMap(): Map<FieldKey, String?> {
    return buildMap {
        title?.let { put(FieldKey.TITLE, it) }
        artist?.let { put(FieldKey.ARTIST, it) }
        album?.let { put(FieldKey.ALBUM, it) }
        albumArtist?.let { put(FieldKey.ALBUM_ARTIST, it) }
        composer?.let { put(FieldKey.COMPOSER, it) }
        genre?.let { put(FieldKey.GENRE, it) }
        year?.let { put(FieldKey.YEAR, it) }
        track?.let { put(FieldKey.TRACK, it) }
        discNumber?.let { put(FieldKey.DISC_NO, it) }
        comment?.let { put(FieldKey.COMMENT, it) }
        lyrics?.let { put(Field<PERSON>ey.LYRICS, it) }
    }
}

/**
 * Creates an AudioTag from JAudioTagger tag values.
 */
fun createAudioTagFromJAudioTagger(
    getValue: (FieldKey) -> String?
): AudioTag {
    return AudioTag(
        title = getValue(FieldKey.TITLE),
        artist = getValue(FieldKey.ARTIST),
        album = getValue(FieldKey.ALBUM),
        albumArtist = getValue(FieldKey.ALBUM_ARTIST),
        composer = getValue(FieldKey.COMPOSER),
        genre = getValue(FieldKey.GENRE),
        year = getValue(FieldKey.YEAR),
        track = getValue(FieldKey.TRACK),
        discNumber = getValue(FieldKey.DISC_NO),
        comment = getValue(FieldKey.COMMENT),
        lyrics = getValue(FieldKey.LYRICS)
    )
}