package soly.lyricsgenerator.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.model.Song
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

object AlbumArtUtils {
    
    // Simple memory cache for album art
    private val albumArtCache = ConcurrentHashMap<Long, Bitmap?>()
    
    /**
     * Extracts album art from the given song file
     * Returns null if no album art is found or if an error occurs
     */
    suspend fun getAlbumArt(context: Context, song: Song): Bitmap? = withContext(Dispatchers.IO) {
        try {
            // Check cache first
            albumArtCache[song.id]?.let { cachedBitmap ->
                return@withContext cachedBitmap
            }
            
            val retriever = MediaMetadataRetriever()
            var bitmap: Bitmap? = null
            
            try {
                retriever.setDataSource(song.data)
                val artBytes = retriever.embeddedPicture
                
                if (artBytes != null) {
                    bitmap = BitmapFactory.decodeByteArray(artBytes, 0, artBytes.size)
                    
                    // Scale down large images to reasonable size for performance
                    bitmap = bitmap?.let { scaleBitmapForDisplay(it) }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error extracting album art for song: ${song.title}")
            } finally {
                try {
                    retriever.release()
                } catch (e: Exception) {
                    Timber.e(e, "Error releasing MediaMetadataRetriever")
                }
            }
            
            // Cache the result (even if null)
            albumArtCache[song.id] = bitmap
            bitmap
            
        } catch (e: Exception) {
            Timber.e(e, "Error getting album art for song: ${song.title}")
            null
        }
    }
    
    /**
     * Scales bitmap to a reasonable size for display in song items
     */
    private fun scaleBitmapForDisplay(bitmap: Bitmap, maxSize: Int = 200): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // If already small enough, return as-is
        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }
        
        // Calculate scaling factor
        val scale = minOf(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * Clears the album art cache to free memory
     */
    fun clearCache() {
        albumArtCache.clear()
    }
    
    /**
     * Gets the current cache size
     */
    fun getCacheSize(): Int = albumArtCache.size
}