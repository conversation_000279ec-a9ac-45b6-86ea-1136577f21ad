//package soly.lyricsgenerator.utils
//
//import android.content.Context
//import android.net.Uri
//import android.os.Environment
//import android.util.Log
//import androidx.annotation.OptIn
//import androidx.media3.common.util.UnstableApi
//import com.arthenica.ffmpegkit.FFmpegKit
//import com.arthenica.ffmpegkit.FFmpegKitConfig
//import com.arthenica.ffmpegkit.ReturnCode
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.withContext
//import soly.lyricsgenerator.R
//import java.io.File
//
//object VideoExporter {
//
//    private const val TAG = "DEBUG_FLOW_VideoExporter"
//
//    @OptIn(UnstableApi::class)
//    suspend fun exportVideoWithLyrics(
//        context: Context,
//        videoUri: Uri, // Input video URI
//        lyricsLines: List<Pair<Long, String>>, // Timestamp (ms) and text
//        outputFileName: String
//    ): Uri? = withContext(Dispatchers.IO) {
//        Log.d(TAG, "Starting video export for: $videoUri")
//
//        // 1. Get safe output file path
//        val outputDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
//        if (outputDir == null) {
//            Log.e(TAG, "Failed to get external movies directory.")
//            return@withContext null
//        }
//        val outputFile = File(outputDir, "$outputFileName.mp4")
//        Log.d(TAG, "Output file path: ${outputFile.absolutePath}")
//
//        // Ensure FFmpegKit is initialized (optional, often done in Application class)
//        FFmpegKitConfig.enableLogCallback { log -> Log.d("FFMPEG_LOG", log.message) }
//        FFmpegKitConfig.enableStatisticsCallback { stats -> Log.d(TAG, "FFmpeg Stats: ${stats.time}") }
//
//        // 2. Generate ASS subtitle content
//        val assContent = generateAssSubtitles(lyricsLines)
//        val assFile = File(context.cacheDir, "lyrics.ass")
//        try {
//            assFile.writeText(assContent)
//            Log.d(TAG, "ASS subtitle file created at: ${assFile.absolutePath}")
//        } catch (e: Exception) {
//            Log.e(TAG, "Failed to write ASS file", e)
//            return@withContext null
//        }
//
//        // 3. Construct FFmpeg command
//        val inputVideoPath = FFmpegKitConfig.getSafParameterForRead(context, videoUri)
//        val assFilePath = assFile.absolutePath
//        val outputFilePath = outputFile.absolutePath
//
//        // Command to overlay subtitles. Adjust filter complex as needed for styling.
//        // Using 'force_style' might be needed depending on ASS specifics.
//        // Make sure paths are quoted if they contain spaces.
//        val ffmpegCommand = "-i \"$inputVideoPath\" -vf \"subtitles='$assFilePath':force_style='FontName=Arial,FontSize=24,PrimaryColour=&H00FFFFFF,OutlineColour=&H00000000,BorderStyle=1,Outline=1,Shadow=0'\" -c:a copy \"$outputFilePath\""
//        // Example with simpler subtitles filter:
//        // val ffmpegCommand = "-i \"$inputVideoPath\" -vf subtitles='$assFilePath' -c:a copy \"$outputFilePath\""
//
//        Log.d(TAG, "Executing FFmpeg command: $ffmpegCommand")
//
//        // 4. Execute FFmpeg command
//        val session = FFmpegKit.execute(ffmpegCommand)
//
//        Log.d(TAG, "FFmpeg execution finished. Return code: ${session.returnCode}")
//
//        // 5. Clean up ASS file
//        assFile.delete()
//        Log.d(TAG, "Cleaned up temporary ASS file.")
//
//        // 6. Check result and return output URI
//        if (ReturnCode.isSuccess(session.returnCode)) {
//            Log.d(TAG, "Video export successful: ${outputFile.absolutePath}")
//            // We need to return a Uri that other apps can access.
//            // For simplicity, returning the File URI, but MediaStore or FileProvider is better for sharing.
//            return@withContext Uri.fromFile(outputFile) // Or use FileProvider.getUriForFile
//        } else {
//            Log.e(TAG, "FFmpeg execution failed. Return code: ${session.returnCode}")
//            Log.e(TAG, "FFmpeg logs: ${session.allLogsAsString}")
//            // Clean up potentially incomplete output file
//            outputFile.delete()
//            return@withContext null
//        }
//    }
//
//    private fun generateAssSubtitles(lyricsLines: List<Pair<Long, String>>): String {
//        val header = """[Script Info]
//Title: Lyrics Subtitles
//ScriptType: v4.00+
//WrapStyle: 0
//PlayResX: 384
//PlayResY: 288
//ScaledBorderAndShadow: yes
//YCbCr Matrix: None
//
//[V4+ Styles]
//Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
//Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,1,1,2,10,10,10,1
//
//[Events]
//Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
//""".trimIndent()
//
//        val events = lyricsLines.mapIndexed { index, pair ->
//            val startTimeMs = pair.first
//            val text = pair.second.replace("\n", "\\N") // Replace newline with ASS newline
//            val startTimeFormatted = formatTimeToAss(startTimeMs)
//            // Calculate end time based on the start time of the next line, or add a default duration
//            val endTimeMs = lyricsLines.getOrNull(index + 1)?.first ?: (startTimeMs + 5000) // Default 5s duration
//            val endTimeFormatted = formatTimeToAss(endTimeMs)
//
//            "Dialogue: 0,$startTimeFormatted,$endTimeFormatted,Default,,0,0,0,,$text"
//        }.joinToString("\n")
//
//        return "$header\n$events"
//    }
//
//    private fun formatTimeToAss(timeMs: Long): String {
//        val totalSeconds = timeMs / 1000
//        val hours = totalSeconds / 3600
//        val minutes = (totalSeconds % 3600) / 60
//        val seconds = totalSeconds % 60
//        val centiseconds = (timeMs % 1000) / 10 // ASS uses 1/100th of a second
//        return String.format("%d:%02d:%02d.%02d", hours, minutes, seconds, centiseconds)
//    }
//}
