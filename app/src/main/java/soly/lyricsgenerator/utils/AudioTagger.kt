package soly.lyricsgenerator.utils

import android.content.Context
import android.media.MediaMetadataRetriever
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.annotation.RequiresApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import soly.lyricsgenerator.domain.model.AudioFormat
import soly.lyricsgenerator.domain.model.AudioTag
import soly.lyricsgenerator.domain.model.AudioTagResult
import soly.lyricsgenerator.domain.model.WriteInfo
import soly.lyricsgenerator.domain.model.ArtworkInfo
import soly.lyricsgenerator.domain.model.SaveTagsResult
import soly.lyricsgenerator.utils.createAudioTagFromJAudioTagger
import timber.log.Timber
import com.arthenica.mobileffmpeg.FFmpeg
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.ExecuteCallback
import org.jaudiotagger.audio.AudioFileIO
import org.jaudiotagger.audio.AudioFile
import org.jaudiotagger.tag.FieldKey
import org.jaudiotagger.tag.TagException
import org.jaudiotagger.tag.images.AndroidArtwork
import org.jaudiotagger.tag.images.Artwork
import java.io.File
import java.io.IOException

/**
 * Utility class for reading and writing audio metadata tags.
 * Uses Android's MediaMetadataRetriever for reading and FFmpeg for writing.
 * 
 * Supports multiple audio formats: MP3, FLAC, OGG, M4A, WAV, AIFF, WMA.
 * 
 * This class follows the project's sealed class pattern and SOLID principles.
 * All operations use polymorphic dispatch instead of when expressions.
 */
object AudioTagger {
    
    private const val TAG = "DEBUG_FLOW_AudioTagger"
    
    /**
     * Detects the audio format based on file extension.
     * Uses polymorphic dispatch with sealed classes.
     */
    private fun detectAudioFormat(filePath: String): AudioFormat {
        val extension = File(filePath).extension.lowercase()
        return listOf(
            AudioFormat.MP3,
            AudioFormat.FLAC, 
            AudioFormat.OGG,
            AudioFormat.M4A,
            AudioFormat.WAV,
            AudioFormat.AIFF,
            AudioFormat.WMA
        ).find { format ->
            when (format) {
                is AudioFormat.MP3 -> extension == "mp3"
                is AudioFormat.FLAC -> extension == "flac"
                is AudioFormat.OGG -> extension in listOf("ogg", "oga")
                is AudioFormat.M4A -> extension in listOf("m4a", "mp4", "aac")
                is AudioFormat.WAV -> extension == "wav"
                is AudioFormat.AIFF -> extension in listOf("aiff", "aif")
                is AudioFormat.WMA -> extension == "wma"
            }
        } ?: AudioFormat.MP3 // Default fallback
    }
    
    /**
     * Reads audio metadata tags from a file using both MediaMetadataRetriever and JAudioTagger.
     * MediaMetadataRetriever provides basic metadata, JAudioTagger provides extended metadata including lyrics.
     * Returns AudioTagResult with polymorphic dispatch.
     */
    suspend fun readAudioTags(
        context: Context,
        filePath: String
    ): AudioTagResult<AudioTag> = withContext(Dispatchers.IO) {
        try {
            Timber.tag(TAG).d("Reading audio tags from: $filePath")
            
            val file = File(filePath)
            if (!file.exists()) {
                return@withContext AudioTagResult.Error(
                    IOException("File not found"),
                    "File does not exist: $filePath"
                )
            }
            
            // First try to read basic metadata with MediaMetadataRetriever
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(filePath)
            
            val basicAudioTag = AudioTag(
                title = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE),
                artist = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST),
                album = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ALBUM),
                track = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CD_TRACK_NUMBER),
                genre = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_GENRE),
                year = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_YEAR),
                albumArtist = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ALBUMARTIST),
                composer = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_COMPOSER),
                duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull()
            )
            
            retriever.release()
            
            // Then try to read extended metadata including lyrics with JAudioTagger
            val audioTag = try {
                val audioFile = AudioFileIO.read(file)
                val tag = audioFile.tag
                
                if (tag != null) {
                    // Merge basic metadata with extended metadata from JAudioTagger
                    val extendedAudioTag = createAudioTagFromJAudioTagger { fieldKey ->
                        try {
                            tag.getFirst(fieldKey)?.takeIf { it.isNotBlank() }
                        } catch (e: Exception) {
                            Timber.tag(TAG).w("Failed to read field $fieldKey: ${e.message}")
                            null
                        }
                    }
                    
                    // Combine data - prefer JAudioTagger values when available, fall back to MediaMetadataRetriever
                    basicAudioTag.copy(
                        title = extendedAudioTag.title ?: basicAudioTag.title,
                        artist = extendedAudioTag.artist ?: basicAudioTag.artist,
                        album = extendedAudioTag.album ?: basicAudioTag.album,
                        track = extendedAudioTag.track ?: basicAudioTag.track,
                        genre = extendedAudioTag.genre ?: basicAudioTag.genre,
                        year = extendedAudioTag.year ?: basicAudioTag.year,
                        albumArtist = extendedAudioTag.albumArtist ?: basicAudioTag.albumArtist,
                        composer = extendedAudioTag.composer ?: basicAudioTag.composer,
                        comment = extendedAudioTag.comment,
                        lyrics = extendedAudioTag.lyrics,  // This is the key field that was missing!
                        discNumber = extendedAudioTag.discNumber,
                        isrc = extendedAudioTag.isrc,
                        musicBrainzId = extendedAudioTag.musicBrainzId
                    )
                } else {
                    Timber.tag(TAG).d("No JAudioTagger tag found, using MediaMetadataRetriever only")
                    basicAudioTag
                }
            } catch (e: Exception) {
                Timber.tag(TAG).w("JAudioTagger failed, using MediaMetadataRetriever only: ${e.message}")
                basicAudioTag
            }
            
            // Add detailed lyrics debugging
            val lyricsInfo = when {
                audioTag.lyrics?.isNotBlank() == true -> "present (${audioTag.lyrics!!.length} chars) - First 100 chars: ${audioTag.lyrics!!.take(100)}"
                audioTag.lyrics?.isEmpty() == true -> "empty string"
                audioTag.lyrics == null -> "null"
                else -> "blank/whitespace only"
            }
            Timber.tag(TAG).d("Successfully read audio tags - lyrics: $lyricsInfo")
            AudioTagResult.Success(audioTag, "Tags read successfully")
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Exception reading audio tags")
            AudioTagResult.Error(e, "Error reading audio metadata: ${e.message}")
        }
    }
    
    /**
     * Writes audio metadata tags to files using JAudioTagger.
     * For Android R+, this requires MediaStore permission.
     * For older versions, it writes directly if the app has permission.
     */
    suspend fun writeTagsToFiles(
        context: Context,
        info: WriteInfo
    ): AudioTagResult<List<String>> = withContext(Dispatchers.IO) {
        try {
            Timber.tag(TAG).d("Writing tags to ${info.paths.size} files")
            
            val albumArtFile = createAlbumArtThumbFile(context)
            val artwork = createArtwork(albumArtFile, info.artworkInfo)
            
            var wroteArtwork = false
            var deletedArtwork = false
            val modifiedPaths = mutableListOf<String>()
            
            for (filePath in info.paths) {
                // Decode URL encoded characters in file path
                val decodedPath = try {
                    java.net.URLDecoder.decode(filePath, "UTF-8")
                } catch (e: Exception) {
                    filePath.replace("%20", " ").replace("+", " ")
                }
                
                Timber.tag(TAG).d("Original path: $filePath")
                Timber.tag(TAG).d("Decoded path: $decodedPath")
                
                val originFile = File(decodedPath)
                if (!originFile.exists()) {
                    Timber.tag(TAG).e("Source file does not exist: $decodedPath")
                    continue
                }
                
                val result = createAudioFile(
                    originFile,
                    artwork,
                    info
                ) { wrote: Boolean, deleted: Boolean ->
                    wroteArtwork = wrote
                    deletedArtwork = deleted
                }
                
                if (result != null) {
                    // For Android R+ we need different handling
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        // Files will be written later after permission is granted
                        // For now just track that we prepared the file
                        modifiedPaths.add(decodedPath)
                    } else {
                        // For older Android versions, write directly
                        try {
                            result.commit()
                            modifiedPaths.add(decodedPath)
                        } catch (e: Exception) {
                            Timber.tag(TAG).e(e, "Failed to write tags to: $decodedPath")
                        }
                    }
                }
            }
            
            updateMediaStore(context, albumArtFile, info.artworkInfo, wroteArtwork, deletedArtwork)
            
            if (modifiedPaths.isNotEmpty()) {
                // Scan files to update MediaStore
                MediaScannerConnection.scanFile(
                    context,
                    modifiedPaths.toTypedArray(),
                    null,
                    null
                )
            }
            
            AudioTagResult.Success(
                modifiedPaths,
                "Tags written to ${modifiedPaths.size} of ${info.paths.size} files"
            )
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to write tags")
            AudioTagResult.Error(e, "Error writing tags: ${e.message}")
        }
    }
    
    /**
     * Creates cache files with updated tags for Android R+.
     * These cache files will be used to overwrite originals after permission is granted.
     */
    @RequiresApi(Build.VERSION_CODES.R)
    suspend fun writeTagsToFilesR(
        context: Context,
        info: WriteInfo
    ): SaveTagsResult = withContext(Dispatchers.IO) {
        try {
            val cacheFiles = mutableListOf<File>()
            
            val albumArtFile = createAlbumArtThumbFile(context)
            val artwork = createArtwork(albumArtFile, info.artworkInfo)
            
            var wroteArtwork = false
            var deletedArtwork = false
            
            for (filePath in info.paths) {
                // Decode URL encoded characters in file path
                val decodedPath = try {
                    java.net.URLDecoder.decode(filePath, "UTF-8")
                } catch (e: Exception) {
                    filePath.replace("%20", " ").replace("+", " ")
                }
                
                Timber.tag(TAG).d("Original path: $filePath")
                Timber.tag(TAG).d("Decoded path: $decodedPath")
                
                val originFile = File(decodedPath)
                Timber.tag(TAG).d("File exists: ${originFile.exists()}")
                
                if (!originFile.exists()) {
                    Timber.tag(TAG).e("Source file does not exist: $decodedPath")
                    return@withContext SaveTagsResult(
                        isLoading = false,
                        isSuccess = false,
                        cacheFiles = null
                    )
                }
                
                val cacheFile = File(context.cacheDir, originFile.name)
                cacheFiles.add(cacheFile)
                
                // Copy original to cache
                originFile.copyTo(cacheFile, true)
                
                val audioFile = createAudioFile(
                    cacheFile,
                    artwork,
                    info
                ) { wrote: Boolean, deleted: Boolean ->
                    wroteArtwork = wrote
                    deletedArtwork = deleted
                }
                
                audioFile?.commit()
            }
            
            updateMediaStore(context, albumArtFile, info.artworkInfo, wroteArtwork, deletedArtwork)
            
            SaveTagsResult(
                isLoading = false,
                isSuccess = true,
                cacheFiles = cacheFiles
            )
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to create cache files")
            SaveTagsResult(
                isLoading = false,
                isSuccess = false,
                cacheFiles = null
            )
        }
    }
    
    /**
     * Exports audio file with updated tags directly to user-selected location via SAF.
     * This is the main function for saving tagged files.
     */
    suspend fun exportTaggedFile(
        context: Context,
        sourceFilePath: String,
        outputUri: android.net.Uri,
        audioTag: AudioTag
    ): AudioTagResult<AudioTag> = withContext(Dispatchers.IO) {
        try {
            Timber.d("DEBUG_FLOW: AudioTagger - exportTaggedFile called")
            Timber.d("DEBUG_FLOW: AudioTagger - source path: $sourceFilePath")
            Timber.d("DEBUG_FLOW: AudioTagger - output URI: $outputUri")
            Timber.d("DEBUG_FLOW: AudioTagger - tags: title=${audioTag.title}, artist=${audioTag.artist}, album=${audioTag.album}")
            
            // Decode URL encoded characters in file path
            val decodedPath = try {
                // Handle common URL encodings: %20 for space, + for space, etc.
                val decoded = java.net.URLDecoder.decode(sourceFilePath, "UTF-8")
                Timber.d("DEBUG_FLOW: AudioTagger - path decoded successfully")
                Timber.d("DEBUG_FLOW: AudioTagger - original: $sourceFilePath")
                Timber.d("DEBUG_FLOW: AudioTagger - decoded: $decoded")
                decoded
            } catch (e: Exception) {
                // If URL decoding fails, try manual replacements for common encodings
                val manualDecoded = sourceFilePath
                    .replace("%20", " ")
                    .replace("+", " ")
                Timber.w("DEBUG_FLOW: AudioTagger - URL decode failed, trying manual: original='$sourceFilePath', manual='$manualDecoded'")
                if (manualDecoded != sourceFilePath) {
                    manualDecoded
                } else {
                    sourceFilePath
                }
            }
            
            val sourceFile = File(decodedPath)
            Timber.d("DEBUG_FLOW: AudioTagger - checking file existence at: ${sourceFile.absolutePath}")
            Timber.d("DEBUG_FLOW: AudioTagger - file exists: ${sourceFile.exists()}, isFile: ${sourceFile.isFile}, canRead: ${sourceFile.canRead()}, length: ${sourceFile.length()}")
            
            if (!sourceFile.exists()) {
                Timber.w("DEBUG_FLOW: AudioTagger - decoded path file not found, trying original path")
                // Try original path as fallback
                val originalFile = File(sourceFilePath)
                Timber.d("DEBUG_FLOW: AudioTagger - checking original file at: ${originalFile.absolutePath}")
                Timber.d("DEBUG_FLOW: AudioTagger - original exists: ${originalFile.exists()}")
                
                if (!originalFile.exists()) {
                    Timber.e("DEBUG_FLOW: AudioTagger - BOTH FILE PATHS FAILED!")
                    Timber.e("DEBUG_FLOW: AudioTagger - decoded: $decodedPath")
                    Timber.e("DEBUG_FLOW: AudioTagger - original: $sourceFilePath")
                    return@withContext AudioTagResult.Error(
                        IOException("File not found"),
                        "Source file does not exist at: $decodedPath or $sourceFilePath"
                    )
                } else {
                    Timber.d("DEBUG_FLOW: AudioTagger - using original path for export")
                    // Use original path for export
                    val tempFile = File(context.cacheDir, "export_temp_${System.currentTimeMillis()}_${originalFile.name}")
                    val command = buildFFmpegTagCommand(sourceFilePath, tempFile.absolutePath, audioTag)
                    
                    Timber.tag(TAG).d("FFmpeg export command (original path): $command")
                    val returnCode = FFmpeg.execute(command)
                    
                    if (returnCode == Config.RETURN_CODE_SUCCESS) {
                        try {
                            context.contentResolver.openOutputStream(outputUri)?.use { outputStream ->
                                tempFile.inputStream().use { inputStream ->
                                    inputStream.copyTo(outputStream)
                                }
                            }
                            tempFile.delete()
                            return@withContext AudioTagResult.Success(audioTag, "File exported successfully")
                        } catch (e: Exception) {
                            tempFile.delete()
                            return@withContext AudioTagResult.Error(e, "Failed to export file: ${e.message}")
                        }
                    } else {
                        tempFile.delete()
                        return@withContext AudioTagResult.Error(
                            RuntimeException("FFmpeg execution failed"),
                            "Failed to apply tags during export"
                        )
                    }
                }
            }
            
            // Create temporary file with tags applied (using decoded path)
            val tempFile = File(context.cacheDir, "export_temp_${System.currentTimeMillis()}_${sourceFile.name}")
            
            // Build FFmpeg command with metadata tags
            val command = buildFFmpegTagCommand(decodedPath, tempFile.absolutePath, audioTag)
            
            Timber.tag(TAG).d("FFmpeg export command: $command")
            
            // Execute FFmpeg command using mobile-ffmpeg
            val returnCode = FFmpeg.execute(command)
            
            if (returnCode == Config.RETURN_CODE_SUCCESS) {
                // Copy the tagged file to user-selected location
                try {
                    context.contentResolver.openOutputStream(outputUri)?.use { outputStream ->
                        tempFile.inputStream().use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                    
                    tempFile.delete()
                    Timber.tag(TAG).d("Successfully exported tagged file to: $outputUri")
                    AudioTagResult.Success(audioTag, "File exported successfully")
                } catch (e: Exception) {
                    tempFile.delete()
                    Timber.tag(TAG).e(e, "Failed to copy file to selected location")
                    AudioTagResult.Error(e, "Failed to export file: ${e.message}")
                }
            } else {
                tempFile.delete()
                val errorOutput = Config.getLastCommandOutput()
                Timber.tag(TAG).e("FFmpeg export failed with return code: $returnCode\nOutput: $errorOutput")
                AudioTagResult.Error(
                    RuntimeException("FFmpeg execution failed"),
                    "Failed to apply tags during export: FFmpeg returned code $returnCode"
                )
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Exception during file export")
            AudioTagResult.Error(e, "Error exporting tagged file: ${e.message}")
        }
    }

    /**
     * Builds FFmpeg command for writing metadata tags.
     * Uses proper escaping for special characters.
     */
    private fun buildFFmpegTagCommand(inputPath: String, outputPath: String, audioTag: AudioTag): String {
        val metadataArgs = mutableListOf<String>()
        
        // Add metadata arguments based on available tags
        audioTag.title?.let { metadataArgs.add("-metadata title=\"${escapeFFmpegString(it)}\"") }
        audioTag.artist?.let { metadataArgs.add("-metadata artist=\"${escapeFFmpegString(it)}\"") }
        audioTag.album?.let { metadataArgs.add("-metadata album=\"${escapeFFmpegString(it)}\"") }
        audioTag.albumArtist?.let { metadataArgs.add("-metadata album_artist=\"${escapeFFmpegString(it)}\"") }
        audioTag.composer?.let { metadataArgs.add("-metadata composer=\"${escapeFFmpegString(it)}\"") }
        audioTag.genre?.let { metadataArgs.add("-metadata genre=\"${escapeFFmpegString(it)}\"") }
        audioTag.year?.let { metadataArgs.add("-metadata date=\"${escapeFFmpegString(it)}\"") }
        audioTag.track?.let { metadataArgs.add("-metadata track=\"${escapeFFmpegString(it)}\"") }
        audioTag.discNumber?.let { metadataArgs.add("-metadata disc=\"${escapeFFmpegString(it)}\"") }
        audioTag.comment?.let { metadataArgs.add("-metadata comment=\"${escapeFFmpegString(it)}\"") }
        audioTag.isrc?.let { metadataArgs.add("-metadata isrc=\"${escapeFFmpegString(it)}\"") }
        audioTag.musicBrainzId?.let { metadataArgs.add("-metadata musicbrainz_trackid=\"${escapeFFmpegString(it)}\"") }
        
        // Build complete command
        return "-i \"$inputPath\" -map 0 -c copy ${metadataArgs.joinToString(" ")} -y \"$outputPath\""
    }
    
    /**
     * Escapes special characters for FFmpeg command line.
     */
    private fun escapeFFmpegString(input: String): String {
        return input
            .replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
    }
    
    /**
     * Creates album art thumbnail file in cache directory.
     */
    private fun createAlbumArtThumbFile(context: Context): File {
        return File(context.cacheDir, "album_art_thumb_${System.currentTimeMillis()}.jpg")
    }
    
    /**
     * Creates artwork object from bitmap.
     */
    private fun createArtwork(albumArtFile: File, artworkInfo: ArtworkInfo?): Artwork? {
        return try {
            if (artworkInfo?.artwork != null) {
                // Save bitmap to file
                albumArtFile.outputStream().use { out ->
                    artworkInfo.artwork.compress(android.graphics.Bitmap.CompressFormat.JPEG, 100, out)
                }
                AndroidArtwork.createArtworkFromFile(albumArtFile)
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to create artwork")
            null
        }
    }
    
    /**
     * Creates an AudioFile with updated tags.
     */
    private fun createAudioFile(
        source: File,
        artwork: Artwork?,
        info: WriteInfo,
        artworkResult: (Boolean, Boolean) -> Unit
    ): AudioFile? {
        return try {
            val audioFile = AudioFileIO.read(source)
            val tag = audioFile.tagOrCreateAndSetDefault
            
            // Update field values from WriteInfo
            info.values?.forEach { (key, newValue) ->
                val currentValue = try {
                    tag.getFirst(key)
                } catch (e: Exception) {
                    null
                }
                
                if (currentValue != newValue) {
                    if (newValue.isNullOrEmpty()) {
                        tag.deleteField(key)
                    } else {
                        tag.setField(key, newValue)
                    }
                }
            }
            
            // Handle artwork
            if (info.artworkInfo != null) {
                if (info.artworkInfo.artwork == null) {
                    tag.deleteArtworkField()
                    artworkResult(false, true)
                } else if (artwork != null) {
                    tag.deleteArtworkField()
                    tag.setField(artwork)
                    artworkResult(true, false)
                }
            }
            
            audioFile
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to create audio file: ${source.absolutePath}")
            null
        }
    }
    
    /**
     * Updates MediaStore after modifying artwork.
     */
    private fun updateMediaStore(
        context: Context,
        albumArtFile: File,
        artworkInfo: ArtworkInfo?,
        wroteArtwork: Boolean,
        deletedArtwork: Boolean
    ) {
        if (artworkInfo != null) {
            if (wroteArtwork) {
                // Update album art in MediaStore
                try {
                    val values = android.content.ContentValues().apply {
                        put(MediaStore.Audio.Albums.ALBUM_ART, albumArtFile.path)
                    }
                    context.contentResolver.update(
                        MediaStore.Audio.Albums.EXTERNAL_CONTENT_URI,
                        values,
                        "${MediaStore.Audio.Albums._ID} = ?",
                        arrayOf(artworkInfo.albumId.toString())
                    )
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to update album art in MediaStore")
                }
            } else if (deletedArtwork) {
                // Remove album art from MediaStore
                try {
                    val values = android.content.ContentValues().apply {
                        putNull(MediaStore.Audio.Albums.ALBUM_ART)
                    }
                    context.contentResolver.update(
                        MediaStore.Audio.Albums.EXTERNAL_CONTENT_URI,
                        values,
                        "${MediaStore.Audio.Albums._ID} = ?",
                        arrayOf(artworkInfo.albumId.toString())
                    )
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to delete album art from MediaStore")
                }
            }
        }
    }
    
    /**
     * Persists changes from cache files to original locations.
     * Used after MediaStore permission is granted on Android R+.
     */
    suspend fun persistChanges(
        context: Context,
        paths: List<String>,
        destUris: List<Uri>,
        cacheFiles: List<File>
    ) = withContext(Dispatchers.IO) {
        if (cacheFiles.size == destUris.size) {
            for (i in cacheFiles.indices) {
                try {
                    context.contentResolver.openOutputStream(destUris[i])?.use { outputStream ->
                        cacheFiles[i].inputStream().use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to persist changes for: ${paths[i]}")
                }
            }
        }
        
        // Scan files to update MediaStore
        MediaScannerConnection.scanFile(
            context,
            paths.toTypedArray(),
            null,
            null
        )
    }
    
    /**
     * Embeds LRC lyrics content into an audio file using FFmpeg.
     * Can embed as synchronized lyrics (SYLT) or plain text lyrics (USLT).
     */
    suspend fun embedLyrics(
        context: Context,
        filePath: String,
        lrcContent: String,
        embedSyncedLyrics: Boolean = true
    ): AudioTagResult<AudioTag> = withContext(Dispatchers.IO) {
        try {
            Timber.tag(TAG).d("Embedding lyrics to: $filePath")
            
            val file = File(filePath)
            if (!file.exists()) {
                return@withContext AudioTagResult.Error(
                    IOException("File not found"),
                    "File does not exist: $filePath"
                )
            }
            
            // Create backup in cache dir to avoid permission issues
            val backupFileName = "backup_lyrics_${System.currentTimeMillis()}_${file.name}"
            val backupFile = File(context.cacheDir, backupFileName)
            file.copyTo(backupFile, overwrite = true)
            Timber.tag(TAG).d("Created backup at: ${backupFile.absolutePath}")
            
            // Create temporary output file
            val tempFile = File(context.cacheDir, "temp_lyrics_${System.currentTimeMillis()}_${file.name}")
            
            // Prepare lyrics content
            val lyricsText = if (embedSyncedLyrics) {
                // For synced lyrics, we need to convert LRC to a format FFmpeg understands
                // FFmpeg doesn't directly support SYLT frames, so we'll embed as plain text
                // with a comment indicating it's synchronized
                "[Synchronized Lyrics]\n$lrcContent"
            } else {
                // Convert LRC to plain text for unsynchronized lyrics
                convertLrcToPlainText(lrcContent)
            }
            
            // Build FFmpeg command
            val command = buildFFmpegLyricsCommand(filePath, tempFile.absolutePath, lyricsText)
            
            Timber.tag(TAG).d("FFmpeg command: $command")
            
            // Execute FFmpeg command using mobile-ffmpeg
            val returnCode = FFmpeg.execute(command)
            
            if (returnCode == Config.RETURN_CODE_SUCCESS) {
                // Check if we can write to the original location (internal storage)
                val isInternalStorage = file.absolutePath.startsWith(context.filesDir.absolutePath) || 
                                       file.absolutePath.startsWith(context.cacheDir.absolutePath)
                
                if (isInternalStorage) {
                    // For internal storage files, replace directly
                    try {
                        tempFile.copyTo(file, overwrite = true)
                        tempFile.delete()
                        backupFile.delete()
                        
                        // Read the updated tags to return
                        val updatedTags = readAudioTags(context, filePath)
                        
                        Timber.tag(TAG).d("Successfully embedded lyrics to internal storage")
                        when (updatedTags) {
                            is AudioTagResult.Success -> {
                                AudioTagResult.Success(
                                    updatedTags.data.copy(lyrics = lyricsText),
                                    "Lyrics embedded successfully"
                                )
                            }
                            else -> AudioTagResult.Success(
                                AudioTag(lyrics = lyricsText),
                                "Lyrics embedded successfully"
                            )
                        }
                    } catch (e: Exception) {
                        // Restore from backup if available
                        try {
                            backupFile.copyTo(file, overwrite = true)
                            backupFile.delete()
                        } catch (restoreException: Exception) {
                            Timber.tag(TAG).e(restoreException, "Failed to restore backup for lyrics")
                        }
                        tempFile.delete()
                        
                        Timber.tag(TAG).e(e, "Failed to replace internal file for lyrics")
                        AudioTagResult.Error(e, "Failed to embed lyrics: ${e.message}")
                    }
                } else {
                    // For external storage files, save to internal storage with modified name
                    try {
                        val fileName = file.nameWithoutExtension
                        val extension = file.extension
                        val newFileName = "${fileName}_with_lyrics.${extension}"
                        val internalFile = File(context.filesDir, newFileName)
                        
                        // Copy tagged file to internal storage
                        tempFile.copyTo(internalFile, overwrite = true)
                        tempFile.delete()
                        backupFile.delete()
                        
                        // Read the updated tags to return
                        val updatedTags = readAudioTags(context, internalFile.absolutePath)
                        
                        Timber.tag(TAG).d("Saved file with lyrics to internal storage: ${internalFile.absolutePath}")
                        when (updatedTags) {
                            is AudioTagResult.Success -> {
                                AudioTagResult.Success(
                                    updatedTags.data.copy(lyrics = lyricsText),
                                    "Lyrics embedded. Use export function to save to accessible location."
                                )
                            }
                            else -> AudioTagResult.Success(
                                AudioTag(lyrics = lyricsText),
                                "Lyrics embedded. Use export function to save to accessible location."
                            )
                        }
                    } catch (e: Exception) {
                        tempFile.delete()
                        backupFile.delete()
                        
                        Timber.tag(TAG).e(e, "Failed to save lyrics to internal storage")
                        AudioTagResult.Error(e, "Failed to embed lyrics: ${e.message}")
                    }
                }
            } else {
                // Restore from backup
                backupFile.copyTo(file, overwrite = true)
                backupFile.delete()
                tempFile.delete()
                
                val errorOutput = Config.getLastCommandOutput()
                Timber.tag(TAG).e("FFmpeg failed with return code: $returnCode\nOutput: $errorOutput")
                AudioTagResult.Error(
                    RuntimeException("FFmpeg execution failed"),
                    "Failed to write tags: FFmpeg returned code $returnCode"
                )
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Exception embedding lyrics")
            AudioTagResult.Error(e, "Error embedding lyrics: ${e.message}")
        }
    }
    
    /**
     * Builds FFmpeg command for embedding lyrics.
     */
    private fun buildFFmpegLyricsCommand(inputPath: String, outputPath: String, lyrics: String): String {
        val escapedLyrics = escapeFFmpegString(lyrics)
        return "-i \"$inputPath\" -map 0 -c copy -metadata lyrics=\"$escapedLyrics\" -y \"$outputPath\""
    }
    
    /**
     * Converts LRC content to plain text by removing timestamps.
     */
    private fun convertLrcToPlainText(lrcContent: String): String {
        return lrcContent.lines()
            .map { line ->
                // Remove LRC timestamps like [00:12.34]
                line.replace(Regex("\\[\\d{2}:\\d{2}\\.\\d{2}\\]"), "").trim()
            }
            .filter { it.isNotEmpty() }
            .joinToString("\n")
    }
}