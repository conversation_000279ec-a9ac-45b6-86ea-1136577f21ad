package soly.lyricsgenerator.utils

import android.content.Context
import android.content.pm.PackageManager

object VersionUtils {
    private const val DEFAULT_VERSION = "1.0.0"
    
    fun getCurrentAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: DEFAULT_VERSION
        } catch (e: PackageManager.NameNotFoundException) {
            DEFAULT_VERSION // Fallback
        }
    }

    fun isUpdateNeeded(currentVersion: String, requiredVersion: String): Boolean {
        val currentParts = currentVersion.split('.').map { it.toIntOrNull() ?: 0 }
        val requiredParts = requiredVersion.split('.').map { it.toIntOrNull() ?: 0 }
        val length = maxOf(currentParts.size, requiredParts.size)
        
        for (i in 0 until length) {
            val current = currentParts.getOrElse(i) { 0 }
            val required = requiredParts.getOrElse(i) { 0 }
            if (current < required) {
                return true
            }
            if (current > required) {
                return false
            }
        }
        
        return false
    }
}