package soly.lyricsgenerator.utils

import soly.lyricsgenerator.BuildConfig

/**
 * Central feature flag management for the application.
 * Controls feature visibility based on build configuration.
 */
object FeatureFlags {
    
    /**
     * Determines if the LyricsSection should be shown in the Tag Editor.
     * Currently disabled as this will be a paid feature in the next release.
     * 
     * @return true if LyricsSection should be enabled, false otherwise
     */
    fun isLyricsSectionEnabled(): Boolean = BuildConfig.ENABLE_LYRICS_SECTION
}