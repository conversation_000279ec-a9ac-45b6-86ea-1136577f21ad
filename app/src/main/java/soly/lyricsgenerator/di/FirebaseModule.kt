package soly.lyricsgenerator.di

import android.content.Context
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.domain.service.UserIdentityManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object FirebaseModule {

    @Provides
    @Singleton
    fun provideFirebaseAnalytics(@ApplicationContext context: Context): FirebaseAnalytics {
        return FirebaseAnalytics.getInstance(context)
    }
    
    @Provides
    @Singleton
    fun provideUserIdentityManager(
        @ApplicationContext context: Context,
        firebaseAnalytics: FirebaseAnalytics
    ): UserIdentityManager {
        return UserIdentityManager(context, firebaseAnalytics)
    }
    
} 