package soly.lyricsgenerator

import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.repository.IMusicRepository
import soly.lyricsgenerator.domain.service.MusicService
import soly.lyricsgenerator.domain.usecase.database.song.SaveSongsUseCase

class MusicServiceTest {
    private val repository: IMusicRepository = mock()
    private val saveSongsUseCase: SaveSongsUseCase = mock()
    private val service = MusicService(repository, saveSongsUseCase)

    @Test
    fun getSongsByArtist_callsRepository() {
        runBlocking {
            val expected = listOf<Song>()
            whenever(repository.getSongsByArtist("Queen")).thenReturn(expected)

            val result = service.getSongsByArtist("Queen")

            assertEquals(expected, result)
            verify(repository).getSongsByArtist("Queen")
        }
    }
}
