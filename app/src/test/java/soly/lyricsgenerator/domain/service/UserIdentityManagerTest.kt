package soly.lyricsgenerator.domain.service

import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertFalse
import junit.framework.TestCase.assertNotNull
import junit.framework.TestCase.assertTrue
import org.junit.Test
import soly.lyricsgenerator.analytics.UserIdentityConstants
import java.util.UUID

/**
 * Unit tests for UserIdentityManager
 * 
 * Tests the core functionality and constants without requiring Android runtime dependencies.
 * Integration tests that require Android context should be in androidTest.
 */
class UserIdentityManagerTest {

    @Test
    fun `UUID generation creates valid UUID format`() {
        // Test that UUID generation creates a properly formatted UUID string
        val uuid1 = UUID.randomUUID().toString()
        val uuid2 = UUID.randomUUID().toString()
        
        // Verify UUID format (36 characters with hyphens at positions 8, 13, 18, 23)
        assertEquals(36, uuid1.length)
        assertEquals(36, uuid2.length)
        assertEquals('-', uuid1[8])
        assertEquals('-', uuid1[13])
        assertEquals('-', uuid1[18])
        assertEquals('-', uuid1[23])
        
        // Verify UUIDs are unique
        assertFalse("UUIDs should be unique", uuid1 == uuid2)
    }

    @Test
    fun `UUID format validation with regex`() {
        // Test that UUID format matches standard UUID regex pattern
        val uuid = UUID.randomUUID().toString()
        val uuidRegex = Regex("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")
        
        assertTrue("UUID should match standard format", uuid.matches(uuidRegex))
    }

    @Test
    fun `UserIdentityConstants contains required keys`() {
        // Verify that all required constants are defined and not empty
        assertNotNull("Encrypted prefs file name should be defined", 
            UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME)
        assertFalse("Encrypted prefs file name should not be empty", 
            UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME.isEmpty())
        
        assertNotNull("Installation ID key should be defined", 
            UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY)
        assertFalse("Installation ID key should not be empty", 
            UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY.isEmpty())
        
        // Verify analytics platform constants
        assertEquals("Firebase Analytics constant should match expected value", 
            "firebase_analytics", UserIdentityConstants.AnalyticsPlatforms.FIREBASE_ANALYTICS)
        assertEquals("Firebase Crashlytics constant should match expected value", 
            "firebase_crashlytics", UserIdentityConstants.AnalyticsPlatforms.FIREBASE_CRASHLYTICS)
        assertEquals("Amplitude constant should match expected value", 
            "amplitude", UserIdentityConstants.AnalyticsPlatforms.AMPLITUDE)
    }

    @Test
    fun `error messages are properly defined`() {
        // Verify that all error message constants are defined and meaningful
        val errorMessages = listOf(
            UserIdentityConstants.ErrorMessages.UUID_GENERATION_FAILED,
            UserIdentityConstants.ErrorMessages.ENCRYPTED_PREFS_CREATION_FAILED,
            UserIdentityConstants.ErrorMessages.UUID_STORAGE_FAILED,
            UserIdentityConstants.ErrorMessages.UUID_RETRIEVAL_FAILED,
            UserIdentityConstants.ErrorMessages.ANALYTICS_PROPAGATION_FAILED
        )
        
        errorMessages.forEach { errorMessage ->
            assertNotNull("Error message should not be null", errorMessage)
            assertFalse("Error message should not be empty", errorMessage.isEmpty())
            assertTrue("Error message should be meaningful (length > 10)", errorMessage.length > 10)
        }
    }

    @Test
    fun `preference keys are unique and meaningful`() {
        // Verify that preference keys are unique and follow naming conventions
        val prefFileName = UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME
        val installationIdKey = UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY
        
        assertFalse("Preference keys should be unique", prefFileName == installationIdKey)
        assertTrue("Preference file name should contain 'prefs'", prefFileName.contains("prefs"))
        assertTrue("Installation ID key should contain 'id'", installationIdKey.contains("id"))
    }

    @Test
    fun `analytics platform constants follow naming convention`() {
        // Verify that analytics platform constants follow snake_case convention
        val platforms = listOf(
            UserIdentityConstants.AnalyticsPlatforms.FIREBASE_ANALYTICS,
            UserIdentityConstants.AnalyticsPlatforms.FIREBASE_CRASHLYTICS,
            UserIdentityConstants.AnalyticsPlatforms.AMPLITUDE
        )
        
        platforms.forEach { platform ->
            assertTrue("Platform constant should be lowercase: $platform", 
                platform == platform.lowercase())
            assertFalse("Platform constant should not contain spaces: $platform", 
                platform.contains(" "))
        }
    }

    @Test
    fun `constants are not hardcoded strings`() {
        // Verify that constants follow the no-hardcoded-strings policy
        val allConstants = listOf(
            UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME,
            UserIdentityConstants.PreferenceKeys.INSTALLATION_ID_KEY,
            UserIdentityConstants.AnalyticsPlatforms.FIREBASE_ANALYTICS,
            UserIdentityConstants.AnalyticsPlatforms.FIREBASE_CRASHLYTICS,
            UserIdentityConstants.AnalyticsPlatforms.AMPLITUDE
        )
        
        allConstants.forEach { constant ->
            assertNotNull("Constant should not be null", constant)
            assertTrue("Constant should be meaningful", constant.length > 2)
            assertFalse("Constant should not be empty", constant.isEmpty())
        }
    }

    @Test
    fun `preference file name follows naming convention`() {
        val fileName = UserIdentityConstants.PreferenceKeys.ENCRYPTED_PREFS_FILE_NAME
        
        // Should contain "prefs" and "user" or "identity" for clarity
        assertTrue("File name should contain 'prefs'", fileName.contains("prefs"))
        assertTrue("File name should contain 'user' or 'identity'", 
            fileName.contains("user") || fileName.contains("identity"))
        
        // Should not contain spaces or special characters (except underscores)
        assertFalse("File name should not contain spaces", fileName.contains(" "))
        assertTrue("File name should use underscores for separation", 
            fileName.contains("_") || !fileName.contains(" "))
    }
}