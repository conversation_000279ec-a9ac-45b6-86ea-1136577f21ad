package soly.lyricsgenerator.domain.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDownward
import androidx.compose.material.icons.filled.ArrowUpward
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for SortOptions sealed classes.
 * Validates sorting functionality follows polymorphic patterns.
 */
class SortOptionsTest {

    private val sampleSong1 = Song(
        id = 1L,
        title = "Abbey Road",
        artist = "The Beatles", 
        data = "/path/to/song1",
        duration = 180000L,
        isFavorite = false,
        album = "Abbey Road",
        dateAdded = 1640995200L // Jan 1, 2022
    )
    
    private val sampleSong2 = Song(
        id = 2L,
        title = "Bohemian Rhapsody",
        artist = "Queen",
        data = "/path/to/song2", 
        duration = 355000L,
        isFavorite = true,
        album = "A Night at the Opera",
        dateAdded = 1672531200L // Jan 1, 2023
    )

    @Test
    fun sortType_title_ascending_sortsCorrectly() {
        val sortType = SortType.Title
        val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Ascending)
        
        // "Abbey Road" comes before "Bohemian Rhapsody" alphabetically
        assertTrue("Title should sort Abbey Road before Bohemian Rhapsody", result < 0)
    }

    @Test
    fun sortType_title_descending_sortsCorrectly() {
        val sortType = SortType.Title
        val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Descending)
        
        // Descending should reverse the order
        assertTrue("Title descending should sort Bohemian Rhapsody before Abbey Road", result > 0)
    }

    @Test
    fun sortType_artist_ascending_sortsCorrectly() {
        val sortType = SortType.Artist
        val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Ascending)
        
        // "The Beatles" comes after "Queen" alphabetically
        assertTrue("Artist should sort Queen before The Beatles", result > 0)
    }

    @Test
    fun sortType_album_ascending_sortsCorrectly() {
        val sortType = SortType.Album
        val result = sortType.compare(sampleSong2, sampleSong1, SortOrder.Ascending)
        
        // "A Night at the Opera" comes before "Abbey Road" alphabetically
        assertTrue("Album should sort A Night at the Opera before Abbey Road", result < 0)
    }

    @Test
    fun sortType_recentlyAdded_ascending_sortsCorrectly() {
        val sortType = SortType.RecentlyAdded
        val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Ascending)
        
        // sampleSong1 was added in 2022, sampleSong2 in 2023
        assertTrue("Recently added should sort older songs first in ascending", result < 0)
    }

    @Test
    fun sortType_recentlyAdded_descending_sortsCorrectly() {
        val sortType = SortType.RecentlyAdded
        val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Descending)
        
        // Descending should put newer songs first
        assertTrue("Recently added descending should sort newer songs first", result > 0)
    }

    @Test
    fun sortOrder_ascending_appliesCorrectly() {
        val sortOrder = SortOrder.Ascending
        assertEquals("Ascending should return comparison as-is", 5, sortOrder.apply(5))
        assertEquals("Ascending should return negative comparison as-is", -3, sortOrder.apply(-3))
    }

    @Test
    fun sortOrder_descending_appliesCorrectly() {
        val sortOrder = SortOrder.Descending
        assertEquals("Descending should reverse positive comparison", -5, sortOrder.apply(5))
        assertEquals("Descending should reverse negative comparison", 3, sortOrder.apply(-3))
    }

    @Test
    fun sortOrder_displayIcon_returnsCorrectIcons() {
        assertEquals("Ascending should show up arrow icon", Icons.Default.ArrowUpward, SortOrder.Ascending.getDisplayIcon())
        assertEquals("Descending should show down arrow icon", Icons.Default.ArrowDownward, SortOrder.Descending.getDisplayIcon())
    }

    @Test
    fun sortType_getDisplayName_returnsStringResourceIds() {
        // Verify that all sort types return valid string resource IDs
        assertTrue("Title should return valid string resource", SortType.Title.getDisplayName() > 0)
        assertTrue("Artist should return valid string resource", SortType.Artist.getDisplayName() > 0)
        assertTrue("Album should return valid string resource", SortType.Album.getDisplayName() > 0)
        assertTrue("RecentlyAdded should return valid string resource", SortType.RecentlyAdded.getDisplayName() > 0)
    }

    @Test
    fun polymorphism_noWhenExpressions_usesMethodDispatch() {
        // This test validates that we're using polymorphic method calls
        // rather than when expressions, following project guidelines
        
        val sortTypes = listOf(
            SortType.Title,
            SortType.Artist, 
            SortType.Album,
            SortType.RecentlyAdded
        )
        
        // Each type should implement its own comparison logic
        sortTypes.forEach { sortType ->
            val result = sortType.compare(sampleSong1, sampleSong2, SortOrder.Ascending)
            assertNotNull("Sort type should implement compare method", result)
            
            val displayName = sortType.getDisplayName()
            assertTrue("Sort type should implement getDisplayName method", displayName > 0)
        }
        
        val sortOrders = listOf(SortOrder.Ascending, SortOrder.Descending)
        
        sortOrders.forEach { sortOrder ->
            val result = sortOrder.apply(1)
            assertNotNull("Sort order should implement apply method", result)
            
            val icon = sortOrder.getDisplayIcon()
            assertNotNull("Sort order should implement getDisplayIcon method", icon)
        }
    }
}