package soly.lyricsgenerator.domain.usecase

import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.rules.TemporaryFolder
import soly.lyricsgenerator.domain.usecase.lyrics.ReadRtfFileUseCase

/**
 * Unit tests for ReadRtfFileUseCase RTF parsing functionality
 * Tests the RTF Parser Kit based implementation extracted for lightweight parsing
 */
class ReadRtfFileUseCaseTest {

    @get:Rule
    val tempFolder = TemporaryFolder()

    private lateinit var readRtfFileUseCase: ReadRtfFileUseCase

    @Before
    fun setup() {
        readRtfFileUseCase = ReadRtfFileUseCase()
    }

    @Test
    fun nonRtfContent_returnsEmpty() = runTest {
        // Given
        val plainText = "This is just plain text, not RTF"
        val file = tempFolder.newFile("test.rtf")
        file.writeText(plainText)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        assertTrue("Non-RTF content should return empty list", result.isEmpty())
    }

    @Test
    fun nonExistentFile_returnsEmpty() = runTest {
        // When
        val result = readRtfFileUseCase.readRtfFile("/nonexistent/path.rtf")

        // Then
        assertTrue("Non-existent file should return empty list", result.isEmpty())
    }

    @Test
    fun simpleRtfContent_extractsText() = runTest {
        // Given
        val rtfContent = """{\rtf1\ansi\deff0 Simple text content}"""
        val file = tempFolder.newFile("test.rtf")
        file.writeText(rtfContent)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        assertTrue("Should extract some content", result.isNotEmpty())
        val allText = result.joinToString(" ")
        assertTrue("Should contain the text content", allText.contains("Simple text content"))
    }

    @Test
    fun rtfWithParagraphs_extractsLines() = runTest {
        // Given
        val rtfContent = """{\rtf1\ansi\deff0 First line\par Second line\par Third line}"""
        val file = tempFolder.newFile("test.rtf")
        file.writeText(rtfContent)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        val allText = result.joinToString(" ")
        assertTrue("Should contain First line", allText.contains("First line"))
        assertTrue("Should contain Second line", allText.contains("Second line"))
        assertTrue("Should contain Third line", allText.contains("Third line"))
    }

    @Test
    fun complexRtfFromTextEdit_extractsMainContent() = runTest {
        // Given - Real RTF from macOS TextEdit (the problematic case)
        val rtfContent = """
            {\rtf1\ansi\ansicpg1252\cocoartf2822
            \cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
            {\colortbl;\red255\green255\blue255;}
            {\*\expandedcolortbl;;}
            \paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
            \pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0
            
            \f0\fs24 \cf0 Prva linija\
            Druga linija\
            Treca Linija\
            Cetvrta Linija\
            Peta  Linija}
        """.trimIndent()
        
        val file = tempFolder.newFile("test.rtf")
        file.writeText(rtfContent)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        val allText = result.joinToString(" ")
        
        // Main requirement: should extract the actual text content
        assertTrue("Should contain Prva linija", allText.contains("Prva linija"))
        assertTrue("Should contain Druga linija", allText.contains("Druga linija"))
        assertTrue("Should contain Treca Linija", allText.contains("Treca Linija"))
        assertTrue("Should contain Cetvrta Linija", allText.contains("Cetvrta Linija"))
        assertTrue("Should contain Peta  Linija", allText.contains("Peta  Linija"))
        
        // Should not contain major formatting artifacts
        assertFalse("Should not contain Helvetica", allText.contains("Helvetica"))
        
        println("Extracted content: $result")
        println("All text: $allText")
    }

    @Test
    fun rtfWithFormatting_ignoresFormattingCommands() = runTest {
        // Given
        val rtfContent = """{\rtf1\ansi\deff0 \b Bold\b0 and \i italic\i0 text}"""
        val file = tempFolder.newFile("test.rtf")
        file.writeText(rtfContent)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        val allText = result.joinToString(" ")
        assertTrue("Should contain Bold", allText.contains("Bold"))
        assertTrue("Should contain italic", allText.contains("italic"))
        assertTrue("Should contain text", allText.contains("text"))
        
        // Should not contain formatting commands
        assertFalse("Should not contain \\b", allText.contains("\\b"))
        assertFalse("Should not contain \\i", allText.contains("\\i"))
    }

    @Test
    fun malformedRtf_handlesGracefully() = runTest {
        // Given
        val malformedRtf = """{\rtf1\ansi\deff0 Text without proper closing"""
        val file = tempFolder.newFile("test.rtf")
        file.writeText(malformedRtf)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        // Should not crash, may or may not extract content
        assertNotNull("Should handle gracefully", result)
    }

    @Test
    fun rtfWithManyLines_extractsAllContent() = runTest {
        // Given - RTF with 30 lines of content
        val lines = (1..30).map { "Line number $it with some content" }
        val rtfContent = buildString {
            append("{\\rtf1\\ansi\\ansicpg1252\\cocoartf2822")
            append("\\cocoatextscaling0\\cocoaplatform0{\\fonttbl\\f0\\fswiss\\fcharset0 Helvetica;}")
            append("{\\colortbl;\\red255\\green255\\blue255;}")
            append("{\\*\\expandedcolortbl;;}")
            append("\\paperw11900\\paperh16840\\margl1440\\margr1440\\vieww11520\\viewh8400\\viewkind0")
            append("\\pard\\tx720\\tx1440\\tx2160\\tx2880\\tx3600\\tx4320\\tx5040\\tx5760\\tx6480\\tx7200\\tx7920\\tx8640\\pardirnatural\\partightenfactor0")
            append("\\f0\\fs24 \\cf0 ")
            
            lines.forEachIndexed { index, line ->
                append(line)
                if (index < lines.size - 1) {
                    append("\\")  // RTF line break
                    append("\n")
                }
            }
            append("}")
        }
        
        val file = tempFolder.newFile("test.rtf")
        file.writeText(rtfContent)

        // When
        val result = readRtfFileUseCase.readRtfFile(file.absolutePath)

        // Then
        val allText = result.joinToString(" ")
        
        // Should extract all 30 lines
        assertTrue("Should extract content from many lines", result.size >= 25) // Allow some tolerance for formatting
        
        // Verify specific lines are present
        assertTrue("Should contain Line number 1", allText.contains("Line number 1"))
        assertTrue("Should contain Line number 15", allText.contains("Line number 15"))
        assertTrue("Should contain Line number 30", allText.contains("Line number 30"))
        
        // Should contain content from first, middle, and last lines
        assertTrue("Should contain first line content", allText.contains("Line number 1 with some content"))
        assertTrue("Should contain middle line content", allText.contains("Line number 15 with some content"))
        assertTrue("Should contain last line content", allText.contains("Line number 30 with some content"))
        
        // Should not contain RTF artifacts
        assertFalse("Should not contain Helvetica", allText.contains("Helvetica"))
        
        println("30-line RTF test - Extracted ${result.size} lines")
        println("Sample extracted lines:")
        result.take(5).forEachIndexed { index, line -> 
            println("  Line $index: '$line'")
        }
        if (result.size > 10) {
            println("  ... (showing first 5 of ${result.size} total lines)")
        }
    }
}