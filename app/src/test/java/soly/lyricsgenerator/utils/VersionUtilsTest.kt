package soly.lyricsgenerator.utils

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations

class VersionUtilsTest {

    @Mock
    private lateinit var mockContext: Context
    
    @Mock
    private lateinit var mockPackageManager: PackageManager

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        `when`(mockContext.packageManager).thenReturn(mockPackageManager)
        `when`(mockContext.packageName).thenReturn("com.test.app")
    }

    @Test
    fun `getCurrentAppVersion should return version name from package info`() {
        // Given
        val expectedVersion = "2.0.51"
        val mockPackageInfo = PackageInfo().apply {
            versionName = expectedVersion
        }
        `when`(mockPackageManager.getPackageInfo("com.test.app", 0)).thenReturn(mockPackageInfo)

        // When
        val result = VersionUtils.getCurrentAppVersion(mockContext)

        // Then
        assertEquals(expectedVersion, result)
    }

    @Test
    fun `getCurrentAppVersion should return fallback when version name is null`() {
        // Given
        val mockPackageInfo = PackageInfo().apply {
            versionName = null
        }
        `when`(mockPackageManager.getPackageInfo("com.test.app", 0)).thenReturn(mockPackageInfo)

        // When
        val result = VersionUtils.getCurrentAppVersion(mockContext)

        // Then
        assertEquals("1.0.0", result)
    }

    @Test
    fun `getCurrentAppVersion should return fallback when PackageManager throws exception`() {
        // Given
        `when`(mockPackageManager.getPackageInfo("com.test.app", 0)).thenThrow(PackageManager.NameNotFoundException())

        // When
        val result = VersionUtils.getCurrentAppVersion(mockContext)

        // Then
        assertEquals("1.0.0", result)
    }

    @Test
    fun `isUpdateNeeded should return true when current version is lower than required`() {
        // Given
        val currentVersion = "2.0.50"
        val requiredVersion = "2.0.51"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should return false when current version is equal to required`() {
        // Given
        val currentVersion = "2.0.51"
        val requiredVersion = "2.0.51"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertFalse(result)
    }

    @Test
    fun `isUpdateNeeded should return false when current version is higher than required`() {
        // Given
        val currentVersion = "2.0.52"
        val requiredVersion = "2.0.51"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertFalse(result)
    }

    @Test
    fun `isUpdateNeeded should handle major version differences`() {
        // Given
        val currentVersion = "1.9.99"
        val requiredVersion = "2.0.0"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should handle minor version differences`() {
        // Given
        val currentVersion = "2.0.99"
        val requiredVersion = "2.1.0"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should handle patch version differences`() {
        // Given
        val currentVersion = "2.1.5"
        val requiredVersion = "2.1.10"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should handle different version part lengths - current shorter`() {
        // Given
        val currentVersion = "2.0"
        val requiredVersion = "2.0.1"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should handle different version part lengths - required shorter`() {
        // Given
        val currentVersion = "2.0.1"
        val requiredVersion = "2.0"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertFalse(result)
    }

    @Test
    fun `isUpdateNeeded should handle invalid version parts as zero`() {
        // Given
        val currentVersion = "2.0.abc"
        val requiredVersion = "2.0.1"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result) // "abc" becomes 0, so 2.0.0 < 2.0.1
    }

    @Test
    fun `isUpdateNeeded should handle single digit versions`() {
        // Given
        val currentVersion = "1"
        val requiredVersion = "2"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isUpdateNeeded should handle empty version parts correctly`() {
        // Given
        val currentVersion = "2..1"
        val requiredVersion = "2.0.1"

        // When
        val result = VersionUtils.isUpdateNeeded(currentVersion, requiredVersion)

        // Then
        assertFalse(result) // Empty string becomes 0, so 2.0.1 == 2.0.1
    }

    @Test
    fun `isUpdateNeeded should handle complex version comparison scenarios`() {
        // Test multiple scenarios
        assertTrue(VersionUtils.isUpdateNeeded("1.0.0", "1.0.1"))
        assertTrue(VersionUtils.isUpdateNeeded("1.0.0", "1.1.0"))
        assertTrue(VersionUtils.isUpdateNeeded("1.0.0", "2.0.0"))
        assertFalse(VersionUtils.isUpdateNeeded("1.0.1", "1.0.0"))
        assertFalse(VersionUtils.isUpdateNeeded("1.1.0", "1.0.0"))
        assertFalse(VersionUtils.isUpdateNeeded("2.0.0", "1.0.0"))
        assertFalse(VersionUtils.isUpdateNeeded("1.0.0", "1.0.0"))
    }
}