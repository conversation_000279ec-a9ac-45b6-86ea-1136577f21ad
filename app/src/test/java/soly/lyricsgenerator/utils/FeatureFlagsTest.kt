package soly.lyricsgenerator.utils

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for FeatureFlags utility.
 * Verifies feature flag functionality for controlled feature rollout.
 */
class FeatureFlagsTest {

    @Test
    fun `test isLyricsSectionEnabled returns false for current build`() {
        // Verify that lyrics section is currently disabled
        // This reflects the business requirement to hide the feature until it becomes paid
        assertFalse("LyricsSection should be disabled in current build", 
                   FeatureFlags.isLyricsSectionEnabled())
    }
}