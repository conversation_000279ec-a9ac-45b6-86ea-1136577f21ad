package soly.lyricsgenerator

import org.junit.Test
import org.junit.Assert.*

/**
 * Test to validate SQLite batching logic prevents "too many SQL variables" error.
 * 
 * This test simulates the logic used in MusicRepository.getAllSongs() to ensure
 * that large lists of song IDs are properly batched to avoid exceeding SQLite's
 * 999 variable limit.
 */
class SqliteBatchingTest {

    companion object {
        private const val SQLITE_VARIABLE_LIMIT = 999
        private const val SAFE_BATCH_SIZE = 500
    }

    @Test
    fun testBatchingLogic_withLargeSongList() {
        // Simulate a large list of song IDs (1000 songs, which would cause the crash)
        val songIds = (1L..1000L).toList()
        val batchSize = SAFE_BATCH_SIZE
        
        // Simulate the batching logic from MusicRepository
        val combinedResults = mutableMapOf<Long, Boolean>()
        var totalBatches = 0
        var largestBatchSize = 0
        
        for (i in songIds.indices step batchSize) {
            val endIndex = minOf(i + batchSize, songIds.size)
            val batch = songIds.subList(i, endIndex)
            
            // Verify batch size is within SQLite limits
            assertTrue("Batch size ${batch.size} exceeds SQLite limit of $SQLITE_VARIABLE_LIMIT", batch.size <= SQLITE_VARIABLE_LIMIT)
            assertTrue("Batch size ${batch.size} exceeds our safe limit of $batchSize", batch.size <= batchSize)
            
            // Simulate database call result
            val batchResults = batch.associateWith { id -> id % 2 == 0L } // Even IDs are favorites
            combinedResults.putAll(batchResults)
            
            totalBatches++
            largestBatchSize = maxOf(largestBatchSize, batch.size)
        }
        
        // Verify all IDs were processed
        assertEquals("Not all song IDs were processed", songIds.size, combinedResults.size)
        assertEquals("Expected 2 batches for 1000 songs with batch size $SAFE_BATCH_SIZE", 2, totalBatches)
        assertEquals("Largest batch should be $SAFE_BATCH_SIZE", SAFE_BATCH_SIZE, largestBatchSize)
        
        // Verify data integrity - all original IDs are present
        songIds.forEach { id ->
            assertTrue("Song ID $id missing from results", combinedResults.containsKey(id))
        }
        
        // Verify the logic works correctly
        assertTrue("Song ID 2 should be favorite (even)", combinedResults[2L] == true)
        assertTrue("Song ID 3 should not be favorite (odd)", combinedResults[3L] == false)
    }
    
    @Test
    fun testBatchingLogic_withExactBatchSize() {
        // Test with exactly 500 songs (one batch)
        val songIds = (1L..SAFE_BATCH_SIZE.toLong()).toList()
        val batchSize = SAFE_BATCH_SIZE
        
        val combinedResults = mutableMapOf<Long, Boolean>()
        var batchCount = 0
        
        for (i in songIds.indices step batchSize) {
            val endIndex = minOf(i + batchSize, songIds.size)
            val batch = songIds.subList(i, endIndex)
            
            assertTrue("Batch size ${batch.size} should not exceed $batchSize", batch.size <= batchSize)
            
            val batchResults = batch.associateWith { true } // All favorites
            combinedResults.putAll(batchResults)
            batchCount++
        }
        
        assertEquals("Should have exactly 1 batch", 1, batchCount)
        assertEquals("All $SAFE_BATCH_SIZE songs should be processed", SAFE_BATCH_SIZE, combinedResults.size)
    }
    
    @Test
    fun testBatchingLogic_withSmallList() {
        // Test with small list (should work in one batch)
        val songIds = (1L..10L).toList()
        val batchSize = SAFE_BATCH_SIZE
        
        val combinedResults = mutableMapOf<Long, Boolean>()
        var batchCount = 0
        
        for (i in songIds.indices step batchSize) {
            val endIndex = minOf(i + batchSize, songIds.size)
            val batch = songIds.subList(i, endIndex)
            
            val batchResults = batch.associateWith { false } // No favorites
            combinedResults.putAll(batchResults)
            batchCount++
        }
        
        assertEquals("Should have exactly 1 batch", 1, batchCount)
        assertEquals("All 10 songs should be processed", 10, combinedResults.size)
        assertTrue("All songs should be non-favorites", combinedResults.values.all { !it })
    }
    
    @Test
    fun testBatchingLogic_withEmptyList() {
        // Test edge case with empty list
        val songIds = emptyList<Long>()
        val batchSize = SAFE_BATCH_SIZE
        
        val combinedResults = mutableMapOf<Long, Boolean>()
        var batchCount = 0
        
        for (i in songIds.indices step batchSize) {
            val endIndex = minOf(i + batchSize, songIds.size)
            val batch = songIds.subList(i, endIndex)
            
            val batchResults = batch.associateWith { true }
            combinedResults.putAll(batchResults)
            batchCount++
        }
        
        assertEquals("Should have no batches for empty list", 0, batchCount)
        assertEquals("Result should be empty", 0, combinedResults.size)
    }
}