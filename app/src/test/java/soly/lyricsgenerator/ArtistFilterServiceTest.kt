package soly.lyricsgenerator

import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.domain.usecase.preferences.LoadArtistFilterUseCase
import soly.lyricsgenerator.ui.viewmodel.PreferenceKeys

/**
 * Test to verify artist filter logic works correctly at service level
 */
class ArtistFilterServiceTest {

    private val loadArtistFilterUseCase: LoadArtistFilterUseCase = mock()

    @Test
    fun artistFilter_appliesCorrectly_whenSpecificArtistSelected() {
        runBlocking {
            // Mock returning a specific artist filter
            whenever(loadArtistFilterUseCase()).thenReturn("Queen")

            // Simulate the filtering logic from MusicPlayerService.loadSongs()
            val allSongs = listOf(
                Song(id = 1, title = "Bohemian Rhapsody", artist = "Queen", data = "/path1", duration = 355000),
                <PERSON>(id = 2, title = "Hello", artist = "Adele", data = "/path2", duration = 295000),
                <PERSON>(id = 3, title = "Another One Bites the Dust", artist = "Queen", data = "/path3", duration = 217000)
            )

            // Apply the same filtering logic as in the service
            val artistFilter = loadArtistFilterUseCase()
            val filteredSongs = if (artistFilter != PreferenceKeys.DEFAULT_ARTIST_FILTER) {
                allSongs.filter { it.artist == artistFilter }
            } else {
                allSongs
            }

            // Verify only Queen songs are returned
            assertEquals(2, filteredSongs.size)
            assertEquals("Queen", filteredSongs[0].artist)
            assertEquals("Queen", filteredSongs[1].artist)
            assertEquals("Bohemian Rhapsody", filteredSongs[0].title)
            assertEquals("Another One Bites the Dust", filteredSongs[1].title)
        }
    }

    @Test
    fun artistFilter_showsAllSongs_whenDefaultFilterSelected() {
        runBlocking {
            // Mock returning the default "All Artists" filter
            whenever(loadArtistFilterUseCase()).thenReturn(PreferenceKeys.DEFAULT_ARTIST_FILTER)

            val allSongs = listOf(
                Song(id = 1, title = "Bohemian Rhapsody", artist = "Queen", data = "/path1", duration = 355000),
                Song(id = 2, title = "Hello", artist = "Adele", data = "/path2", duration = 295000),
                Song(id = 3, title = "Another One Bites the Dust", artist = "Queen", data = "/path3", duration = 217000)
            )

            // Apply the same filtering logic as in the service
            val artistFilter = loadArtistFilterUseCase()
            val filteredSongs = if (artistFilter != PreferenceKeys.DEFAULT_ARTIST_FILTER) {
                allSongs.filter { it.artist == artistFilter }
            } else {
                allSongs
            }

            // Verify all songs are returned
            assertEquals(3, filteredSongs.size)
            assertEquals(allSongs, filteredSongs)
        }
    }

    @Test
    fun artistFilter_worksWithEmptyResults() {
        runBlocking {
            // Mock returning an artist that doesn't exist
            whenever(loadArtistFilterUseCase()).thenReturn("NonExistentArtist")

            val allSongs = listOf(
                Song(id = 1, title = "Bohemian Rhapsody", artist = "Queen", data = "/path1", duration = 355000),
                Song(id = 2, title = "Hello", artist = "Adele", data = "/path2", duration = 295000)
            )

            // Apply the same filtering logic as in the service
            val artistFilter = loadArtistFilterUseCase()
            val filteredSongs = if (artistFilter != PreferenceKeys.DEFAULT_ARTIST_FILTER) {
                allSongs.filter { it.artist == artistFilter }
            } else {
                allSongs
            }

            // Verify no songs are returned
            assertEquals(0, filteredSongs.size)
        }
    }
}