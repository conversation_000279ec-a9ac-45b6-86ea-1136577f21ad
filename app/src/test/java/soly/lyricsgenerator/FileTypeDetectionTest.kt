package soly.lyricsgenerator

import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.domain.database.model.FileType
import soly.lyricsgenerator.ui.screens.components.getFileTypeFromExtension
import soly.lyricsgenerator.ui.screens.components.FileExtensionMatcher

/**
 * Unit tests for file type detection functionality
 * Following Clean Architecture guidelines with sealed classes and polymorphic dispatch
 */
class FileTypeDetectionTest {

    @Test
    fun getFileTypeFromExtension_lrcFile_returnsLrcType() {
        // Test with .lrc extension
        val result = getFileTypeFromExtension("test.lrc")
        assertEquals(FileType.LRC, result)
    }

    @Test
    fun getFileTypeFromExtension_txtFile_returnsTxtType() {
        // Test with .txt extension
        val result = getFileTypeFromExtension("test.txt")
        assertEquals(FileType.TXT, result)
    }

    @Test
    fun getFileTypeFromExtension_rtfFile_returnsRtfType() {
        val result = getFileTypeFromExtension("test.rtf")
        assertEquals(FileType.RTF, result)
    }

    @Test
    fun getFileTypeFromExtension_lrcFileUppercase_returnsLrcType() {
        // Test case insensitivity for .LRC
        val result = getFileTypeFromExtension("test.LRC")
        assertEquals(FileType.LRC, result)
    }

    @Test
    fun getFileTypeFromExtension_txtFileUppercase_returnsTxtType() {
        // Test case insensitivity for .TXT
        val result = getFileTypeFromExtension("test.TXT")
        assertEquals(FileType.TXT, result)
    }

    @Test
    fun getFileTypeFromExtension_rtfFileUppercase_returnsRtfType() {
        val result = getFileTypeFromExtension("test.RTF")
        assertEquals(FileType.RTF, result)
    }

    @Test
    fun getFileTypeFromExtension_mixedCase_returnsCorrectType() {
        // Test mixed case extensions
        assertEquals(FileType.LRC, getFileTypeFromExtension("test.Lrc"))
        assertEquals(FileType.TXT, getFileTypeFromExtension("test.Txt"))
        assertEquals(FileType.RTF, getFileTypeFromExtension("test.Rtf"))
    }

    @Test
    fun getFileTypeFromExtension_unknownExtension_returnsLrcDefault() {
        // Test unknown extension defaults to LRC for backward compatibility
        val result = getFileTypeFromExtension("test.unknown")
        assertEquals(FileType.LRC, result)
    }

    @Test
    fun getFileTypeFromExtension_noExtension_returnsLrcDefault() {
        // Test filename without extension
        val result = getFileTypeFromExtension("test")
        assertEquals(FileType.LRC, result)
    }

    @Test
    fun getFileTypeFromExtension_emptyString_returnsLrcDefault() {
        // Test empty filename
        val result = getFileTypeFromExtension("")
        assertEquals(FileType.LRC, result)
    }

    @Test
    fun getFileTypeFromExtension_complexFilename_returnsCorrectType() {
        // Test complex filenames with multiple dots
        assertEquals(FileType.LRC, getFileTypeFromExtension("my.song.name.lrc"))
        assertEquals(FileType.TXT, getFileTypeFromExtension("my.song.name.txt"))
    }

    @Test
    fun fileExtensionMatcher_lrcMatcher_matchesCorrectly() {
        // Test LrcMatcher polymorphic behavior
        val matcher = FileExtensionMatcher.LrcMatcher
        assertTrue(matcher.matches("test.lrc"))
        assertTrue(matcher.matches("test.LRC"))
        assertFalse(matcher.matches("test.txt"))
        assertEquals(FileType.LRC, matcher.getFileType())
    }

    @Test
    fun fileExtensionMatcher_txtMatcher_matchesCorrectly() {
        // Test TxtMatcher polymorphic behavior
        val matcher = FileExtensionMatcher.TxtMatcher
        assertTrue(matcher.matches("test.txt"))
        assertTrue(matcher.matches("test.TXT"))
        assertFalse(matcher.matches("test.lrc"))
        assertEquals(FileType.TXT, matcher.getFileType())
    }

    @Test
    fun fileExtensionMatcher_rtfMatcher_matchesCorrectly() {
        val matcher = FileExtensionMatcher.RtfMatcher
        assertTrue(matcher.matches("test.rtf"))
        assertTrue(matcher.matches("test.RTF"))
        assertFalse(matcher.matches("test.lrc"))
        assertEquals(FileType.RTF, matcher.getFileType())
    }

    @Test
    fun fileExtensionMatcher_polymorphicDispatch_worksCorrectly() {
        // Test that polymorphic dispatch works correctly
        val matchers = listOf(
            FileExtensionMatcher.LrcMatcher,
            FileExtensionMatcher.TxtMatcher,
            FileExtensionMatcher.RtfMatcher
        )
        
        // Test LRC file detection via polymorphic dispatch
        val lrcMatcher = matchers.first { it.matches("test.lrc") }
        assertEquals(FileType.LRC, lrcMatcher.getFileType())
        
        // Test TXT file detection via polymorphic dispatch
        val txtMatcher = matchers.first { it.matches("test.txt") }
        assertEquals(FileType.TXT, txtMatcher.getFileType())

        val rtfMatcher = matchers.first { it.matches("test.rtf") }
        assertEquals(FileType.RTF, rtfMatcher.getFileType())
    }
}