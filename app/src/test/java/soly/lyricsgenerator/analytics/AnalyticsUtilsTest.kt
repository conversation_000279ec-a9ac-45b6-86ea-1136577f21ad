package soly.lyricsgenerator.analytics

import android.os.Bundle
import org.junit.Test
import org.junit.Assert.*
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class AnalyticsUtilsTest {

    @Test
    fun `bundleToMap should return empty map for null bundle`() {
        val result = AnalyticsUtils.bundleToMap(null)
        assertTrue("Result should be empty for null bundle", result.isEmpty())
    }

    @Test
    fun `bundleToMap should convert string values correctly`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("key1", "key2")
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("key1")).doReturn("value1")
        whenever(bundle.get("key2")).doReturn("value2")
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 2 entries", 2, result.size)
        assertEquals("Should contain correct string value", "value1", result["key1"])
        assertEquals("Should contain correct string value", "value2", result["key2"])
    }

    @Test
    fun `bundleToMap should convert numeric values correctly`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("int_key", "long_key", "float_key", "double_key")
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("int_key")).doReturn(42)
        whenever(bundle.get("long_key")).doReturn(123L)
        whenever(bundle.get("float_key")).doReturn(3.14f)
        whenever(bundle.get("double_key")).doReturn(2.718)
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 4 entries", 4, result.size)
        assertEquals("Should contain correct int value", 42, result["int_key"])
        assertEquals("Should contain correct long value", 123L, result["long_key"])
        assertEquals("Should contain correct float value", 3.14f, result["float_key"])
        assertEquals("Should contain correct double value", 2.718, result["double_key"])
    }

    @Test
    fun `bundleToMap should convert boolean values correctly`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("bool_true", "bool_false")
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("bool_true")).doReturn(true)
        whenever(bundle.get("bool_false")).doReturn(false)
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 2 entries", 2, result.size)
        assertEquals("Should contain correct boolean value", true, result["bool_true"])
        assertEquals("Should contain correct boolean value", false, result["bool_false"])
    }

    @Test
    fun `bundleToMap should convert unsupported types to string`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("object_key")
        val customObject = listOf("item1", "item2") // Example of unsupported type
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("object_key")).doReturn(customObject)
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 1 entry", 1, result.size)
        assertEquals("Should convert object to string", customObject.toString(), result["object_key"])
    }

    @Test
    fun `bundleToMap should handle null values by skipping them`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("key1", "null_key", "key2")
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("key1")).doReturn("value1")
        whenever(bundle.get("null_key")).doReturn(null)
        whenever(bundle.get("key2")).doReturn("value2")
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 2 entries (null excluded)", 2, result.size)
        assertEquals("Should contain correct value", "value1", result["key1"])
        assertEquals("Should contain correct value", "value2", result["key2"])
        assertFalse("Should not contain null key", result.containsKey("null_key"))
    }

    @Test
    fun `bundleToMap should handle mixed data types correctly`() {
        val bundle = mock<Bundle>()
        val keySet = setOf("string", "int", "boolean", "object")
        
        whenever(bundle.keySet()).doReturn(keySet)
        whenever(bundle.get("string")).doReturn("text")
        whenever(bundle.get("int")).doReturn(100)
        whenever(bundle.get("boolean")).doReturn(true)
        whenever(bundle.get("object")).doReturn(mapOf("nested" to "value"))
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertEquals("Should have 4 entries", 4, result.size)
        assertEquals("String should remain string", "text", result["string"])
        assertEquals("Int should remain int", 100, result["int"])
        assertEquals("Boolean should remain boolean", true, result["boolean"])
        assertTrue("Object should become string", result["object"] is String)
    }

    @Test
    fun `bundleToMap should return empty map on exception`() {
        val bundle = mock<Bundle>()
        
        // Mock to throw exception on keySet call
        whenever(bundle.keySet()).thenThrow(RuntimeException("Test exception"))
        
        val result = AnalyticsUtils.bundleToMap(bundle)
        
        assertTrue("Should return empty map on exception", result.isEmpty())
    }
}