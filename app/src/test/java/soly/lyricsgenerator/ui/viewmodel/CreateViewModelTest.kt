import android.app.Application
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito.mock
import Tuple
import soly.lyricsgenerator.ui.viewmodel.CreateViewModel
import soly.lyricsgenerator.domain.usecase.database.file.SaveFileUseCase
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByIdUseCase
import soly.lyricsgenerator.domain.usecase.database.file.GetFileByFileIdUseCase
import soly.lyricsgenerator.domain.usecase.database.song.GetSongUseCase
import soly.lyricsgenerator.domain.usecase.lyrics.ParseAndPrepareTextFileUseCase
import soly.lyricsgenerator.domain.usecase.lrc.SaveLrcFileUseCase
import androidx.core.content.ContextCompat
import org.mockito.Mockito
import kotlinx.coroutines.ExperimentalCoroutinesApi

@OptIn(ExperimentalCoroutinesApi::class)
class CreateViewModelTest {

    private lateinit var viewModel: CreateViewModel
    private val dispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(dispatcher)
        Mockito.mockStatic(ContextCompat::class.java).use {
            viewModel = CreateViewModel(
                context = mock(Application::class.java),
                saveFileUseCase = mock(SaveFileUseCase::class.java),
                saveLrcFileUseCase = mock(SaveLrcFileUseCase::class.java),
                getSongUseCase = mock(GetSongUseCase::class.java),
                getFileByIdUseCase = mock(GetFileByIdUseCase::class.java),
                getFileByFileIdUseCase = mock(GetFileByFileIdUseCase::class.java),
                parseAndPrepareTextFileUseCase = mock(ParseAndPrepareTextFileUseCase::class.java)
            )
        }
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun resetTimestamps_setsAllTimestampsToZero() {
        val initial = mapOf(
            0 to Tuple(100L, "line1"),
            1 to Tuple(200L, "line2")
        )
        viewModel.lrcKeyValuePairs.value = initial

        viewModel.resetTimestamps()

        val expected = mapOf(
            0 to Tuple(0L, "line1"),
            1 to Tuple(0L, "line2")
        )
        assertEquals(expected, viewModel.lrcKeyValuePairs.value)
    }
}
