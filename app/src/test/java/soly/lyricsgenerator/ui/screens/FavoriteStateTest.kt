package soly.lyricsgenerator.ui.screens

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.ui.graphics.Color
import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.ui.screens.song_details_screen.FavoriteState
import soly.lyricsgenerator.ui.theme.InteractiveAccent

/**
 * Test the FavoriteState sealed class polymorphic behavior.
 * Validates that the sealed class follows the architectural pattern correctly.
 */
class FavoriteStateTest {

    @Test
    fun `NotFavorite state returns correct icon and properties`() {
        val state = FavoriteState.NotFavorite
        
        assertEquals(Icons.Default.FavoriteBorder, state.getIcon())
        assertEquals(Color.White, state.getTint())
    }

    @Test
    fun `Favorite state returns correct icon and properties`() {
        val state = FavoriteState.Favorite
        
        assertEquals(Icons.Default.Favorite, state.getIcon())
        assertEquals(InteractiveAccent, state.getTint())
    }

    @Test
    fun `Loading state returns correct icon and properties`() {
        val state = FavoriteState.Loading
        
        assertEquals(Icons.Default.FavoriteBorder, state.getIcon())
        assertEquals(InteractiveAccent.copy(alpha = 0.5f), state.getTint())
    }

    @Test
    fun `sealed class polymorphism works correctly`() {
        val states = listOf(
            FavoriteState.NotFavorite,
            FavoriteState.Favorite, 
            FavoriteState.Loading
        )
        
        // Test that each state can be dispatched polymorphically
        states.forEach { state ->
            // Should not throw exceptions and should return valid results
            assertNotNull(state.getIcon())
            assertNotNull(state.getTint())
        }
    }

    @Test
    fun `states have different behaviors`() {
        val notFavorite = FavoriteState.NotFavorite
        val favorite = FavoriteState.Favorite
        val loading = FavoriteState.Loading
        
        // Verify states have different icons (favorite vs border)
        assertNotEquals(notFavorite.getIcon(), favorite.getIcon())
        
        // NotFavorite uses white, Favorite uses InteractiveAccent, Loading has alpha
        assertNotEquals(notFavorite.getTint(), favorite.getTint())
        assertNotEquals(notFavorite.getTint(), loading.getTint())
        
        // Loading should be similar to NotFavorite but with different tint
        assertEquals(notFavorite.getIcon(), loading.getIcon())
        assertNotEquals(notFavorite.getTint(), loading.getTint())
    }
}