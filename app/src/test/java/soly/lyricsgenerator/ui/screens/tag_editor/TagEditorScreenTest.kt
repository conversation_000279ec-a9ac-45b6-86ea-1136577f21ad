package soly.lyricsgenerator.ui.screens.tag_editor

import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.domain.model.AudioTag

/**
 * Unit tests for TagEditorScreen to ensure LyricsSection data model functionality.
 * Following architecture guidelines with sealed classes and polymorphic dispatch.
 */
class TagEditorScreenTest {

    @Test
    fun `test TagEditorUiState Loaded contains lyrics data`() = runTest {
        // Arrange: Create test data with lyrics
        val testTags = AudioTag(
            title = "Test Song",
            artist = "Test Artist",
            album = "Test Album",
            lyrics = "Test lyrics content"
        )
        
        val testState = TagEditorUiState.Loaded(
            filePath = "/test/path",
            fileName = "test_song.mp3",
            supportedFormats = listOf("mp3", "flac"),
            supportsEmbeddedLyrics = true,
            originalTags = testTags,
            currentTags = testTags,
            validationErrors = emptyMap(),
            linkedLrcFiles = emptyList()
        )
        
        // Assert: Verify that the state model correctly holds lyrics data,
        // even though the UI section is currently hidden via feature flag
        assertEquals("Test lyrics content", testState.currentTags.lyrics)
    }
}