package soly.lyricsgenerator.ui.theme

import androidx.compose.ui.graphics.Color
import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.ui.screens.song_details_screen.FavoriteState

/**
 * Test the InteractiveAccent color consistency across UI components.
 * Validates that the unified violet color is used correctly for interactive elements.
 */
class InteractiveAccentTest {

    @Test
    fun `InteractiveAccent matches Purple80 violet color`() {
        assertEquals(Purple80, InteractiveAccent)
        assertEquals(Color(0xFFD0BCFF), InteractiveAccent)
    }

    @Test
    fun `FavoriteState uses unified InteractiveAccent color`() {
        val notFavorite = FavoriteState.NotFavorite
        val favorite = FavoriteState.Favorite
        val loading = FavoriteState.Loading

        // All states should use InteractiveAccent (with Loading having alpha)
        assertEquals(Color.White, notFavorite.getTint())
        assertEquals(InteractiveAccent, favorite.getTint())
        assertEquals(InteractiveAccent.copy(alpha = 0.5f), loading.getTint())
    }

    @Test
    fun `InteractiveAccent color is violet hex value`() {
        // Verify it's the correct violet color as specified in the issue
        val expectedColor = Color(0xFFD0BCFF)
        assertEquals(expectedColor, InteractiveAccent)
    }
}