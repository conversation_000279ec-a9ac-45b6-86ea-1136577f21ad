package soly.lyricsgenerator.features.shared_audio.presentation

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.SavedStateHandle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult
import soly.lyricsgenerator.features.shared_audio.domain.use_cases.ProcessSharedAudioUseCase
import soly.lyricsgenerator.features.shared_audio.presentation.states.SharedAudioUiState
import soly.lyricsgenerator.features.shared_audio.presentation.viewmodels.SharedAudioViewModel

/**
 * Unit tests for SharedAudioViewModel.
 * Tests ViewModel behavior with mocked dependencies.
 */
@OptIn(ExperimentalCoroutinesApi::class)
class SharedAudioViewModelTest {

    @Mock
    private lateinit var mockProcessSharedAudioUseCase: ProcessSharedAudioUseCase

    @Mock
    private lateinit var mockSavedStateHandle: SavedStateHandle

    private lateinit var viewModel: SharedAudioViewModel
    private val testDispatcher = StandardTestDispatcher()

    private val testUri1 = Uri.parse("content://media/external/audio/123")
    private val testUri2 = Uri.parse("content://media/external/audio/456")

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        Dispatchers.setMain(testDispatcher)
        
        viewModel = SharedAudioViewModel(
            processSharedAudioUseCase = mockProcessSharedAudioUseCase,
            savedStateHandle = mockSavedStateHandle
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `initial state is Idle`() = runTest {
        // Act
        val state = viewModel.uiState.first()
        
        // Assert
        assertTrue(state is SharedAudioUiState.Idle)
    }

    @Test
    fun `processIntent with ACTION_SEND updates state to Processing then Success`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        val expectedResult = SharedAudioResult.Success(listOf(testUri1))
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Success)
        assertEquals(listOf(testUri1), finalState.getAudioUris())
        
        // Check navigation event
        val navigationEvent = viewModel.navigationEvent.value
        assertTrue(navigationEvent is SharedAudioViewModel.NavigationEvent.NavigateToMusicWithAudio)
    }

    @Test
    fun `processIntent with ACTION_SEND_MULTIPLE processes multiple URIs`() = runTest {
        // Arrange
        val uris = arrayListOf(testUri1, testUri2)
        val intent = Intent(Intent.ACTION_SEND_MULTIPLE).apply {
            putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris)
        }
        val expectedResult = SharedAudioResult.Success(uris)
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Success)
        assertEquals(uris, finalState.getAudioUris())
    }

    @Test
    fun `processIntent with missing EXTRA_STREAM results in NoAudio`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND) // No EXTRA_STREAM
        val expectedResult = SharedAudioResult.ValidationError("Invalid audio intent received")
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Error)
        assertEquals("Invalid audio intent received", (finalState as SharedAudioUiState.Error).getErrorMessage())
    }

    @Test
    fun `processIntent with failed processing updates state to Error`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        val expectedResult = SharedAudioResult.ValidationError("Unsupported audio format")
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Error)
        assertEquals("Unsupported audio format", (finalState as SharedAudioUiState.Error).getErrorMessage())
        
        // Check no navigation event
        assertNull(viewModel.navigationEvent.value)
    }

    @Test
    fun `processIntent with use case exception updates state to Error`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenThrow(RuntimeException("Network error"))
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Error)
        assertTrue((finalState as SharedAudioUiState.Error).getErrorMessage().contains("Failed to process shared audio"))
    }

    @Test
    fun `processIntent with unknown action results in NoAudio`() = runTest {
        // Arrange
        val intent = Intent("unknown.action")
        val expectedResult = SharedAudioResult.ValidationError("Invalid audio intent received")
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Assert
        val finalState = viewModel.uiState.value
        assertTrue(finalState is SharedAudioUiState.Error)
        assertEquals("Invalid audio intent received", (finalState as SharedAudioUiState.Error).getErrorMessage())
    }

    @Test
    fun `clearNavigationEvent clears the navigation event`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        val expectedResult = SharedAudioResult.Success(listOf(testUri1))
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Verify navigation event is set
        assertNotNull(viewModel.navigationEvent.value)
        
        // Act
        viewModel.clearNavigationEvent()
        
        // Assert
        assertNull(viewModel.navigationEvent.value)
    }

    @Test
    fun `resetState resets UI state to Idle`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        val expectedResult = SharedAudioResult.Success(listOf(testUri1))
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Verify state is not Idle
        assertFalse(viewModel.uiState.value is SharedAudioUiState.Idle)
        
        // Act
        viewModel.resetState()
        
        // Assert
        assertTrue(viewModel.uiState.value is SharedAudioUiState.Idle)
    }

    @Test
    fun `state transitions from Idle to Processing to Success`() = runTest {
        // Arrange
        val intent = Intent(Intent.ACTION_SEND).apply {
            putExtra(Intent.EXTRA_STREAM, testUri1)
        }
        val expectedResult = SharedAudioResult.Success(listOf(testUri1))
        
        whenever(mockProcessSharedAudioUseCase.invoke(any())).thenReturn(expectedResult)
        
        val states = mutableListOf<SharedAudioUiState>()
        
        // Collect states
        val job = launch(testDispatcher) {
            viewModel.uiState.collect { states.add(it) }
        }
        
        // Act
        viewModel.processIntent(intent)
        testDispatcher.scheduler.advanceUntilIdle()
        
        job.cancel()
        
        // Assert
        assertTrue(states[0] is SharedAudioUiState.Idle)
        assertTrue(states[1] is SharedAudioUiState.Processing)
        assertTrue(states[2] is SharedAudioUiState.Success)
    }
}