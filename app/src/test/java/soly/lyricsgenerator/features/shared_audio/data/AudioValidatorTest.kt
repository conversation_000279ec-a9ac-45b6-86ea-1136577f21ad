package soly.lyricsgenerator.features.shared_audio.data

import android.content.ContentResolver
import android.net.Uri
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.mockito.kotlin.any
import soly.lyricsgenerator.features.shared_audio.data.validators.AudioValidator
import java.io.ByteArrayInputStream
import java.io.IOException

/**
 * Unit tests for AudioValidator.
 * Tests validation logic with mocked ContentResolver.
 */
class AudioValidatorTest {

    @Mock
    private lateinit var mockContentResolver: ContentResolver

    private lateinit var validator: AudioValidator

    private val testUri = Uri.parse("content://media/external/audio/123")
    private val emptyUri = Uri.EMPTY

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        validator = AudioValidator()
    }

    @Test
    fun `validate returns false for empty URI`() = runTest {
        // Act
        val result = validator.validate(emptyUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate returns false when MIME type cannot be determined`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn(null)
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate returns false for invalid MIME type`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("image/jpeg")
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate returns false when content cannot be read`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(null)
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate returns true for valid audio MP3`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertTrue(result)
    }

    @Test
    fun `validate returns true for valid audio M4A`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mp4")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertTrue(result)
    }

    @Test
    fun `validate returns true for audio wildcard MIME type`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/flac")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertTrue(result)
    }

    @Test
    fun `validate returns true for octet-stream MIME type`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("application/octet-stream")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertTrue(result)
    }

    @Test
    fun `validate returns false when reading content throws exception`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(testUri)).thenThrow(IOException("Access denied"))
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate returns false when stream is empty`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(testUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf()) // Empty stream
        )
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `validate handles ContentResolver exception gracefully`() = runTest {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenThrow(SecurityException("Permission denied"))
        
        // Act
        val result = validator.validate(testUri, mockContentResolver)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `getMimeType returns correct MIME type`() {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenReturn("audio/mpeg")
        
        // Act
        val result = validator.getMimeType(testUri, mockContentResolver)
        
        // Assert
        assertEquals("audio/mpeg", result)
    }

    @Test
    fun `getMimeType returns null when ContentResolver throws exception`() {
        // Arrange
        whenever(mockContentResolver.getType(testUri)).thenThrow(SecurityException("Permission denied"))
        
        // Act
        val result = validator.getMimeType(testUri, mockContentResolver)
        
        // Assert
        assertNull(result)
    }

    @Test
    fun `validateMultiple filters out invalid URIs`() = runTest {
        // Arrange
        val validUri = testUri
        val invalidUri = Uri.parse("content://media/external/audio/456")
        val uris = listOf(validUri, invalidUri)
        
        whenever(mockContentResolver.getType(validUri)).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(validUri)).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        whenever(mockContentResolver.getType(invalidUri)).thenReturn("image/jpeg")
        
        // Act
        val result = validator.validateMultiple(uris, mockContentResolver)
        
        // Assert
        assertEquals(1, result.size)
        assertEquals(validUri, result[0])
    }

    @Test
    fun `validateMultiple returns empty list when all URIs are invalid`() = runTest {
        // Arrange
        val uri1 = testUri
        val uri2 = Uri.parse("content://media/external/audio/456")
        val uris = listOf(uri1, uri2)
        
        whenever(mockContentResolver.getType(any())).thenReturn("image/jpeg")
        
        // Act
        val result = validator.validateMultiple(uris, mockContentResolver)
        
        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `validateMultiple returns all URIs when all are valid`() = runTest {
        // Arrange
        val uri1 = testUri
        val uri2 = Uri.parse("content://media/external/audio/456")
        val uris = listOf(uri1, uri2)
        
        whenever(mockContentResolver.getType(any())).thenReturn("audio/mpeg")
        whenever(mockContentResolver.openInputStream(any())).thenReturn(
            ByteArrayInputStream(byteArrayOf(0x01, 0x02, 0x03))
        )
        
        // Act
        val result = validator.validateMultiple(uris, mockContentResolver)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(uri1, result[0])
        assertEquals(uri2, result[1])
    }
}