package soly.lyricsgenerator.features.shared_audio.domain

import android.net.Uri
import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult

/**
 * Unit tests for SharedAudioResult sealed class.
 * Tests polymorphic behavior without using when expressions.
 */
class SharedAudioResultTest {

    private val testUri1 = Uri.parse("content://media/external/audio/123")
    private val testUri2 = Uri.parse("content://media/external/audio/456")

    @Test
    fun `Success isSuccess returns true`() {
        // Arrange
        val audioUris = listOf(testUri1, testUri2)
        val success = SharedAudioResult.Success(audioUris)
        
        // Act & Assert
        assertTrue(success.isSuccess())
    }

    @Test
    fun `Success getAudioUris returns correct URIs`() {
        // Arrange
        val audioUris = listOf(testUri1, testUri2)
        val success = SharedAudioResult.Success(audioUris)
        
        // Act
        val result = success.getAudioUris()
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(testUri1, result[0])
        assertEquals(testUri2, result[1])
    }

    @Test
    fun `Success getErrorMessage returns null`() {
        // Arrange
        val audioUris = listOf(testUri1)
        val success = SharedAudioResult.Success(audioUris)
        
        // Act
        val errorMessage = success.getErrorMessage()
        
        // Assert
        assertNull(errorMessage)
    }

    @Test
    fun `Success getMetadata returns provided metadata`() {
        // Arrange
        val audioUris = listOf(testUri1)
        val metadata = mapOf("test_key" to "test_value", "count" to 1)
        val success = SharedAudioResult.Success(audioUris, metadata)
        
        // Act
        val result = success.getMetadata()
        
        // Assert
        assertEquals(metadata, result)
    }

    @Test
    fun `Success handleResult returns correct message`() {
        // Arrange
        val audioUris = listOf(testUri1, testUri2)
        val success = SharedAudioResult.Success(audioUris)
        
        // Act
        val message = success.handleResult()
        
        // Assert
        assertEquals("Successfully processed 2 audio file(s)", message)
    }

    @Test
    fun `ValidationError isSuccess returns false`() {
        // Arrange
        val error = SharedAudioResult.ValidationError("Invalid format")
        
        // Act & Assert
        assertFalse(error.isSuccess())
    }

    @Test
    fun `ValidationError getAudioUris returns empty list`() {
        // Arrange
        val error = SharedAudioResult.ValidationError("Invalid format")
        
        // Act
        val result = error.getAudioUris()
        
        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `ValidationError getErrorMessage returns correct message`() {
        // Arrange
        val errorMessage = "Invalid audio format"
        val error = SharedAudioResult.ValidationError(errorMessage)
        
        // Act
        val result = error.getErrorMessage()
        
        // Assert
        assertEquals(errorMessage, result)
    }

    @Test
    fun `ValidationError getMetadata contains error info`() {
        // Arrange
        val failedUris = listOf(testUri1)
        val error = SharedAudioResult.ValidationError("Invalid format", failedUris)
        
        // Act
        val metadata = error.getMetadata()
        
        // Assert
        assertEquals(failedUris, metadata["failed_uris"])
        assertEquals("validation", metadata["error_type"])
    }

    @Test
    fun `ValidationError handleResult returns correct message`() {
        // Arrange
        val errorMessage = "Invalid audio format"
        val error = SharedAudioResult.ValidationError(errorMessage)
        
        // Act
        val result = error.handleResult()
        
        // Assert
        assertEquals("Validation failed: $errorMessage", result)
    }

    @Test
    fun `ProcessingError isSuccess returns false`() {
        // Arrange
        val error = SharedAudioResult.ProcessingError("Processing failed")
        
        // Act & Assert
        assertFalse(error.isSuccess())
    }

    @Test
    fun `ProcessingError getAudioUris returns empty list`() {
        // Arrange
        val error = SharedAudioResult.ProcessingError("Processing failed")
        
        // Act
        val result = error.getAudioUris()
        
        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `ProcessingError getErrorMessage returns correct message`() {
        // Arrange
        val errorMessage = "Processing failed"
        val error = SharedAudioResult.ProcessingError(errorMessage)
        
        // Act
        val result = error.getErrorMessage()
        
        // Assert
        assertEquals(errorMessage, result)
    }

    @Test
    fun `ProcessingError getMetadata contains exception info`() {
        // Arrange
        val exception = RuntimeException("Test exception")
        val error = SharedAudioResult.ProcessingError("Processing failed", exception)
        
        // Act
        val metadata = error.getMetadata()
        
        // Assert
        assertEquals("Test exception", metadata["exception"])
        assertEquals("processing", metadata["error_type"])
    }

    @Test
    fun `ProcessingError handleResult returns correct message`() {
        // Arrange
        val errorMessage = "Processing failed"
        val error = SharedAudioResult.ProcessingError(errorMessage)
        
        // Act
        val result = error.handleResult()
        
        // Assert
        assertEquals("Processing failed: $errorMessage", result)
    }

    @Test
    fun `NoContent isSuccess returns false`() {
        // Act & Assert
        assertFalse(SharedAudioResult.NoContent.isSuccess())
    }

    @Test
    fun `NoContent getAudioUris returns empty list`() {
        // Act
        val result = SharedAudioResult.NoContent.getAudioUris()
        
        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `NoContent getErrorMessage returns correct message`() {
        // Act
        val result = SharedAudioResult.NoContent.getErrorMessage()
        
        // Assert
        assertEquals("No audio content received", result)
    }

    @Test
    fun `NoContent getMetadata contains error type`() {
        // Act
        val metadata = SharedAudioResult.NoContent.getMetadata()
        
        // Assert
        assertEquals("no_content", metadata["error_type"])
    }

    @Test
    fun `NoContent handleResult returns correct message`() {
        // Act
        val result = SharedAudioResult.NoContent.handleResult()
        
        // Assert
        assertEquals("No audio content to process", result)
    }

    @Test
    fun `from creates Success for non-empty validation results`() {
        // Arrange
        val validationResults = listOf(testUri1, testUri2)
        
        // Act
        val result = SharedAudioResult.from(validationResults)
        
        // Assert
        assertTrue(result.isSuccess())
        assertEquals(validationResults, result.getAudioUris())
    }

    @Test
    fun `from creates ValidationError for empty validation results`() {
        // Arrange
        val validationResults = emptyList<Uri>()
        
        // Act
        val result = SharedAudioResult.from(validationResults)
        
        // Assert
        assertFalse(result.isSuccess())
        assertEquals("No valid audio files found", result.getErrorMessage())
    }
}