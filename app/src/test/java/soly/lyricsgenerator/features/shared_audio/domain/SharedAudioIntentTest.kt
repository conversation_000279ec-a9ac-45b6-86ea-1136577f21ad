package soly.lyricsgenerator.features.shared_audio.domain

import android.net.Uri
import org.junit.Test
import org.junit.Assert.*
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioIntent

/**
 * Unit tests for SharedAudioIntent sealed class.
 * Tests polymorphic behavior without using when expressions.
 */
class SharedAudioIntentTest {

    private val testUri1 = Uri.parse("content://media/external/audio/123")
    private val testUri2 = Uri.parse("content://media/external/audio/456")
    private val emptyUri = Uri.EMPTY

    @Test
    fun `SingleAudio process returns single URI`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(testUri1)
        
        // Act
        val result = singleAudio.process()
        
        // Assert
        assertEquals(1, result.size)
        assertEquals(testUri1, result[0])
    }

    @Test
    fun `SingleAudio getSourceDescription returns correct description`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(testUri1)
        
        // Act
        val description = singleAudio.getSourceDescription()
        
        // Assert
        assertEquals("Single audio file", description)
    }

    @Test
    fun `SingleAudio getAudioCount returns 1`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(testUri1)
        
        // Act
        val count = singleAudio.getAudioCount()
        
        // Assert
        assertEquals(1, count)
    }

    @Test
    fun `SingleAudio isValid returns true for non-empty URI`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(testUri1)
        
        // Act
        val isValid = singleAudio.isValid()
        
        // Assert
        assertTrue(isValid)
    }

    @Test
    fun `SingleAudio isValid returns false for empty URI`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(emptyUri)
        
        // Act
        val isValid = singleAudio.isValid()
        
        // Assert
        assertFalse(isValid)
    }

    @Test
    fun `SingleAudio logIntent returns formatted string`() {
        // Arrange
        val singleAudio = SharedAudioIntent.SingleAudio(testUri1)
        
        // Act
        val logString = singleAudio.logIntent()
        
        // Assert
        assertEquals("SingleAudio(uri=$testUri1)", logString)
    }

    @Test
    fun `MultipleAudio process returns all URIs`() {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val result = multipleAudio.process()
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(testUri1, result[0])
        assertEquals(testUri2, result[1])
    }

    @Test
    fun `MultipleAudio getSourceDescription returns correct description`() {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val description = multipleAudio.getSourceDescription()
        
        // Assert
        assertEquals("Multiple audio files (2)", description)
    }

    @Test
    fun `MultipleAudio getAudioCount returns correct count`() {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val count = multipleAudio.getAudioCount()
        
        // Assert
        assertEquals(2, count)
    }

    @Test
    fun `MultipleAudio isValid returns true for non-empty valid URIs`() {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val isValid = multipleAudio.isValid()
        
        // Assert
        assertTrue(isValid)
    }

    @Test
    fun `MultipleAudio isValid returns false for empty list`() {
        // Arrange
        val multipleAudio = SharedAudioIntent.MultipleAudio(emptyList())
        
        // Act
        val isValid = multipleAudio.isValid()
        
        // Assert
        assertFalse(isValid)
    }

    @Test
    fun `MultipleAudio isValid returns false when containing empty URI`() {
        // Arrange
        val uris = listOf(testUri1, emptyUri)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val isValid = multipleAudio.isValid()
        
        // Assert
        assertFalse(isValid)
    }

    @Test
    fun `MultipleAudio logIntent returns formatted string with count`() {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val multipleAudio = SharedAudioIntent.MultipleAudio(uris)
        
        // Act
        val logString = multipleAudio.logIntent()
        
        // Assert
        assertTrue(logString.startsWith("MultipleAudio(count=2"))
        assertTrue(logString.contains("uris="))
    }

    @Test
    fun `NoAudio process returns empty list`() {
        // Act
        val result = SharedAudioIntent.NoAudio.process()
        
        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `NoAudio getSourceDescription returns correct description`() {
        // Act
        val description = SharedAudioIntent.NoAudio.getSourceDescription()
        
        // Assert
        assertEquals("No audio content", description)
    }

    @Test
    fun `NoAudio getAudioCount returns 0`() {
        // Act
        val count = SharedAudioIntent.NoAudio.getAudioCount()
        
        // Assert
        assertEquals(0, count)
    }

    @Test
    fun `NoAudio isValid returns false`() {
        // Act
        val isValid = SharedAudioIntent.NoAudio.isValid()
        
        // Assert
        assertFalse(isValid)
    }

    @Test
    fun `NoAudio logIntent returns correct string`() {
        // Act
        val logString = SharedAudioIntent.NoAudio.logIntent()
        
        // Assert
        assertEquals("NoAudio", logString)
    }
}