package soly.lyricsgenerator.features.shared_audio.domain

import android.net.Uri
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioIntent
import soly.lyricsgenerator.features.shared_audio.domain.models.SharedAudioResult
import soly.lyricsgenerator.features.shared_audio.domain.repository.SharedAudioRepository
import soly.lyricsgenerator.features.shared_audio.domain.use_cases.ProcessSharedAudioUseCase

/**
 * Unit tests for ProcessSharedAudioUseCase.
 * Tests use case business logic with mocked dependencies.
 */
class ProcessSharedAudioUseCaseTest {

    @Mock
    private lateinit var mockRepository: SharedAudioRepository

    private lateinit var useCase: ProcessSharedAudioUseCase

    private val testUri1 = Uri.parse("content://media/external/audio/123")
    private val testUri2 = Uri.parse("content://media/external/audio/456")

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        useCase = ProcessSharedAudioUseCase(mockRepository)
    }

    @Test
    fun `invoke with invalid intent returns ValidationError`() = runTest {
        // Arrange
        val invalidIntent = SharedAudioIntent.NoAudio
        
        // Act
        val result = useCase(invalidIntent)
        
        // Assert
        assertFalse(result.isSuccess())
        assertEquals("Invalid audio intent received", result.getErrorMessage())
    }

    @Test
    fun `invoke with empty URIs returns NoContent`() = runTest {
        // Arrange
        val intent = SharedAudioIntent.MultipleAudio(emptyList())
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertFalse(result.isSuccess())
        assertEquals("No audio content received", result.getErrorMessage())
    }

    @Test
    fun `invoke with valid URIs returns Success when validation passes`() = runTest {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val intent = SharedAudioIntent.MultipleAudio(uris)
        val validationResult = SharedAudioResult.Success(uris)
        
        whenever(mockRepository.validateMultipleAudioUris(uris)).thenReturn(validationResult)
        whenever(mockRepository.extractAudioMetadata(testUri1)).thenReturn(mapOf("title" to "Song 1"))
        whenever(mockRepository.extractAudioMetadata(testUri2)).thenReturn(mapOf("title" to "Song 2"))
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertTrue(result.isSuccess())
        assertEquals(uris, result.getAudioUris())
        
        val metadata = result.getMetadata()
        assertEquals(2, metadata["total_files"])
        assertTrue(metadata.containsKey("processing_timestamp"))
        assertTrue(metadata.containsKey("file_0"))
        assertTrue(metadata.containsKey("file_1"))
    }

    @Test
    fun `invoke with failed validation returns validation result`() = runTest {
        // Arrange
        val uris = listOf(testUri1)
        val intent = SharedAudioIntent.SingleAudio(testUri1)
        val validationError = SharedAudioResult.ValidationError("Invalid audio format")
        
        whenever(mockRepository.validateMultipleAudioUris(uris)).thenReturn(validationError)
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertFalse(result.isSuccess())
        assertEquals("Invalid audio format", result.getErrorMessage())
    }

    @Test
    fun `invoke handles metadata extraction errors gracefully`() = runTest {
        // Arrange
        val uris = listOf(testUri1, testUri2)
        val intent = SharedAudioIntent.MultipleAudio(uris)
        val validationResult = SharedAudioResult.Success(uris)
        
        whenever(mockRepository.validateMultipleAudioUris(uris)).thenReturn(validationResult)
        whenever(mockRepository.extractAudioMetadata(testUri1)).thenReturn(mapOf("title" to "Song 1"))
        whenever(mockRepository.extractAudioMetadata(testUri2)).thenThrow(RuntimeException("Metadata extraction failed"))
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertTrue(result.isSuccess())
        assertEquals(uris, result.getAudioUris())
        
        val metadata = result.getMetadata()
        assertEquals(2, metadata["total_files"])
        
        @Suppress("UNCHECKED_CAST")
        val file1Metadata = metadata["file_1"] as? Map<String, Any>
        assertEquals("Metadata extraction failed", file1Metadata?.get("error"))
    }

    @Test
    fun `invoke with repository exception returns ProcessingError`() = runTest {
        // Arrange
        val uris = listOf(testUri1)
        val intent = SharedAudioIntent.SingleAudio(testUri1)
        val exception = RuntimeException("Repository error")
        
        whenever(mockRepository.validateMultipleAudioUris(uris)).thenThrow(exception)
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertFalse(result.isSuccess())
        assertEquals("Failed to process shared audio: Repository error", result.getErrorMessage())
    }

    @Test
    fun `invoke with single audio URI processes correctly`() = runTest {
        // Arrange
        val intent = SharedAudioIntent.SingleAudio(testUri1)
        val validationResult = SharedAudioResult.Success(listOf(testUri1))
        
        whenever(mockRepository.validateMultipleAudioUris(listOf(testUri1))).thenReturn(validationResult)
        whenever(mockRepository.extractAudioMetadata(testUri1)).thenReturn(mapOf("title" to "Test Song", "duration" to 180000))
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertTrue(result.isSuccess())
        assertEquals(1, result.getAudioUris().size)
        assertEquals(testUri1, result.getAudioUris()[0])
        
        val metadata = result.getMetadata()
        assertEquals(1, metadata["total_files"])
        
        @Suppress("UNCHECKED_CAST")
        val file0Metadata = metadata["file_0"] as? Map<String, Any>
        assertEquals("Test Song", file0Metadata?.get("title"))
        assertEquals(180000, file0Metadata?.get("duration"))
    }

    @Test
    fun `invoke creates metadata with processing timestamp`() = runTest {
        // Arrange
        val intent = SharedAudioIntent.SingleAudio(testUri1)
        val validationResult = SharedAudioResult.Success(listOf(testUri1))
        val startTime = System.currentTimeMillis()
        
        whenever(mockRepository.validateMultipleAudioUris(listOf(testUri1))).thenReturn(validationResult)
        whenever(mockRepository.extractAudioMetadata(testUri1)).thenReturn(emptyMap())
        
        // Act
        val result = useCase(intent)
        
        // Assert
        assertTrue(result.isSuccess())
        
        val metadata = result.getMetadata()
        val timestamp = metadata["processing_timestamp"] as? Long
        assertNotNull(timestamp)
        assertTrue("Timestamp should be after start time", timestamp!! >= startTime)
        assertTrue("Timestamp should be reasonable", timestamp <= System.currentTimeMillis() + 1000)
    }
}