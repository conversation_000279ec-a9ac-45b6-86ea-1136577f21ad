package soly.lyricsgenerator.features.update.domain.usecase

import android.content.Context
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository

class CheckForUpdateUseCaseTest {

    @Mock
    private lateinit var mockUpdateRepository: UpdateRepository
    
    @Mock
    private lateinit var mockContext: Context
    
    private lateinit var useCase: CheckForUpdateUseCase

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        useCase = CheckForUpdateUseCase(mockUpdateRepository, mockContext)
    }

    @Test
    fun `invoke should return NoUpdate when fetchAndActivate throws exception`() = runTest {
        // Given
        `when`(mockUpdateRepository.fetchAndActivate()).thenThrow(RuntimeException("Network error"))

        // When
        val result = useCase.invoke()

        // Then
        assertTrue("Expected NoUpdate when exception occurs", result is UpdateState.NoUpdate)
    }

    @Test
    fun `invoke should return NoUpdate when repository method throws exception`() = runTest {
        // Given
        `when`(mockUpdateRepository.fetchAndActivate()).then { }
        `when`(mockUpdateRepository.isForceUpdateRequired()).thenThrow(RuntimeException("Remote config error"))

        // When
        val result = useCase.invoke()

        // Then
        assertTrue("Expected NoUpdate when repository throws exception", result is UpdateState.NoUpdate)
    }

    @Test
    fun `invoke should return NoUpdate when version utils throws exception`() = runTest {
        // Given
        `when`(mockUpdateRepository.fetchAndActivate()).then { }
        `when`(mockUpdateRepository.isForceUpdateRequired()).thenReturn(false)
        `when`(mockUpdateRepository.getForceUpdateVersion()).thenReturn("2.0.51")
        `when`(mockUpdateRepository.getStoreAppVersion()).thenReturn("2.0.51")

        // When - VersionUtils.getCurrentAppVersion will throw exception due to mock context
        val result = useCase.invoke()

        // Then
        assertTrue("Expected NoUpdate when version utils throws exception", result is UpdateState.NoUpdate)
    }

    @Test
    fun `invoke should handle repository calls correctly for error scenarios`() = runTest {
        // Given
        `when`(mockUpdateRepository.fetchAndActivate()).then { }
        `when`(mockUpdateRepository.isForceUpdateRequired()).thenReturn(true)
        `when`(mockUpdateRepository.getForceUpdateVersion()).thenThrow(RuntimeException("Config error"))

        // When
        val result = useCase.invoke()

        // Then
        assertTrue("Expected NoUpdate when config access fails", result is UpdateState.NoUpdate)
    }

    @Test
    fun `invoke should handle store version access error gracefully`() = runTest {
        // Given
        `when`(mockUpdateRepository.fetchAndActivate()).then { }
        `when`(mockUpdateRepository.isForceUpdateRequired()).thenReturn(false)
        `when`(mockUpdateRepository.getForceUpdateVersion()).thenReturn("2.0.50")
        `when`(mockUpdateRepository.getStoreAppVersion()).thenThrow(RuntimeException("Store version error"))

        // When
        val result = useCase.invoke()

        // Then
        assertTrue("Expected NoUpdate when store version access fails", result is UpdateState.NoUpdate)
    }
}