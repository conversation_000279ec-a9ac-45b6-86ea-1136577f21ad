package soly.lyricsgenerator.features.update.data.repository

import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import soly.lyricsgenerator.features.update.data.constants.RemoteConfigConstants

class UpdateRepositoryImplTest {

    @Mock
    private lateinit var mockRemoteConfig: FirebaseRemoteConfig

    private lateinit var repository: UpdateRepositoryImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        repository = UpdateRepositoryImpl(mockRemoteConfig)
    }

    @Test
    fun `fetchAndActivate should call remoteConfig fetchAndActivate`() = runTest {
        // Given
        val mockTask: Task<Boolean> = Tasks.forResult(true)
        `when`(mockRemoteConfig.fetchAndActivate()).thenReturn(mockTask)

        // When
        repository.fetchAndActivate()

        // Then
        verify(mockRemoteConfig).fetchAndActivate()
    }

    @Test
    fun `isForceUpdateRequired should return true when remote config has force update enabled`() {
        // Given
        `when`(mockRemoteConfig.getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)).thenReturn(true)

        // When
        val result = repository.isForceUpdateRequired()

        // Then
        assertTrue(result)
        verify(mockRemoteConfig).getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)
    }

    @Test
    fun `isForceUpdateRequired should return false when remote config has force update disabled`() {
        // Given
        `when`(mockRemoteConfig.getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)).thenReturn(false)

        // When
        val result = repository.isForceUpdateRequired()

        // Then
        assertFalse(result)
        verify(mockRemoteConfig).getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)
    }

    @Test
    fun `getForceUpdateVersion should return version from remote config`() {
        // Given
        val expectedVersion = "2.1.0"
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)).thenReturn(expectedVersion)

        // When
        val result = repository.getForceUpdateVersion()

        // Then
        assertEquals(expectedVersion, result)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)
    }

    @Test
    fun `getForceUpdateVersion should return empty string when remote config returns empty`() {
        // Given
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)).thenReturn("")

        // When
        val result = repository.getForceUpdateVersion()

        // Then
        assertEquals("", result)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)
    }

    @Test
    fun `getStoreAppVersion should return version from remote config`() {
        // Given
        val expectedVersion = "2.0.51"
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.STORE_APP_VERSION)).thenReturn(expectedVersion)

        // When
        val result = repository.getStoreAppVersion()

        // Then
        assertEquals(expectedVersion, result)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.STORE_APP_VERSION)
    }

    @Test
    fun `getStoreAppVersion should return empty string when remote config returns empty`() {
        // Given
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.STORE_APP_VERSION)).thenReturn("")

        // When
        val result = repository.getStoreAppVersion()

        // Then
        assertEquals("", result)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.STORE_APP_VERSION)
    }

    @Test
    fun `repository should use correct remote config constants`() {
        // Given
        `when`(mockRemoteConfig.getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)).thenReturn(false)
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)).thenReturn("test")
        `when`(mockRemoteConfig.getString(RemoteConfigConstants.STORE_APP_VERSION)).thenReturn("test")

        // When
        repository.isForceUpdateRequired()
        repository.getForceUpdateVersion()
        repository.getStoreAppVersion()

        // Then
        verify(mockRemoteConfig).getBoolean(RemoteConfigConstants.FORCE_UPDATE_REQUIRED)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.FORCE_UPDATE_VERSION)
        verify(mockRemoteConfig).getString(RemoteConfigConstants.STORE_APP_VERSION)
    }
}