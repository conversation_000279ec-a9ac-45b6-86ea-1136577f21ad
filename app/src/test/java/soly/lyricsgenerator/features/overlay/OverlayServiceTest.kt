package soly.lyricsgenerator.features.overlay

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import soly.lyricsgenerator.domain.model.Song
import soly.lyricsgenerator.features.overlay.data.services.OverlayService
import soly.lyricsgenerator.features.overlay.domain.models.OverlaySettings

/**
 * Unit tests for OverlayService to ensure the redesigned UI maintains all functionality
 */
@RunWith(MockitoJUnitRunner::class)
class OverlayServiceTest {

    private lateinit var overlayService: OverlayService

    @Before
    fun setUp() {
        overlayService = OverlayService()
    }

    @Test
    fun `test overlay service state flows initialization`() = runTest {
        // Verify initial state
        assertNull(overlayService.currentSong.first())
        assertTrue(overlayService.currentLyrics.first().isEmpty())
        assertEquals(0L, overlayService.currentPosition.first())
    }

    @Test
    fun `test overlay service song state update`() = runTest {
        // Create test song
        val testSong = Song(
            id = 1,
            title = "Test Song",
            artist = "Test Artist",
            data = "/test/path",
            duration = 180000,
            isFavorite = false
        )

        // Update song state
        overlayService._currentSong.value = testSong

        // Verify state update
        val currentSong = overlayService.currentSong.first()
        assertNotNull(currentSong)
        assertEquals("Test Song", currentSong?.title)
        assertEquals("Test Artist", currentSong?.artist)
    }

    @Test
    fun `test overlay service lyrics state update`() = runTest {
        // Create test lyrics
        val testLyrics = mapOf(
            0 to "First line",
            5000 to "Second line",
            10000 to "Third line"
        )

        // Update lyrics state
        overlayService._currentLyrics.value = testLyrics

        // Verify state update
        val currentLyrics = overlayService.currentLyrics.first()
        assertEquals(3, currentLyrics.size)
        assertEquals("First line", currentLyrics[0])
        assertEquals("Second line", currentLyrics[5000])
        assertEquals("Third line", currentLyrics[10000])
    }

    @Test
    fun `test overlay service position state update`() = runTest {
        // Update position state
        val testPosition = 7500L
        overlayService._currentPosition.value = testPosition

        // Verify state update
        assertEquals(testPosition, overlayService.currentPosition.first())
    }

    @Test
    fun `test overlay settings data class`() {
        // Test default overlay settings
        val defaultSettings = OverlaySettings()
        assertEquals(1.0f, defaultSettings.transparency, 0.01f)
        assertEquals(16f, defaultSettings.fontSize, 0.01f)
        assertEquals(0xFFFFFFFF, defaultSettings.fontColor)
        assertEquals(0xFF000000, defaultSettings.backgroundColor)

        // Test custom overlay settings
        val customSettings = OverlaySettings(
            transparency = 0.8f,
            fontSize = 18f,
            fontColor = 0xFF00FF00,
            backgroundColor = 0xFF123456
        )
        assertEquals(0.8f, customSettings.transparency, 0.01f)
        assertEquals(18f, customSettings.fontSize, 0.01f)
        assertEquals(0xFF00FF00, customSettings.fontColor)
        assertEquals(0xFF123456, customSettings.backgroundColor)
    }

    @Test
    fun `test overlay service handles empty lyrics gracefully`() = runTest {
        // Set empty lyrics
        overlayService._currentLyrics.value = emptyMap()

        // Verify empty state
        val currentLyrics = overlayService.currentLyrics.first()
        assertTrue(currentLyrics.isEmpty())
    }

    @Test
    fun `test overlay service handles null song gracefully`() = runTest {
        // Set null song
        overlayService._currentSong.value = null

        // Verify null state
        assertNull(overlayService.currentSong.first())
    }

    @Test
    fun `test overlay service state consistency`() = runTest {
        // Create test data
        val testSong = Song(
            id = 1,
            title = "Consistency Test",
            artist = "Test Artist",
            data = "/test/path",
            duration = 240000,
            isFavorite = true
        )
        val testLyrics = mapOf(
            0 to "Line 1",
            3000 to "Line 2"
        )
        val testPosition = 1500L

        // Update all states
        overlayService._currentSong.value = testSong
        overlayService._currentLyrics.value = testLyrics
        overlayService._currentPosition.value = testPosition

        // Verify all states are consistent
        assertEquals(testSong, overlayService.currentSong.first())
        assertEquals(testLyrics, overlayService.currentLyrics.first())
        assertEquals(testPosition, overlayService.currentPosition.first())
    }
}