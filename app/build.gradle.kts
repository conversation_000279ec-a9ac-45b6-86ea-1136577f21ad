plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    id("com.google.devtools.ksp")
    id("dagger.hilt.android.plugin")
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
}

android {
    namespace = "soly.lyricsgenerator"
    compileSdk = 35

    defaultConfig {
        applicationId = "soly.lyricsgenerator"
        minSdk = 26
        targetSdk = 35
        versionCode = 62
        versionName = "2.0.52"

        testInstrumentationRunner = "soly.lyricsgenerator.HiltTestRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
        
        // Add ABI filters for FFmpeg
        ndk {
            abiFilters += listOf("armeabi-v7a", "arm64-v8a", "x86", "x86_64")
        }

        defaultConfig {
            testInstrumentationRunnerArguments["clearPackageData"] = "true"
            testInstrumentationRunnerArguments["useOrchestrator"] = "true"
        }

        testInstrumentationRunnerArguments["class"] = listOf(
            "soly.lyricsgenerator.MainActivityBasicUITest",
            "soly.lyricsgenerator.ui.screens.create_screen.PasteLyricsSeekControlsTest",
            "soly.lyricsgenerator.ui.screens.create_screen.SyncDataReloadTest",
            "soly.lyricsgenerator.ui.screens.create_screen.UndoTimestampsTest",
            "soly.lyricsgenerator.ui.screens.create_screen.CreateScreenLrcImportTest",
            "soly.lyricsgenerator.ui.screens.create_screen.CreateScreenFullWorkflowTest",
            "soly.lyricsgenerator.ui.screens.RealSortFunctionalTest",
            "soly.lyricsgenerator.ui.screens.ArtistFilterTest",
            "soly.lyricsgenerator.features.shared_audio.SharedAudioIntegrationTest"
        ).joinToString(",")
    }

    buildTypes {
        debug {
//            applicationIdSuffix = ".debug"
            buildConfigField("String", "OPENAI_API_KEY", "\"${System.getenv("OPENAI_API_KEY") ?: ""}\"")
            buildConfigField("String", "AMPLITUDE_API_KEY", "\"${System.getenv("AMPLITUDE_API_KEY") ?: "dd6fe754df24b6d922b7e576e5dec660"}\"")
            buildConfigField("boolean", "ENABLE_LYRICS_SECTION", "false")
        }
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            buildConfigField("String", "OPENAI_API_KEY", "\"${System.getenv("OPENAI_API_KEY") ?: ""}\"")
            buildConfigField("String", "AMPLITUDE_API_KEY", "\"${System.getenv("AMPLITUDE_API_KEY") ?: "dd6fe754df24b6d922b7e576e5dec660"}\"")
            buildConfigField("boolean", "ENABLE_LYRICS_SECTION", "false")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.11"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    implementation("androidx.media:media:1.6.0")
    implementation("com.aallam.openai:openai-client:3.8.2")
    implementation("io.ktor:ktor-client-okhttp:2.3.4")
    implementation("androidx.appcompat:appcompat:1.4.1")
    implementation("androidx.core:core-ktx:1.7.0")
    implementation("androidx.activity:activity-ktx:1.4.0")
    implementation("androidx.fragment:fragment-ktx:1.4.1")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.4.1")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.1")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.4.1")
    implementation("androidx.lifecycle:lifecycle-viewmodel-savedstate:2.4.1")
    implementation("androidx.navigation:navigation-compose:2.6.0")
    implementation("androidx.hilt:hilt-navigation-compose:1.0.0")
    implementation("com.google.dagger:hilt-android:2.48")
    ksp("com.google.dagger:hilt-android-compiler:2.48")
    implementation(libs.hilt.navigation.compose)

    // FFmpeg for audio metadata processing - using mobile-ffmpeg 4.4.LTS AAR from libs/
    
    // Include all AAR files from libs directory (for potential future use)
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))

    // Timber for logging
    implementation("com.jakewharton.timber:timber:5.0.1")
    
    // Scrollbar for LazyColumn
    implementation("com.github.nanihadesuka:LazyColumnScrollbar:2.2.0")
    
    // JAudioTagger for direct audio file metadata editing
    implementation("com.github.Adonai:jaudiotagger:2.3.15")

    implementation(libs.androidx.room.runtime)
    ksp(libs.androidx.room.compiler)
    implementation(libs.androidx.room.ktx)

    implementation(libs.androidx.material.icons.core)
    implementation(libs.material.icons.extended)
    implementation(libs.androidx.navigation.compose)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.runtime.compose)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.accompanist.swiperefresh)

    // Firebase
    implementation(platform("com.google.firebase:firebase-bom:33.1.1"))
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.google.firebase:firebase-crashlytics")
    implementation(libs.firebase.config.ktx)
    
    // AndroidX Security for EncryptedSharedPreferences
    implementation("androidx.security:security-crypto:1.1.0-alpha03")
    
    // Amplitude Analytics
    implementation("com.amplitude:analytics-android:1.16.8")

    testImplementation(libs.junit)
    testImplementation("org.mockito:mockito-core:5.1.1")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.1.0")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation("androidx.test.espresso:espresso-intents:3.6.1")
    androidTestImplementation("androidx.test:core:1.5.0")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    androidTestImplementation("androidx.test:rules:1.5.0")
    androidTestImplementation("com.google.dagger:hilt-android-testing:2.48")
    kspAndroidTest("com.google.dagger:hilt-compiler:2.48")
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}
