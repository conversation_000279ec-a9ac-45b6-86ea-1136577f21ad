# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Keep line numbers for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Keep annotation attributes for runtime processing
-keepattributes *Annotation*

# Keep generic signatures for reflection
-keepattributes Signature

# Keep inner classes
-keepattributes InnerClasses

# Keep enum classes
-keepattributes Enum

# Jetpack Compose specific rules
-keep class androidx.compose.** { *; }
-keep interface androidx.compose.** { *; }
-dontwarn androidx.compose.**

# Room database rules
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *
-keepclassmembers class * {
  @androidx.room.* <methods>;
}

# Hilt dependency injection rules  
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class * extends dagger.hilt.android.HiltAndroidApp
-keepclasseswithmembers class * {
    @dagger.hilt.android.AndroidEntryPoint <methods>;
}

# Firebase rules
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Firebase Crashlytics specific rules
-keep class com.google.firebase.crashlytics.** { *; }
-keep interface com.google.firebase.crashlytics.** { *; }
-dontwarn com.google.firebase.crashlytics.**

# Keep crash reporting data structures
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception

# Keep Firebase Crashlytics SDK classes
-keep class com.google.firebase.crashlytics.internal.** { *; }
-keep class com.google.firebase.crashlytics.FirebaseCrashlytics { *; }
-keep class com.google.firebase.crashlytics.FirebaseCrashlytics$* { *; }

# AndroidX Security (EncryptedSharedPreferences) rules
-keep class androidx.security.crypto.** { *; }
-keep interface androidx.security.crypto.** { *; }
-dontwarn androidx.security.crypto.**

# Keep EncryptedSharedPreferences specific classes
-keep class androidx.security.crypto.EncryptedSharedPreferences { *; }
-keep class androidx.security.crypto.MasterKey { *; }
-keep class androidx.security.crypto.MasterKey$* { *; }

# Amplitude Analytics rules
-keep class com.amplitude.** { *; }
-keep interface com.amplitude.** { *; }
-dontwarn com.amplitude.**

# Keep Amplitude SDK specific classes
-keep class com.amplitude.android.Amplitude { *; }
-keep class com.amplitude.android.Configuration { *; }
-keep class com.amplitude.android.Configuration$* { *; }

# Keep UserIdentityManager and related analytics classes
-keep class soly.lyricsgenerator.domain.service.UserIdentityManager { *; }
-keep class soly.lyricsgenerator.analytics.** { *; }

# Keep all model classes (data classes)
-keep class soly.lyricsgenerator.domain.model.** { *; }

# Keep ViewModels
-keep class soly.lyricsgenerator.ui.viewmodel.** { *; }

# Keep Navigation classes
-keep class androidx.navigation.** { *; }

# Timber logging rules
-keep class timber.log.** { *; }
-dontwarn timber.log.**

# SLF4J logging framework rules
-dontwarn org.slf4j.**
-dontwarn org.slf4j.impl.StaticLoggerBinder

# Media/Audio related classes
-keep class androidx.media.** { *; }
-keep class android.media.** { *; }

# Mobile FFmpeg ProGuard rules
-keep class com.arthenica.mobileffmpeg.** { *; }
-keep class com.arthenica.mobileffmpeg.Config {
    native <methods>;
    void log(long, int, byte[]);
    void statistics(long, int, float, float, long , int, double, double);
}
-dontwarn com.arthenica.mobileffmpeg.**

# JAudioTagger ProGuard rules
-keep class org.jaudiotagger.** { *; }
-keep interface org.jaudiotagger.** { *; }
-dontwarn org.jaudiotagger.**

# Keep JAudioTagger specific classes for reflection
-keep class org.jaudiotagger.audio.AudioFileIO { *; }
-keep class org.jaudiotagger.audio.AudioFile { *; }
-keep class org.jaudiotagger.tag.** { *; }
-keep class org.jaudiotagger.audio.exceptions.** { *; }

# Keep JAudioTagger native implementations
-keepclasseswithmembers class org.jaudiotagger.** {
    native <methods>;
}

# Prevent obfuscation of JAudioTagger enums and field keys
-keepclassmembers enum org.jaudiotagger.** {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Retrofit and OkHttp (if used for API calls)
-keep class retrofit2.** { *; }
-keep class okhttp3.** { *; }
-dontwarn retrofit2.**
-dontwarn okhttp3.**

# Keep classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep public classes and methods for external access
-keep public class soly.lyricsgenerator.MainActivity { *; }
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Optimize and obfuscate while preserving functionality
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove Timber logging in release builds  
-assumenosideeffects class timber.log.Timber* {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# Keep custom exceptions for better crash reporting
-keep public class * extends java.lang.Exception

# Keep BuildConfig class
-keep class soly.lyricsgenerator.BuildConfig { *; }

# Additional security - obfuscate package names
-repackageclasses ''