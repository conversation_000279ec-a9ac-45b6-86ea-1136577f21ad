# LyricsGenerator

An Android application for creating and managing synchronized lyrics (LRC) files for your music collection.

![Android CI](https://github.com/Gerrix90/lyrics-generator-android/actions/workflows/android-ci.yml/badge.svg)

## Features

- 🎵 **Music Player Integration**: Browse and play local music files from your device
- ✍️ **LRC File Creation**: Create synchronized lyrics files with precise timestamps
- 📁 **File Management**: Import, view, and manage existing LRC files
- 🎤 **Real-time Sync**: View synchronized lyrics during music playback
- 🌍 **Multi-language Support**: Available in 10 languages (English, Spanish, French, German, Italian, Portuguese, Russian, Serbian, Turkish, Polish)
- 🎨 **Modern UI**: Built with Jetpack Compose and Material Design 3

## Screenshots

<details>
<summary>View Screenshots</summary>

- Music Library Screen
- Create LRC Screen
- Lyrics Display
- File Management

</details>

## Requirements

- Android 7.0 (API level 26) or higher
- Storage permission for accessing music files

## Installation

### From Release

1. Download the latest APK from the [Releases](https://github.com/Gerrix90/lyrics-generator-android/releases) page
2. Enable "Install from unknown sources" in your Android settings
3. Install the APK

### Build from Source

```bash
# Clone the repository
git clone https://github.com/Gerrix90/lyrics-generator-android.git
cd lyrics-generator-android

# Build debug APK
./gradlew assembleDebug

# Install on connected device
./gradlew installDebug
```

## Usage

1. **Browse Music**: Navigate to the Music tab to see all songs on your device
2. **Create LRC**: 
   - Select a song from your library
   - Tap "Create" to start the LRC creation process
   - Play the song and tap each line as it's sung to timestamp it
   - Save the LRC file when complete
3. **View Lyrics**: Select any song with an associated LRC file to view synchronized lyrics
4. **Import LRC**: Use the Files tab to import existing LRC files

## Development

### Prerequisites

- Java JDK 17
- Android Studio (recommended)
- Android SDK with API level 34

### Architecture

The app follows Clean Architecture principles with MVVM pattern:

- **Presentation Layer**: Jetpack Compose UI with ViewModels
- **Domain Layer**: Use cases containing business logic
- **Data Layer**: Room database and file repositories

### Key Technologies

- Jetpack Compose for UI
- Hilt for dependency injection
- Room for local database
- Kotlin Coroutines and Flow
- Firebase Analytics & Crashlytics
- Amplitude Analytics
- AndroidX Security (EncryptedSharedPreferences)

### User Analytics & Privacy

This app implements a privacy-friendly analytics system for apps without user login:

#### UUID Generation for Analytics
- **Purpose**: Unifies user identification across Firebase Analytics, Firebase Crashlytics, and Amplitude without requiring login
- **Privacy**: UUID contains no PII and is GDPR-friendly
- **Generation**: `UUID.randomUUID()` generated on first app launch
- **Storage**: Encrypted using AndroidX Security library's `EncryptedSharedPreferences` with AES256_GCM encryption
- **Persistence**: UUID remains consistent across app sessions until uninstall/reinstall

#### Analytics Platforms Integration
The UUID is automatically propagated to:
- **Firebase Analytics**: `setUserId(uuid)` - for event tracking and user journeys
- **Firebase Crashlytics**: `setUserId(uuid)` - for crash correlation and user impact analysis  
- **Amplitude**: `setUserId(uuid)` - for advanced user behavior analytics

#### Implementation Details
- **UserIdentityManager**: Encapsulates all UUID logic following Clean Architecture patterns
- **Initialization**: Runs in `Application.onCreate()` before any analytics events
- **Security**: Uses AndroidX Security with MasterKey.KeyScheme.AES256_GCM encryption
- **Error Handling**: Comprehensive error handling with fallback UUID generation
- **Testing**: Unit tests validate UUID generation, constants, and error handling

#### Privacy & Security Features
- **No PII**: UUID contains no personally identifiable information
- **Encrypted Storage**: Uses military-grade AES256_GCM encryption for local storage
- **GDPR Compliant**: No personal data collection, only anonymous installation tracking
- **User Control**: UUID is reset on app uninstall/reinstall (user can control anonymity)

#### References
- [EncryptedSharedPreferences Documentation](https://developer.android.com/reference/androidx/security/crypto/EncryptedSharedPreferences)
- [UUID Documentation](https://developer.android.com/reference/java/util/UUID)
- [Firebase Analytics setUserId](https://firebase.google.com/docs/analytics/userid)
- [Amplitude setUserId](https://amplitude.com/docs/sdks/analytics/android/android-kotlin-sdk)

### Building

```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease

# Run tests
./gradlew test

# Run Android tests
./gradlew connectedAndroidTest
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Convention

This project uses conventional commits:
- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test additions or modifications
- `chore:` Maintenance tasks

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [Jetpack Compose](https://developer.android.com/jetpack/compose)
- Icons from [Material Design Icons](https://material.io/resources/icons/)
- LRC file format specification from [LRC Wikipedia](https://en.wikipedia.org/wiki/LRC_(file_format))

## Support

For issues and feature requests, please use the [GitHub Issues](https://github.com/Gerrix90/lyrics-generator-android/issues) page.