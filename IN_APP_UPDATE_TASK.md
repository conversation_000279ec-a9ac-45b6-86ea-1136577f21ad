# **Task: Implement In-App Update System via Firebase Remote Config**

## 1. Goal

Implement a system for soft and force application updates using Firebase Remote Config. The check will occur at application startup within `MainActivity`, without a dedicated splash screen, and will follow the project's established Clean Architecture and MVVM patterns.

## 2. Firebase and Project Configuration

### 2.1. Firebase Console Setup

(This part remains the same; ensure these parameters are configured in your Firebase project's Remote Config.)

1.  **`update_url`** (String): The Play Store URL for the app.
    *   _Example_: `https://play.google.com/store/apps/details?id=soly.lyricsgenerator`
2.  **`store_app_version`** (String): The latest version available on the Play Store (for soft updates).
3.  **`force_update_required`** (<PERSON>olean): Master switch to enable force updates.
4.  **`force_update_version`** (String): The minimum required version for the app to run.

### 2.2. Add Dependencies

Modify `gradle/libs.versions.toml` to add the Firebase Remote Config library version:
```toml
# gradle/libs.versions.toml

// ... existing versions
firebase-config = "21.6.3"
```

Then, add the library to `app/build.gradle.kts`:
```kotlin
// app/build.gradle.kts

// ...
dependencies {
    // ... existing dependencies
    implementation(libs.firebase.config.ktx)
}
```

### 2.3. Create Default Values XML

Create the file `app/src/main/res/xml/remote_config_defaults.xml`. This ensures the app has fallback values before fetching from Firebase.

```xml
<!-- app/src/main/res/xml/remote_config_defaults.xml -->
<?xml version="1.0" encoding="utf-8"?>
<defaultsMap>
    <entry>
        <key>force_update_required</key>
        <value>false</value>
    </entry>
    <entry>
        <key>force_update_version</key>
        <value>1.0.0</value>
    </entry>
    <entry>
        <key>store_app_version</key>
        <value>1.0.0</value>
    </entry>
    <entry>
        <key>update_url</key>
        <value>https://play.google.com/store/apps/details?id=soly.lyricsgenerator</value>
    </entry>
</defaultsMap>
```

### 2.4. Add Required Strings

Add the following string resources to `app/src/main/res/values/strings.xml`:
```xml
<!-- app/src/main/res/values/strings.xml -->
<resources>
    <!-- ... existing strings -->
    <string name="update_required_title">Update Required</string>
    <string name="update_required_message">To continue using the app, you must update to the latest version.</string>
    <string name="update_now">Update Now</string>
    <string name="update_available_title">Update Available</string>
    <string name="update_available_message">A new version of the app is available. Would you like to install it now?</string>
    <string name="update_later">Later</string>
    <string name="error_play_store">Could not open Play Store.</string>
</resources>
```

## 3. Core Logic Implementation (Clean Architecture)

We will create a new feature package: `app/src/main/java/soly/lyricsgenerator/features/update/`.

### 3.1. Create `VersionUtils.kt`

In the `app/src/main/java/soly/lyricsgenerator/utils/` package, create the following utility object:
```kotlin
// app/src/main/java/soly/lyricsgenerator/utils/VersionUtils.kt
package soly.lyricsgenerator.utils

import android.content.Context
import android.content.pm.PackageManager

object VersionUtils {
    fun getCurrentAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            "1.0.0" // Fallback
        }
    }

    fun isUpdateNeeded(currentVersion: String, requiredVersion: String): Boolean {
        val currentParts = currentVersion.split('.').map { it.toIntOrNull() ?: 0 }
        val requiredParts = requiredVersion.split('.').map { it.toIntOrNull() ?: 0 }
        val length = maxOf(currentParts.size, requiredParts.size)
        
        for (i in 0 until length) {
            val current = currentParts.getOrElse(i) { 0 }
            val required = requiredParts.getOrElse(i) { 0 }
            if (current < required) return true
            if (current > required) return false
        }
        return false
    }
}
```

### 3.2. Define Data Models

Create the file `app/src/main/java/soly/lyricsgenerator/features/update/domain/UpdateState.kt`:
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/domain/UpdateState.kt
package soly.lyricsgenerator.features.update.domain

sealed class UpdateState {
    object Loading : UpdateState()
    object NoUpdate : UpdateState()
    data class SoftUpdate(val newVersion: String) : UpdateState()
    data class ForceUpdate(val newVersion: String) : UpdateState()
}
```

### 3.3. Create Repository Layer

Create the repository interface and its implementation.

**`UpdateRepository.kt`**
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/domain/repository/UpdateRepository.kt
package soly.lyricsgenerator.features.update.domain.repository

interface UpdateRepository {
    suspend fun fetchAndActivate()
    fun isForceUpdateRequired(): Boolean
    fun getForceUpdateVersion(): String
    fun getStoreAppVersion(): String
    fun getUpdateUrl(): String
}
```

**`UpdateRepositoryImpl.kt`**
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/data/repository/UpdateRepositoryImpl.kt
package soly.lyricsgenerator.features.update.data.repository

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import kotlinx.coroutines.tasks.await
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import javax.inject.Inject

class UpdateRepositoryImpl @Inject constructor(
    private val remoteConfig: FirebaseRemoteConfig
) : UpdateRepository {

    override suspend fun fetchAndActivate() {
        remoteConfig.fetchAndActivate().await()
    }

    override fun isForceUpdateRequired(): Boolean = remoteConfig.getBoolean("force_update_required")
    override fun getForceUpdateVersion(): String = remoteConfig.getString("force_update_version")
    override fun getStoreAppVersion(): String = remoteConfig.getString("store_app_version")
    override fun getUpdateUrl(): String = remoteConfig.getString("update_url")
}
```

### 3.4. Create Use Case

Create the use case to encapsulate the update check logic.

**`CheckForUpdateUseCase.kt`**
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/domain/usecase/CheckForUpdateUseCase.kt
package soly.lyricsgenerator.features.update.domain.usecase

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import soly.lyricsgenerator.utils.VersionUtils
import javax.inject.Inject

class CheckForUpdateUseCase @Inject constructor(
    private val updateRepository: UpdateRepository,
    @ApplicationContext private val context: Context
) {
    suspend operator fun invoke(): UpdateState {
        updateRepository.fetchAndActivate()

        val currentAppVersion = VersionUtils.getCurrentAppVersion(context)
        val forceUpdateRequired = updateRepository.isForceUpdateRequired()
        val forceUpdateVersion = updateRepository.getForceUpdateVersion()
        val storeAppVersion = updateRepository.getStoreAppVersion()

        return when {
            forceUpdateRequired && VersionUtils.isUpdateNeeded(currentAppVersion, forceUpdateVersion) -> {
                UpdateState.ForceUpdate(forceUpdateVersion)
            }
            VersionUtils.isUpdateNeeded(currentAppVersion, storeAppVersion) -> {
                UpdateState.SoftUpdate(storeAppVersion)
            }
            else -> {
                UpdateState.NoUpdate
            }
        }
    }
}
```

### 3.5. Setup Dependency Injection

Create a new Hilt module to provide the necessary dependencies for the update feature.

**`UpdateModule.kt`**
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/di/UpdateModule.kt
package soly.lyricsgenerator.features.update.di

import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import soly.lyricsgenerator.R
import soly.lyricsgenerator.features.update.data.repository.UpdateRepositoryImpl
import soly.lyricsgenerator.features.update.domain.repository.UpdateRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object UpdateModule {

    @Provides
    @Singleton
    fun provideRemoteConfig(): FirebaseRemoteConfig {
        val remoteConfig = Firebase.remoteConfig
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = 3600 // Cache for 1 hour
        }
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        return remoteConfig
    }

    @Provides
    @Singleton
    fun provideUpdateRepository(remoteConfig: FirebaseRemoteConfig): UpdateRepository {
        return UpdateRepositoryImpl(remoteConfig)
    }
}
```

### 3.6. Create ViewModel

Create the `ViewModel` to connect the UI with the business logic.

**`UpdateViewModel.kt`**
```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/presentation/UpdateViewModel.kt
package soly.lyricsgenerator.features.update.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.domain.usecase.CheckForUpdateUseCase
import javax.inject.Inject

@HiltViewModel
class UpdateViewModel @Inject constructor(
    private val checkForUpdateUseCase: CheckForUpdateUseCase
) : ViewModel() {

    private val _updateState = MutableStateFlow<UpdateState>(UpdateState.Loading)
    val updateState: StateFlow<UpdateState> = _updateState

    init {
        checkForUpdates()
    }

    private fun checkForUpdates() {
        viewModelScope.launch {
            try {
                _updateState.value = checkForUpdateUseCase()
            } catch (e: Exception) {
                // In case of error (e.g., no network), proceed to the app
                _updateState.value = UpdateState.NoUpdate
            }
        }
    }

    fun dismissSoftUpdate() {
        _updateState.value = UpdateState.NoUpdate
    }
}
```

## 4. UI Implementation and Integration

### 4.1. Create `ForceUpdateScreen.kt`

This screen will block the UI until the user updates.

```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/presentation/ForceUpdateScreen.kt
package soly.lyricsgenerator.features.update.presentation

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SystemUpdate
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import soly.lyricsgenerator.R

@Composable
fun ForceUpdateScreen(onUpdateClick: () -> Unit) {
    BackHandler(enabled = true) { /* Block back navigation */ }

    Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.SystemUpdate,
                contentDescription = null,
                modifier = Modifier.size(100.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(24.dp))
            Text(
                text = stringResource(id = R.string.update_required_title),
                style = MaterialTheme.typography.headlineSmall,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = stringResource(id = R.string.update_required_message),
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(32.dp))
            Button(
                onClick = onUpdateClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(text = stringResource(id = R.string.update_now))
            }
        }
    }
}
```

### 4.2. Create `SoftUpdateDialog.kt`

This dialog will prompt the user for an optional update.

```kotlin
// app/src/main/java/soly/lyricsgenerator/features/update/presentation/SoftUpdateDialog.kt
package soly.lyricsgenerator.features.update.presentation

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import soly.lyricsgenerator.R

@Composable
fun SoftUpdateDialog(
    onUpdateClick: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(text = stringResource(id = R.string.update_available_title)) },
        text = { Text(text = stringResource(id = R.string.update_available_message)) },
        confirmButton = {
            TextButton(onClick = onUpdateClick) {
                Text(stringResource(id = R.string.update_now))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(id = R.string.update_later))
            }
        }
    )
}
```

### 4.3. Integrate into `MainActivity.kt`

Modify `MainActivity.kt` to orchestrate the update flow.

```kotlin
// app/src/main/java/soly/lyricsgenerator/MainActivity.kt
package soly.lyricsgenerator

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import dagger.hilt.android.AndroidEntryPoint
import soly.lyricsgenerator.features.update.domain.UpdateState
import soly.lyricsgenerator.features.update.presentation.ForceUpdateScreen
import soly.lyricsgenerator.features.update.presentation.SoftUpdateDialog
import soly.lyricsgenerator.features.update.presentation.UpdateViewModel
import soly.lyricsgenerator.ui.navigation.NavGraph
import soly.lyricsgenerator.ui.theme.LyricsGeneratorTheme

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val updateViewModel: UpdateViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            LyricsGeneratorTheme {
                val updateState by updateViewModel.updateState.collectAsState()
                var showSoftUpdateDialog by remember(updateState) {
                    mutableStateOf(updateState is UpdateState.SoftUpdate)
                }

                when (updateState) {
                    is UpdateState.Loading -> {
                        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                            CircularProgressIndicator()
                        }
                    }
                    is UpdateState.ForceUpdate -> {
                        ForceUpdateScreen(onUpdateClick = ::openPlayStore)
                    }
                    else -> {
                        // For NoUpdate or SoftUpdate, show the main app
                        NavGraph()

                        if (showSoftUpdateDialog) {
                            SoftUpdateDialog(
                                onUpdateClick = {
                                    openPlayStore()
                                    showSoftUpdateDialog = false // Dismiss dialog
                                },
                                onDismiss = { showSoftUpdateDialog = false }
                            )
                        }
                    }
                }
            }
        }
    }

    private fun openPlayStore() {
        // This is a simplified way to get the URL. For a more robust solution,
        // the URL should be part of the UpdateState or retrieved from the repository.
        val updateUrl = "https://play.google.com/store/apps/details?id=$packageName"
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(updateUrl)).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, R.string.error_play_store, Toast.LENGTH_SHORT).show()
        }
    }
} 